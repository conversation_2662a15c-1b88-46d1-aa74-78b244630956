# 社区健康管理系统 - 前端

基于 Vue 3 + Vite + Pinia 构建的社区健康管理系统前端应用。

## 已完成功能 (TODO 1)

### ✅ 用户认证系统
- **路由配置**: 已配置 `/login` 和 `/register` 页面路由
- **状态管理**: 使用 Pinia 创建了 `userStore`，管理用户信息和 JWT Token
- **登录注册页面**:
  - `Login.vue` - 用户登录页面，包含表单验证
  - `Register.vue` - 用户注册页面，支持居民和医生角色注册
- **API 封装**: 在 `api/user.js` 中封装了登录和注册的 axios 请求
- **路由守卫**: 实现了全局前置路由守卫，保护需要登录的页面
- **Axios 拦截器**: 自动为请求添加 Authorization 头，处理 token 过期

### 🏗️ 项目结构
```
src/
├── api/
│   ├── index.js          # Axios 配置和拦截器
│   └── user.js           # 用户相关 API
├── stores/
│   └── user.js           # 用户状态管理
├── views/
│   ├── Login.vue         # 登录页面
│   ├── Register.vue      # 注册页面
│   └── HomeView.vue      # 首页
├── router/
│   └── index.js          # 路由配置和守卫
└── App.vue               # 主应用组件
```

### 🎨 功能特性
- **响应式设计**: 适配桌面和移动端
- **表单验证**: 手机号、密码、昵称等字段验证
- **角色支持**: 支持居民(RESIDENT)、医生(DOCTOR)、管理员(ADMIN)角色
- **状态持久化**: Token 和用户信息自动保存到 localStorage
- **自动登出**: Token 过期时自动清除状态并跳转登录页
- **路由保护**: 未登录用户自动重定向到登录页

### 🔧 技术栈
- **Vue 3**: 使用 Composition API
- **Vite**: 快速构建工具
- **Pinia**: 状态管理
- **Vue Router**: 路由管理
- **Axios**: HTTP 请求库

## 开发指南

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 数据库支持

系统支持以下用户角色（基于 `init-mysql.sql`）：
- **RESIDENT**: 居民用户，可管理健康档案、预约医生
- **DOCTOR**: 医生用户，可管理排班、接诊患者
- **ADMIN**: 管理员用户，可管理系统和用户

## 下一步开发

等待后端接口完成后，可以：
1. 连接真实的后端 API
2. 实现具体的业务功能模块
3. 添加更多的页面和组件
4. 完善错误处理和用户体验

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Compile and Minify for Production

```sh
npm run build
```
