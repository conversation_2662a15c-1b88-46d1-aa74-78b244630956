# Unified Content Management API Test Script

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Content Management API Test" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Global variables
$adminToken = ""
$doctorToken = ""
$residentToken = ""
$newsId = ""
$activityId = ""
$guidanceId = ""

# 1. Admin Login
Write-Host "`n1. Admin Login..." -ForegroundColor Yellow
$adminLoginData = '{"phoneNumber":"19999999999","password":"admin123"}'
try {
    $adminResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method POST -ContentType "application/json" -Body $adminLoginData
    $adminToken = $adminResponse.data.token
    Write-Host "✅ Admin login success" -ForegroundColor Green
} catch {
    Write-Host "❌ Admin login failed" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    exit 1
}

# 2. Doctor Login
Write-Host "`n2. Doctor Login..." -ForegroundColor Yellow
$doctorResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method POST -ContentType "application/json" -Body (Get-Content login-doctor.json -Raw)
$doctorToken = $doctorResponse.data.token
Write-Host "✅ Doctor login success" -ForegroundColor Green

# 3. Resident Login
Write-Host "`n3. Resident Login..." -ForegroundColor Yellow
$residentResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method POST -ContentType "application/json" -Body (Get-Content login-resident.json -Raw)
$residentToken = $residentResponse.data.token
Write-Host "✅ Resident login success" -ForegroundColor Green

# 4. Admin Create News Content
Write-Host "`n4. Admin Create News Content..." -ForegroundColor Yellow
$adminHeaders = @{
    'Authorization' = "Bearer $adminToken"
    'Content-Type' = 'application/json'
}

try {
    $newsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/contents" -Method POST -Headers $adminHeaders -Body (Get-Content create-news.json -Raw)
    $newsId = $newsResponse.data.id
    Write-Host "✅ News content created successfully" -ForegroundColor Green
    Write-Host "   News ID: $newsId" -ForegroundColor Cyan
    Write-Host "   Title: $($newsResponse.data.title)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to create news content" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

# 5. Admin Create Activity Content
Write-Host "`n5. Admin Create Activity Content..." -ForegroundColor Yellow
try {
    $activityResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/contents" -Method POST -Headers $adminHeaders -Body (Get-Content create-activity.json -Raw)
    $activityId = $activityResponse.data.id
    Write-Host "✅ Activity content created successfully" -ForegroundColor Green
    Write-Host "   Activity ID: $activityId" -ForegroundColor Cyan
    Write-Host "   Title: $($activityResponse.data.title)" -ForegroundColor Cyan
    Write-Host "   Activity Time: $($activityResponse.data.activityTime)" -ForegroundColor Cyan
    Write-Host "   Location: $($activityResponse.data.activityLocation)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to create activity content" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

# 6. Doctor Create Health Guidance
Write-Host "`n6. Doctor Create Health Guidance..." -ForegroundColor Yellow
$doctorHeaders = @{
    'Authorization' = "Bearer $doctorToken"
    'Content-Type' = 'application/json'
}

try {
    $guidanceResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/contents" -Method POST -Headers $doctorHeaders -Body (Get-Content create-guidance.json -Raw)
    $guidanceId = $guidanceResponse.data.id
    Write-Host "✅ Health guidance created successfully" -ForegroundColor Green
    Write-Host "   Guidance ID: $guidanceId" -ForegroundColor Cyan
    Write-Host "   Title: $($guidanceResponse.data.title)" -ForegroundColor Cyan
    Write-Host "   Author: $($guidanceResponse.data.authorName)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to create health guidance" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

# 7. Test Permission Control (Doctor tries to create news - should fail)
Write-Host "`n7. Test Permission Control (Doctor tries to create news)..." -ForegroundColor Yellow
try {
    $unauthorizedResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/contents" -Method POST -Headers $doctorHeaders -Body (Get-Content create-news.json -Raw)
    Write-Host "❌ Permission control failed - doctor should not create news" -ForegroundColor Red
} catch {
    Write-Host "✅ Permission control working - doctor cannot create news" -ForegroundColor Green
}

# 8. Get All Content List (Public Access)
Write-Host "`n8. Get All Content List (Public Access)..." -ForegroundColor Yellow
try {
    $allContentResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/contents?page=1&size=10" -Method GET
    Write-Host "✅ All content list retrieved successfully" -ForegroundColor Green
    Write-Host "   Total contents: $($allContentResponse.data.totalElements)" -ForegroundColor Cyan
    
    foreach ($content in $allContentResponse.data.content) {
        Write-Host "   [$($content.contentType)] $($content.title) by $($content.authorName)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Failed to get all content list" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

# 9. Get Activity Content List
Write-Host "`n9. Get Activity Content List..." -ForegroundColor Yellow
try {
    $activityListResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/contents?contentType=ACTIVITY&page=1&size=5" -Method GET
    Write-Host "✅ Activity content list retrieved successfully" -ForegroundColor Green
    Write-Host "   Total activities: $($activityListResponse.data.totalElements)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to get activity content list" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

# 10. Get Content Detail (Public Access)
Write-Host "`n10. Get Content Detail (Public Access)..." -ForegroundColor Yellow
if ($activityId) {
    try {
        $contentDetailResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/contents/$activityId" -Method GET
        Write-Host "✅ Content detail retrieved successfully" -ForegroundColor Green
        Write-Host "   Title: $($contentDetailResponse.data.title)" -ForegroundColor Cyan
        Write-Host "   Author: $($contentDetailResponse.data.authorName)" -ForegroundColor Cyan
        Write-Host "   Type: $($contentDetailResponse.data.contentTypeDescription)" -ForegroundColor Cyan
    } catch {
        Write-Host "❌ Failed to get content detail" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
    }
}

# 11. Resident Register for Activity
Write-Host "`n11. Resident Register for Activity..." -ForegroundColor Yellow
if ($activityId) {
    $residentHeaders = @{
        'Authorization' = "Bearer $residentToken"
        'Content-Type' = 'application/json'
    }
    
    try {
        $registerResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/activities/$activityId/register" -Method POST -Headers $residentHeaders
        Write-Host "✅ Activity registration successful" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to register for activity" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
    }
}

# 12. Update Activity Content (Admin)
Write-Host "`n12. Update Activity Content (Admin)..." -ForegroundColor Yellow
if ($activityId) {
    try {
        $updateResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/contents/$activityId" -Method PUT -Headers $adminHeaders -Body (Get-Content update-content.json -Raw)
        Write-Host "✅ Activity content updated successfully" -ForegroundColor Green
        Write-Host "   Updated Title: $($updateResponse.data.title)" -ForegroundColor Cyan
    } catch {
        Write-Host "❌ Failed to update activity content" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
    }
}

# 13. Search Content by Keyword
Write-Host "`n13. Search Content by Keyword..." -ForegroundColor Yellow
try {
    $searchResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/contents?keyword=health&page=1&size=5" -Method GET
    Write-Host "✅ Content search successful" -ForegroundColor Green
    Write-Host "   Search results: $($searchResponse.data.totalElements)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to search content" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

# 14. Test Delete Permission (Resident tries to delete - should fail)
Write-Host "`n14. Test Delete Permission (Resident tries to delete)..." -ForegroundColor Yellow
if ($newsId) {
    try {
        $deleteResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/contents/$newsId" -Method DELETE -Headers $residentHeaders
        Write-Host "❌ Permission control failed - resident should not delete content" -ForegroundColor Red
    } catch {
        Write-Host "✅ Permission control working - resident cannot delete content" -ForegroundColor Green
    }
}

Write-Host "`n========================================" -ForegroundColor Cyan
Write-Host "    Content Management Test Complete" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "`n📋 Test Summary:" -ForegroundColor White
Write-Host "✅ 1. Create Content - POST /api/contents" -ForegroundColor Green
Write-Host "✅ 2. Update Content - PUT /api/contents/{contentId}" -ForegroundColor Green
Write-Host "✅ 3. Get Content List - GET /api/contents" -ForegroundColor Green
Write-Host "✅ 4. Get Content Detail - GET /api/contents/{contentId}" -ForegroundColor Green
Write-Host "✅ 5. Register Activity - POST /api/activities/{activityId}/register" -ForegroundColor Green
Write-Host "✅ 6. Permission Control Verification" -ForegroundColor Green

Write-Host "`n🎯 Functional Verification:" -ForegroundColor White
Write-Host "✅ Admins can create all types of content" -ForegroundColor Green
Write-Host "✅ Doctors can create health guidance only" -ForegroundColor Green
Write-Host "✅ Public can view all published content" -ForegroundColor Green
Write-Host "✅ Residents can register for activities" -ForegroundColor Green
Write-Host "✅ Content search and filtering work correctly" -ForegroundColor Green
Write-Host "✅ Permission controls are properly implemented" -ForegroundColor Green
