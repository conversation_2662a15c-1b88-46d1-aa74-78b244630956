# 社区健康管理系统

## 项目简介

社区健康管理系统是一个基于Spring Boot的现代化健康管理平台，提供用户认证、健康档案管理、健康数据记录、健康提醒等功能。

## 技术栈

- **后端框架**: Spring Boot 2.6.13
- **数据库**: MySQL 8.0
- **ORM框架**: Spring Data JPA + MyBatis
- **安全框架**: Spring Security + JWT
- **连接池**: HikariCP
- **构建工具**: Maven
- **Java版本**: JDK 8+

## 功能特性

### 🔐 用户认证模块
- 用户注册/登录
- JWT Token认证
- 密码加密存储
- 手机号验证

### 📋 健康档案管理
- 个人健康档案创建
- 家庭成员档案管理
- 健康信息维护

### 📊 健康数据记录
- 多种健康指标记录
- 数据趋势分析
- 历史记录查询

### ⏰ 健康提醒
- 个性化提醒设置
- 多种提醒类型
- 定时提醒功能

## 数据库设计

### 核心表结构
- `users` - 用户表
- `health_profiles` - 健康档案表
- `health_metric_records` - 健康指标记录表
- `health_reminders` - 健康提醒表
- `doctors` - 医生信息表
- `departments` - 科室表

## 快速开始

### 环境要求
- JDK 8 或更高版本
- Maven 3.6+
- MySQL 8.0+

### 数据库配置
1. 创建MySQL数据库：
```sql
CREATE DATABASE community_health_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改配置文件中的数据库连接信息：
```properties
spring.datasource.username=your_username
spring.datasource.password=your_password
```

### 启动应用

#### 开发环境
```bash
# Windows
scripts/start-dev.bat

# 或使用Maven命令
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

#### 生产环境
```bash
# Windows
scripts/start-prod.bat

# 或使用Maven命令
mvn clean package -DskipTests
java -jar -Dspring.profiles.active=prod target/BaoLeMe-0.0.1-SNAPSHOT.jar
```

### 访问应用
- 应用地址: http://localhost:8080
- API文档: http://localhost:8080/api-documentation.md
- 健康检查: http://localhost:8080/actuator/health

## API接口

### 用户认证
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录

### 健康档案
- `GET /api/health-profiles` - 获取健康档案列表
- `POST /api/health-profiles` - 创建健康档案
- `PUT /api/health-profiles/{id}` - 更新健康档案

### 健康记录
- `GET /api/health-records` - 获取健康记录
- `POST /api/health-records` - 添加健康记录

### 健康提醒
- `GET /api/health-reminders` - 获取提醒列表
- `POST /api/health-reminders` - 创建提醒

## 配置说明

### 环境配置
- `application.properties` - 默认配置
- `application-dev.properties` - 开发环境配置
- `application-prod.properties` - 生产环境配置

### 关键配置项
```properties
# 数据库配置
spring.datasource.url=***********************************************
spring.datasource.username=root
spring.datasource.password=123456

# JWT配置
jwt.secret=your_jwt_secret
jwt.expiration=604800000

# 连接池配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
```

## 测试

### 运行单元测试
```bash
mvn test
```

### 运行集成测试
```bash
mvn verify
```

## 部署

### Docker部署（推荐）
```bash
# 构建镜像
docker build -t community-health-system .

# 运行容器
docker run -d -p 8080:8080 --name community-health community-health-system
```

### 传统部署
1. 打包应用：`mvn clean package`
2. 上传jar包到服务器
3. 配置数据库连接
4. 启动应用：`java -jar app.jar`

## 监控

### 应用监控
- 健康检查: `/actuator/health`
- 应用信息: `/actuator/info`
- 数据库连接池监控: 通过HikariCP内置监控

### 日志
- 开发环境：控制台输出
- 生产环境：文件输出到 `logs/community-health.log`

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- 项目地址: https://github.com/your-org/community-health-system

---

**社区健康管理系统** - 让健康管理更简单、更智能！
