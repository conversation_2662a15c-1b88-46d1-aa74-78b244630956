# 电子处方API接口文档

## 📋 接口概述

本文档描述了社区健康管理系统模块二电子处方功能的2个核心API接口。

### 🎯 功能特性

- ✅ **医生开具处方**: 医生可以为患者开具电子处方，包含诊断和药品信息
- ✅ **患者查看处方**: 患者可以查看自己的历史处方记录
- ✅ **医生查看处方**: 医生可以查看自己开具的处方记录
- ✅ **权限控制**: 严格的角色权限验证，确保数据安全
- ✅ **药品信息管理**: 支持多种药品信息的结构化存储

---

## 🔐 认证说明

所有API接口都需要JWT Token认证，在请求头中添加：
```
Authorization: Bearer <token>
```

---

## 📝 API接口详情

### 1. 开具电子处方

**功能**: 医生为患者开具一张新的电子处方  
**权限**: 医生 (Doctor)

```http
POST /api/prescriptions
Content-Type: application/json
Authorization: Bearer <doctor_token>

{
  "profileId": 1,
  "consultationId": 1,
  "diagnosis": "Migraine headache with associated nausea",
  "medications": [
    {
      "name": "Ibuprofen",
      "specification": "400mg",
      "quantity": 20,
      "frequency": "3 times daily",
      "dosage": "Take 1 tablet with food",
      "notes": "Take after meals to reduce stomach irritation"
    },
    {
      "name": "Metoclopramide",
      "specification": "10mg",
      "quantity": 10,
      "frequency": "As needed",
      "dosage": "Take 1 tablet when nauseous",
      "notes": "Maximum 3 tablets per day"
    }
  ]
}
```

**字段说明**:
- `profileId`: 健康档案ID（必填）
- `consultationId`: 关联的问诊ID（可选）
- `diagnosis`: 临床诊断（必填）
- `medications`: 药品列表（必填，至少一种药品）
  - `name`: 药品名称（必填）
  - `specification`: 药品规格（必填）
  - `quantity`: 药品数量（必填，大于0）
  - `frequency`: 用药频次（必填）
  - `dosage`: 用法用量（必填）
  - `notes`: 备注信息（可选）

**响应示例**:
```json
{
  "code": 200,
  "message": "处方开具成功",
  "data": {
    "id": 1,
    "doctorId": 6,
    "doctorName": "王健康",
    "departmentName": "内科",
    "profileId": 1,
    "patientName": "张三",
    "consultationId": 1,
    "diagnosis": "Migraine headache with associated nausea",
    "medications": [
      {
        "name": "Ibuprofen",
        "specification": "400mg",
        "quantity": 20,
        "frequency": "3 times daily",
        "dosage": "Take 1 tablet with food",
        "notes": "Take after meals to reduce stomach irritation"
      },
      {
        "name": "Metoclopramide",
        "specification": "10mg",
        "quantity": 10,
        "frequency": "As needed",
        "dosage": "Take 1 tablet when nauseous",
        "notes": "Maximum 3 tablets per day"
      }
    ],
    "createdAt": "2025-06-15T15:25:33"
  }
}
```

---

### 2. 获取患者的处方列表

**功能**: 查看某个患者的所有历史处方记录  
**权限**: 该健康档案的管理人，或为该患者服务过的医生

```http
GET /api/profiles/{profileId}/prescriptions?page=1&size=10
Authorization: Bearer <token>
```

**参数说明**:
- `profileId`: 健康档案ID（路径参数）
- `page`: 页码，从1开始，默认1
- `size`: 每页大小，默认10

**响应示例**:
```json
{
  "code": 200,
  "message": "获取处方列表成功",
  "data": {
    "content": [
      {
        "id": 1,
        "doctorId": 6,
        "doctorName": "王健康",
        "departmentName": "内科",
        "profileId": 1,
        "patientName": "张三",
        "consultationId": 1,
        "diagnosis": "Migraine headache with associated nausea",
        "medications": [
          {
            "name": "Ibuprofen",
            "specification": "400mg",
            "quantity": 20,
            "frequency": "3 times daily",
            "dosage": "Take 1 tablet with food",
            "notes": "Take after meals to reduce stomach irritation"
          }
        ],
        "createdAt": "2025-06-15T15:25:33"
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 0
  }
}
```

---

### 3. 获取医生开具的处方列表（额外功能）

**功能**: 医生查看自己开具的所有处方记录  
**权限**: 医生 (Doctor)

```http
GET /api/prescriptions/doctor?page=1&size=10
Authorization: Bearer <doctor_token>
```

**参数说明**:
- `page`: 页码，从1开始，默认1
- `size`: 每页大小，默认10

**响应示例**:
```json
{
  "code": 200,
  "message": "获取医生处方列表成功",
  "data": {
    "content": [
      {
        "id": 1,
        "doctorId": 6,
        "doctorName": "王健康",
        "departmentName": "内科",
        "profileId": 1,
        "patientName": "张三",
        "consultationId": 1,
        "diagnosis": "Type 2 Diabetes Mellitus",
        "medications": [
          {
            "name": "Metformin",
            "specification": "500mg",
            "quantity": 60,
            "frequency": "Twice daily",
            "dosage": "Take 1 tablet with breakfast and dinner",
            "notes": "Monitor blood glucose levels regularly"
          }
        ],
        "createdAt": "2025-06-15T15:25:33"
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 0
  }
}
```

---

## 🔍 错误处理

### 常见错误码

- `401`: 未授权 - Token无效或过期
- `403`: 权限不足 - 角色权限不匹配
- `404`: 资源不存在 - 健康档案或医生不存在
- `500`: 服务器内部错误

### 错误响应格式

```json
{
  "code": 500,
  "message": "开具处方失败: 医生尚未通过审核，无法开具处方",
  "data": null
}
```

### 权限控制错误

```json
{
  "code": 500,
  "message": "获取处方列表失败: 无权限查看该患者的处方记录",
  "data": null
}
```

---

## 📊 数据库表结构

### e_prescriptions (电子处方表)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | BIGINT | 主键 |
| doctor_id | BIGINT | 医生用户ID |
| profile_id | BIGINT | 健康档案ID |
| consultation_id | BIGINT | 关联问诊ID（可选） |
| diagnosis | TEXT | 临床诊断 |
| medication_details | JSON | 药品信息（JSON格式） |
| created_at | DATETIME | 创建时间 |

### 药品信息JSON结构

```json
[
  {
    "name": "药品名称",
    "specification": "药品规格",
    "quantity": 数量,
    "frequency": "用药频次",
    "dosage": "用法用量",
    "notes": "备注信息"
  }
]
```

---

## 🎯 权限控制规则

### 开具处方权限
- ✅ 只有**医生**可以开具处方
- ✅ 医生必须通过审核（状态为APPROVED）
- ✅ 健康档案必须存在

### 查看处方权限
- ✅ **健康档案管理人**可以查看该档案的所有处方
- ✅ **为该患者服务过的医生**可以查看自己开具的处方
- ❌ 其他用户无法查看

### 医生处方列表权限
- ✅ 只有**医生**可以查看自己开具的处方列表
- ❌ 其他角色无法访问

---

## 🧪 测试验证

### 功能测试结果
- ✅ **医生开具处方** - 成功创建处方记录
- ✅ **患者查看处方** - 正确返回患者的处方列表
- ✅ **医生查看处方** - 正确返回医生开具的处方列表
- ✅ **权限控制** - 正确阻止无权限访问
- ✅ **数据持久化** - 处方数据正确保存到数据库
- ✅ **JSON存储** - 药品信息正确序列化和反序列化

### 数据库验证
处方数据已正确保存到 `e_prescriptions` 表：
- 处方ID: 1
- 医生ID: 6
- 健康档案ID: 2
- 诊断: "Type 2 Diabetes Mellitus"
- 药品信息: JSON格式存储

---

## 🏆 **模块二电子处方功能实现完成**

**所有2个核心API接口都已成功实现并通过测试：**

1. ✅ 开具电子处方 - `POST /api/prescriptions`
2. ✅ 获取患者的处方列表 - `GET /api/profiles/{profileId}/prescriptions`

**额外功能：**
3. ✅ 获取医生处方列表 - `GET /api/prescriptions/doctor`

电子处方功能已经完全实现并可以正常使用！医生可以为患者开具包含多种药品信息的电子处方，患者和相关医生可以查看处方历史记录。
