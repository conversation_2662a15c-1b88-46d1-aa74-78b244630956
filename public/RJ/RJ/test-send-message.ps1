# Test Send Message API

Write-Host "=== Test Send Message ===" -ForegroundColor Cyan

# 1. Resident Login
Write-Host "1. Resident Login..." -ForegroundColor Yellow
$residentResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method POST -ContentType "application/json" -Body (Get-Content login-resident.json -Raw)
$residentToken = $residentResponse.data.token
Write-Host "Resident login success" -ForegroundColor Green

# 2. Doctor Login
Write-Host "2. Doctor Login..." -ForegroundColor Yellow
$doctorResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method POST -ContentType "application/json" -Body (Get-Content login-doctor.json -Raw)
$doctorToken = $doctorResponse.data.token
Write-Host "Doctor login success" -ForegroundColor Green

# Use the consultation ID from previous test
$consultationId = 1

# 3. Resident Send Message
Write-Host "3. Resident Send Message..." -ForegroundColor Yellow
$residentHeaders = @{
    'Authorization' = "Bearer $residentToken"
    'Content-Type' = 'application/json'
}
$residentMessageBody = '{"content": "Additional info: The headache is mainly around the temples, has lasted for about 3 days, and sometimes I feel nauseous."}'

try {
    $messageResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations/$consultationId/messages" -Method POST -Headers $residentHeaders -Body $residentMessageBody
    Write-Host "SUCCESS: Resident message sent!" -ForegroundColor Green
    Write-Host "Message ID: $($messageResponse.data.id)" -ForegroundColor Cyan
} catch {
    Write-Host "ERROR: Failed to send resident message" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

# 4. Doctor Send Reply
Write-Host "4. Doctor Send Reply..." -ForegroundColor Yellow
$doctorHeaders = @{
    'Authorization' = "Bearer $doctorToken"
    'Content-Type' = 'application/json'
}
$doctorMessageBody = '{"content": "Based on your symptoms, temple pain with nausea may indicate migraine. Recommendations: 1. Get adequate sleep; 2. Avoid bright lights; 3. Gently massage temples. If symptoms persist or worsen, please seek further medical examination."}'

try {
    $doctorMessageResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations/$consultationId/messages" -Method POST -Headers $doctorHeaders -Body $doctorMessageBody
    Write-Host "SUCCESS: Doctor reply sent!" -ForegroundColor Green
    Write-Host "Message ID: $($doctorMessageResponse.data.id)" -ForegroundColor Cyan
} catch {
    Write-Host "ERROR: Failed to send doctor reply" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

# 5. Get Message History
Write-Host "5. Get Message History..." -ForegroundColor Yellow
try {
    $messagesResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations/$consultationId/messages?page=1&size=10" -Method GET -Headers $residentHeaders
    Write-Host "SUCCESS: Message history retrieved!" -ForegroundColor Green
    Write-Host "Total messages: $($messagesResponse.data.totalElements)" -ForegroundColor Cyan
    
    foreach ($message in $messagesResponse.data.content) {
        $roleColor = if ($message.senderRole -eq "RESIDENT") { "Blue" } else { "Magenta" }
        Write-Host "[$($message.senderRole)] $($message.senderNickname): $($message.content)" -ForegroundColor $roleColor
    }
} catch {
    Write-Host "ERROR: Failed to get message history" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

Write-Host "`n=== Test Completed ===" -ForegroundColor Cyan
