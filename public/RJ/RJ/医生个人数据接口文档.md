# 医生个人数据相关接口文档

## 基础认证接口

### 1. 医生登录
- **接口路径**: POST /api/user/login
- **功能描述**: 医生用户登录系统
- **请求参数**:
```json
{
  "phoneNumber": "18610001001",  // 手机号
  "password": "doctor666"        // 密码
}
```
- **响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",  // JWT Token
    "userInfo": {
      "id": 6,
      "nickname": "王医生",
      "phoneNumber": "18610001001",
      "realName": "王健康",
      "isDoctor": true,
      "isAdmin": false,
      "doctorStatus": "APPROVED",      // 医生审核状态
      "departmentName": "内科",        // 科室名称
      "title": "主任医师"              // 职称
    }
  }
}
```

### 2. 获取个人信息
- **接口路径**: GET /api/user/profile
- **功能描述**: 获取医生个人详细信息
- **请求头**: Authorization: Bearer {JWT_TOKEN}
- **响应数据**:
```json
{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "id": 6,
    "nickname": "王医生",
    "phoneNumber": "18610001001",
    "realName": "王健康",
    "gender": null,
    "age": null,
    "avatarUrl": null,
    "isDoctor": true,
    "isAdmin": false,
    "doctorStatus": "APPROVED",
    "departmentName": "内科",
    "title": "主任医师"
  }
}
```

### 3. 更新个人信息
- **接口路径**: PUT /api/user/profile
- **功能描述**: 更新医生个人资料（已完善支持所有字段）
- **请求头**: Authorization: Bearer {JWT_TOKEN}
- **可修改字段**:
  - `nickname`: 昵称（用户表字段）
  - `realName`: 真实姓名（医生表字段）
  - `gender`: 性别（医生表字段，枚举值：MALE/FEMALE/OTHER）
  - `birthDate`: 出生日期（医生表字段，格式：YYYY-MM-DD）
  - `idCardNumber`: 身份证号（医生表字段，需要唯一性验证）
  - `avatarUrl`: 头像链接（医生表字段）
  - `phoneNumber`: 手机号（用户表字段，需要唯一性验证）
- **请求参数**:
```json
{
  "nickname": "王医生-完整更新",                    // 昵称
  "realName": "王健康主任医师",                    // 真实姓名
  "gender": "MALE",                              // 性别
  "birthDate": "1978-05-15",                     // 出生日期
  "idCardNumber": "110101197805151234",          // 身份证号
  "avatarUrl": "https://example.com/avatar/doctor_wang_complete.jpg", // 头像URL
  "phoneNumber": "18610001001"                   // 手机号（可选）
}
```
- **响应数据**:
```json
{
  "code": 200,
  "message": "用户信息更新成功"
}
```

#### 字段说明
- **nickname**: 更新用户表中的昵称字段
- **realName**: 更新医生表中的真实姓名字段
- **gender**: 更新医生表中的性别字段，支持 MALE/FEMALE/OTHER
- **birthDate**: 更新医生表中的出生日期，系统会自动计算年龄
- **idCardNumber**: 更新医生表中的身份证号，需要通过身份证格式验证和唯一性验证
- **avatarUrl**: 更新医生表中的头像链接
- **phoneNumber**: 更新用户表中的手机号，系统会验证新手机号的唯一性

#### 修改限制
- 手机号必须唯一，如果新手机号已被其他用户使用，会返回错误
- 身份证号必须唯一，如果新身份证号已被其他医生使用，会返回错误
- 身份证号需要通过正则表达式格式验证
- 只有医生用户可以修改医生表相关字段
- 所有字段都是可选的，只传入需要修改的字段即可

### 4. 医生密码修改
- **接口路径**: POST /api/user/doctor/change-password
- **功能描述**: 医生用户专用密码修改接口
- **请求头**: Authorization: Bearer {JWT_TOKEN}
- **请求参数**:
```json
{
  "currentPassword": "doctor666",      // 当前密码
  "newPassword": "newdoctor123",       // 新密码（6-20位）
  "confirmPassword": "newdoctor123"    // 确认密码
}
```
- **响应数据**:
```json
{
  "code": 200,
  "message": "医生密码修改成功"
}
```

#### 密码修改规则
- 只有医生用户可以使用此接口
- 必须提供正确的当前密码
- 新密码长度必须在6-20位之间
- 新密码和确认密码必须一致
- 新密码不能与当前密码相同
- 密码修改成功后，原JWT Token仍然有效

## 医生专业功能接口

### 4. 医生排班管理
- **获取排班**: GET /api/doctor/schedules
- **创建排班**: POST /api/doctor/schedules
- **更新排班**: PUT /api/doctor/schedules/{id}
- **删除排班**: DELETE /api/doctor/schedules/{id}

### 5. 预约管理
- **获取预约列表**: GET /api/doctor/appointments
- **确认预约**: POST /api/doctor/appointments/{id}/confirm
- **完成诊疗**: POST /api/doctor/appointments/{id}/complete
- **取消预约**: POST /api/doctor/appointments/{id}/cancel

### 6. 患者管理
- **获取患者病历**: GET /api/doctor/patients/{id}/records
- **获取患者健康数据**: GET /api/doctor/patients/{id}/health-data

### 7. 统计信息
- **获取工作统计**: GET /api/doctor/statistics/my

## 数据模型

### 医生信息模型
```json
{
  "userId": 6,                    // 用户ID
  "realName": "王健康",           // 真实姓名
  "departmentId": 1,              // 科室ID
  "title": "主任医师",            // 职称
  "specialty": "心脑血管疾病、内科杂症",  // 专长
  "bio": "医术精湛，擅长各种内科疑难杂症的诊断与治疗。",  // 个人简介
  "status": "APPROVED"            // 审核状态: PENDING/APPROVED/REJECTED
}
```

### 用户基础信息模型
```json
{
  "id": 6,
  "phoneNumber": "18610001001",
  "nickname": "王医生",
  "role": "DOCTOR",               // 用户角色
  "createdAt": "2025-06-14T11:28:44"
}
```

## 认证说明
- 所有接口（除登录外）都需要在请求头中携带JWT Token
- Token格式: `Authorization: Bearer {JWT_TOKEN}`
- Token有效期: 7天
- 医生用户具有特殊权限，可以访问医生专用接口

## 医生个人数据修改测试

### 测试结果
✅ **医生可以成功修改个人数据**

#### 实际可修改的字段
1. **昵称 (nickname)** ✅
   - 存储位置: users表
   - 测试结果: 成功修改
   - 示例: "王医生-实际可修改" → "王医生-完整更新"

2. **真实姓名 (realName)** ✅
   - 存储位置: doctors表
   - 测试结果: 成功修改
   - 示例: "王健康主任医师" → "王健康主任医师"

3. **性别 (gender)** ✅
   - 存储位置: doctors表
   - 测试结果: 成功修改
   - 示例: null → "MALE"

4. **出生日期 (birthDate)** ✅
   - 存储位置: doctors表
   - 测试结果: 成功修改
   - 示例: null → "1978-05-15"
   - 自动计算年龄: 47岁

5. **身份证号 (idCardNumber)** ✅
   - 存储位置: doctors表
   - 测试结果: 成功修改
   - 示例: null → "110101197805151234"
   - 唯一性验证: 通过

6. **头像URL (avatarUrl)** ✅
   - 存储位置: doctors表
   - 测试结果: 成功修改
   - 示例: null → "https://example.com/avatar/doctor_wang_complete.jpg"

7. **手机号 (phoneNumber)** ✅
   - 存储位置: users表
   - 测试结果: 支持修改，但需要唯一性验证
   - 限制: 新手机号不能与其他用户重复

8. **密码修改** ✅
   - 专用接口: POST /api/user/doctor/change-password
   - 测试结果: 成功修改并验证
   - 验证流程: 原密码验证 → 新密码设置 → 新密码登录测试 → 密码恢复

#### 测试用例

##### 个人信息完整更新测试
```json
// 请求参数
{
  "nickname": "王医生-完整更新",
  "realName": "王健康主任医师",
  "gender": "MALE",
  "birthDate": "1978-05-15",
  "idCardNumber": "110101197805151234",
  "avatarUrl": "https://example.com/avatar/doctor_wang_complete.jpg"
}

// 响应结果
{
  "code": 200,
  "message": "用户信息更新成功"
}
```

##### 密码修改测试
```json
// 请求参数
{
  "currentPassword": "doctor666",
  "newPassword": "newdoctor123",
  "confirmPassword": "newdoctor123"
}

// 响应结果
{
  "code": 200,
  "message": "医生密码修改成功"
}
```

### 数据持久化验证
- ✅ 昵称修改后立即生效（users表）
- ✅ 真实姓名在医生信息中正确更新（doctors表）
- ✅ 性别字段成功保存并正确显示（doctors表）
- ✅ 出生日期保存后自动计算年龄（doctors表）
- ✅ 身份证号唯一性验证正常工作（doctors表）
- ✅ 头像URL正确保存和显示（doctors表）
- ✅ 手机号唯一性验证正常工作（users表）
- ✅ 密码修改功能完全正常，支持验证和恢复
- ✅ 所有修改的数据正确保存到MySQL数据库
- ✅ 数据库表结构完善，支持医生完整个人信息管理

## 测试账号
- 手机号: 18610001001
- 密码: doctor666
- 角色: 医生
- 状态: 已审核通过
- 科室: 内科
- 职称: 主任医师
