# Online Consultation API Test Script

Write-Host "=== Online Consultation API Test ===" -ForegroundColor Cyan

# 1. Resident Login
Write-Host "`n1. Resident Login..." -ForegroundColor Yellow
$residentResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method POST -ContentType "application/json" -Body (Get-Content login-resident.json -Raw)
$residentToken = $residentResponse.data.token
Write-Host "Resident login success, Token: $($residentToken.Substring(0,20))..." -ForegroundColor Green

# 2. Doctor Login
Write-Host "`n2. Doctor Login..." -ForegroundColor Yellow
$doctorResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method POST -ContentType "application/json" -Body (Get-Content login-doctor.json -Raw)
$doctorToken = $doctorResponse.data.token
Write-Host "Doctor login success, Token: $($doctorToken.Substring(0,20))..." -ForegroundColor Green

# 3. Create Consultation
Write-Host "`n3. Create Consultation..." -ForegroundColor Yellow
$headers = @{
    'Authorization' = "Bearer $residentToken"
    'Content-Type' = 'application/json'
}
$consultationResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations" -Method POST -Headers $headers -Body (Get-Content create-consultation.json -Raw)
$consultationId = $consultationResponse.data.id
Write-Host "Consultation created successfully, ID: $consultationId" -ForegroundColor Green
Write-Host "Doctor: $($consultationResponse.data.doctorName)" -ForegroundColor Cyan
Write-Host "Status: $($consultationResponse.data.statusDescription)" -ForegroundColor Cyan

# 4. Resident Send Message
Write-Host "`n4. Resident Send Message..." -ForegroundColor Yellow
$messageBody = '{"content":"Additional info: The headache is mainly around the temples, has lasted for about 3 days, and sometimes I feel nauseous."}'
$messageResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations/$consultationId/messages" -Method POST -Headers $headers -Body $messageBody
Write-Host "Resident message sent successfully, Message ID: $($messageResponse.data.id)" -ForegroundColor Green

# 5. Doctor Send Reply
Write-Host "`n5. Doctor Send Reply..." -ForegroundColor Yellow
$doctorHeaders = @{
    'Authorization' = "Bearer $doctorToken"
    'Content-Type' = 'application/json'
}
$doctorMessageBody = '{"content":"Based on your symptoms, temple pain with nausea may indicate migraine. Recommendations: 1. Get adequate sleep; 2. Avoid bright lights; 3. Gently massage temples. If symptoms persist or worsen, please seek further medical examination."}'
$doctorMessageResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations/$consultationId/messages" -Method POST -Headers $doctorHeaders -Body $doctorMessageBody
Write-Host "Doctor reply sent successfully, Message ID: $($doctorMessageResponse.data.id)" -ForegroundColor Green

# 6. Get Message History
Write-Host "`n6. Get Message History..." -ForegroundColor Yellow
$messagesResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations/$consultationId/messages?page=1&size=10" -Method GET -Headers $headers
Write-Host "Message history retrieved successfully, Total messages: $($messagesResponse.data.totalElements)" -ForegroundColor Green

foreach ($message in $messagesResponse.data.content) {
    $roleColor = if ($message.senderRole -eq "RESIDENT") { "Blue" } else { "Magenta" }
    Write-Host "[$($message.senderRole)] $($message.senderNickname): $($message.content)" -ForegroundColor $roleColor
}

# 7. Get Consultation List
Write-Host "`n7. Get Consultation List..." -ForegroundColor Yellow
$consultationsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations?page=1&size=5" -Method GET -Headers $headers
Write-Host "Consultation list retrieved successfully, Total consultations: $($consultationsResponse.data.totalElements)" -ForegroundColor Green

foreach ($consultation in $consultationsResponse.data.content) {
    Write-Host "Consultation ID: $($consultation.id) | Doctor: $($consultation.doctorName) | Status: $($consultation.statusDescription)" -ForegroundColor Cyan
}

# 8. Doctor Complete Consultation
Write-Host "`n8. Doctor Complete Consultation..." -ForegroundColor Yellow
$completeResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations/$consultationId/complete" -Method PUT -Headers $doctorHeaders
Write-Host "Consultation completed successfully" -ForegroundColor Green

Write-Host "`n=== Test Completed ===" -ForegroundColor Cyan
Write-Host "All online consultation features tested successfully!" -ForegroundColor Green

Write-Host "`nTest Summary:" -ForegroundColor White
Write-Host "✓ 1. Create Consultation - POST /api/consultations" -ForegroundColor Green
Write-Host "✓ 2. Get Consultation List - GET /api/consultations" -ForegroundColor Green
Write-Host "✓ 3. Send Message - POST /api/consultations/{id}/messages" -ForegroundColor Green
Write-Host "✓ 4. Get Message History - GET /api/consultations/{id}/messages" -ForegroundColor Green
Write-Host "✓ 5. Complete Consultation - PUT /api/consultations/{id}/complete" -ForegroundColor Green
