-- 为医生统计分析添加测试数据

USE community_health_db;

-- 添加一些在线问诊数据
INSERT INTO online_consultations (user_id, doctor_id, status, created_at) VALUES
(1, 7, 'completed', '2025-06-10 09:00:00'),
(2, 7, 'completed', '2025-06-11 14:30:00'),
(3, 7, 'completed', '2025-06-12 16:00:00'),
(1, 7, 'in_progress', '2025-06-14 10:00:00'),
(4, 8, 'completed', '2025-06-10 11:00:00'),
(5, 8, 'completed', '2025-06-13 15:00:00');

-- 添加一些健康指导内容
INSERT INTO contents (author_id, content_type, title, body, published_at) VALUES
(7, 'guidance', '夏季高血压患者注意事项', '夏季天气炎热，高血压患者需要特别注意以下几点...', '2025-06-10 08:00:00'),
(7, 'guidance', '糖尿病患者的饮食建议', '糖尿病患者在日常饮食中应该注意控制血糖...', '2025-06-12 09:00:00'),
(8, 'guidance', '外科手术后的康复指导', '外科手术后的康复期间，患者需要注意...', '2025-06-11 10:00:00'),
(7, 'news', '医院最新通知', '关于门诊时间调整的通知...', '2025-06-13 14:00:00');

-- 更新一些预约状态为已完成
UPDATE appointments SET status = 'completed', updated_at = NOW() WHERE id IN (1, 2);
UPDATE appointments SET status = 'cancelled', updated_at = NOW() WHERE id = 3;

-- 查看当前数据状态
SELECT '=== 预约数据统计 ===' as info;
SELECT status, COUNT(*) as count FROM appointments GROUP BY status;

SELECT '=== 在线问诊数据统计 ===' as info;
SELECT status, COUNT(*) as count FROM online_consultations GROUP BY status;

SELECT '=== 内容数据统计 ===' as info;
SELECT content_type, COUNT(*) as count FROM contents GROUP BY content_type;

SELECT '=== 医生7的服务统计 ===' as info;
SELECT 
    '预约已完成' as service_type,
    COUNT(*) as count
FROM appointments a 
JOIN doctor_schedules s ON a.schedule_id = s.id 
WHERE s.doctor_id = 7 AND a.status = 'completed'
UNION ALL
SELECT 
    '在线问诊已完成' as service_type,
    COUNT(*) as count
FROM online_consultations 
WHERE doctor_id = 7 AND status = 'completed';
