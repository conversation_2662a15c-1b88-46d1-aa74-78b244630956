# 简化的内容管理API测试

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Content Management API Test" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# 1. 医生登录
Write-Host "`n1. Doctor Login..." -ForegroundColor Yellow
$doctorResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method POST -ContentType "application/json" -Body (Get-Content login-doctor.json -Raw)
$doctorToken = $doctorResponse.data.token
Write-Host "✅ Doctor login success" -ForegroundColor Green

# 2. 医生创建健康指导
Write-Host "`n2. Doctor Create Health Guidance..." -ForegroundColor Yellow
$doctorHeaders = @{
    'Authorization' = "Bearer $doctorToken"
    'Content-Type' = 'application/json'
}

try {
    $guidanceResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/contents" -Method POST -Headers $doctorHeaders -Body (Get-Content create-guidance.json -Raw)
    $guidanceId = $guidanceResponse.data.id
    Write-Host "✅ Health guidance created successfully" -ForegroundColor Green
    Write-Host "   Guidance ID: $guidanceId" -ForegroundColor Cyan
    Write-Host "   Title: $($guidanceResponse.data.title)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to create health guidance" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

# 3. 获取活动内容列表（公开接口）
Write-Host "`n3. Get Activity Content List (Public)..." -ForegroundColor Yellow
try {
    $activityListResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/contents?contentType=ACTIVITY&page=1&size=5" -Method GET
    Write-Host "✅ Activity content list retrieved successfully" -ForegroundColor Green
    Write-Host "   Total activities: $($activityListResponse.data.totalElements)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to get activity content list" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

# 4. 搜索健康相关内容
Write-Host "`n4. Search Health Content..." -ForegroundColor Yellow
try {
    $searchResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/contents?keyword=health&page=1&size=5" -Method GET
    Write-Host "✅ Content search successful" -ForegroundColor Green
    Write-Host "   Search results: $($searchResponse.data.totalElements)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to search content" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

Write-Host "`n========================================" -ForegroundColor Cyan
Write-Host "    Content Management Test Complete" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
