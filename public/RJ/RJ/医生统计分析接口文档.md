# 医生统计分析接口文档

## 概述

医生统计分析模块提供了完整的医生工作数据统计和分析功能，包括4个核心KPI指标和4个可视化图表分析。所有接口都支持时间范围参数，可以查看本周、本月或近三个月的数据。

## 接口列表

### 1. 核心指标KPI接口

**接口地址**: `GET /api/doctor/statistics/kpi`

**请求参数**:
- `range` (可选): 时间范围，支持 `week`(本周)、`month`(本月)、`quarter`(近三个月)，默认为 `month`

**请求头**:
```
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

**响应示例**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "totalServiceCount": 0,           // 总服务人次
        "totalServiceCountChange": 0,     // 总服务人次变化
        "appointmentCompletionRate": 0.0, // 预约完成率(%)
        "completedAppointments": 0,       // 已完成预约数
        "totalAppointments": 3,           // 总预约数
        "newPatientCount": 0,             // 新增患者数量
        "newPatientCountChange": 0,       // 新增患者数量变化
        "publishedGuidanceCount": 1,      // 发布健康指南数量
        "monthlyGuidanceCount": 1         // 本月发布健康指南总数
    }
}
```

### 2. 服务量趋势接口

**接口地址**: `GET /api/doctor/statistics/service-trend`

**请求参数**:
- `range` (可选): 时间范围，默认为 `month`

**请求头**:
```
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

**响应示例**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "dailyData": [
            {
                "date": "2025-05-15",
                "appointmentCount": 0,      // 预约服务数量
                "consultationCount": 0,     // 在线问诊数量
                "totalCount": 0             // 总服务数量
            },
            // ... 更多日期数据
        ]
    }
}
```

### 3. 预约状态分配接口

**接口地址**: `GET /api/doctor/statistics/appointment-status`

**请求参数**:
- `range` (可选): 时间范围，默认为 `month`

**请求头**:
```
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

**响应示例**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "statusData": [
            {
                "status": "BOOKED",
                "statusDescription": "已预约",
                "count": 3,
                "percentage": 100.0
            },
            {
                "status": "COMPLETED",
                "statusDescription": "已完成",
                "count": 0,
                "percentage": 0.0
            },
            {
                "status": "CANCELLED",
                "statusDescription": "已取消",
                "count": 0,
                "percentage": 0.0
            }
        ]
    }
}
```

### 4. 高频服务患者排行接口

**接口地址**: `GET /api/doctor/statistics/top-patients`

**请求参数**:
- `range` (可选): 时间范围，默认为 `month`

**请求头**:
```
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

**响应示例**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "patientData": [
            {
                "patientId": 1,
                "patientName": "张三",
                "serviceCount": 5,          // 总服务次数
                "appointmentCount": 3,      // 预约次数
                "consultationCount": 2      // 在线问诊次数
            }
            // ... 更多患者数据 (TOP 5)
        ]
    }
}
```

### 5. 预约时间段分析接口

**接口地址**: `GET /api/doctor/statistics/schedule-hotness`

**请求参数**:
- `range` (可选): 时间范围，默认为 `month`

**请求头**:
```
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

**响应示例**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "timeSlotData": [
            {
                "timeSlot": "08-10点",
                "startHour": 8,
                "endHour": 10,
                "appointmentCount": 2,
                "hotnessPercentage": 66.67
            },
            {
                "timeSlot": "10-12点",
                "startHour": 10,
                "endHour": 12,
                "appointmentCount": 0,
                "hotnessPercentage": 0.0
            },
            {
                "timeSlot": "14-16点",
                "startHour": 14,
                "endHour": 16,
                "appointmentCount": 1,
                "hotnessPercentage": 33.33
            },
            {
                "timeSlot": "16-18点",
                "startHour": 16,
                "endHour": 18,
                "appointmentCount": 0,
                "hotnessPercentage": 0.0
            }
        ]
    }
}
```

## 错误码说明

- `200`: 成功
- `401`: 未授权，需要登录
- `403`: 权限不足，需要医生角色
- `500`: 服务器内部错误

## 使用说明

1. 所有接口都需要医生角色的JWT Token认证
2. 时间范围参数支持：
   - `week`: 本周数据
   - `month`: 本月数据（默认）
   - `quarter`: 近三个月数据
3. 所有统计数据都是基于当前登录医生的数据
4. 接口返回的时间都是服务器本地时间
