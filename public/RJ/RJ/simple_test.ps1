# 简化的接口测试脚本

$userToken = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMzgwMDAwMDAxMSIsImlhdCI6MTc0OTk4NzM4NSwiZXhwIjoxNzUwNTkyMTg1fQ.dYRRmNCNyGNBdVyhpQ4ii5x9cbttuDWO3LxXtywErzk"
$doctorToken = "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxODYxMDAwMTAwMSIsImlhdCI6MTc0OTk4NzM5MywiZXhwIjoxNzUwNTkyMTkzfQ.VuXAoM17XoLhHXtbks8M47cNZwxh48ccex2OWH19F50"

Write-Host "=== 在线问诊和预约管理接口测试 ===" -ForegroundColor Green

# 测试1: 用户创建问诊会话
Write-Host "`n1. 测试用户创建问诊会话" -ForegroundColor Yellow
$createBody = '{"doctorId": 6, "initialMessage": "医生您好，我最近感觉头痛，想咨询一下可能的原因和治疗建议。"}'
$headers1 = @{"Content-Type"="application/json"; "Authorization"="Bearer $userToken"}

try {
    $response1 = Invoke-WebRequest -Uri "http://localhost:8080/api/consultations" -Method POST -Headers $headers1 -Body $createBody
    $result1 = $response1.Content | ConvertFrom-Json
    Write-Host "状态码: $($response1.StatusCode)" -ForegroundColor Green
    Write-Host "响应: $($result1 | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
    $consultationId = $result1.data.id
    Write-Host "问诊ID: $consultationId" -ForegroundColor Magenta
} catch {
    Write-Host "创建问诊失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试2: 用户发送消息
Write-Host "`n2. 测试用户发送消息" -ForegroundColor Yellow
if ($consultationId) {
    $messageBody = '{"content": "补充一下，头痛主要是在太阳穴附近，持续了大概3天了，有时候还会感到恶心。"}'
    try {
        $response2 = Invoke-WebRequest -Uri "http://localhost:8080/api/consultations/$consultationId/messages" -Method POST -Headers $headers1 -Body $messageBody
        $result2 = $response2.Content | ConvertFrom-Json
        Write-Host "状态码: $($response2.StatusCode)" -ForegroundColor Green
        Write-Host "响应: $($result2 | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
    } catch {
        Write-Host "发送消息失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试3: 医生查看问诊列表
Write-Host "`n3. 测试医生查看问诊列表" -ForegroundColor Yellow
$headers2 = @{"Authorization"="Bearer $doctorToken"}
try {
    $response3 = Invoke-WebRequest -Uri "http://localhost:8080/api/consultations" -Method GET -Headers $headers2
    $result3 = $response3.Content | ConvertFrom-Json
    Write-Host "状态码: $($response3.StatusCode)" -ForegroundColor Green
    Write-Host "响应: $($result3 | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
} catch {
    Write-Host "获取问诊列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试4: 医生回复消息
Write-Host "`n4. 测试医生回复消息" -ForegroundColor Yellow
if ($consultationId) {
    $replyBody = '{"content": "根据您描述的症状，太阳穴疼痛伴随恶心可能是偏头痛的表现。建议您：1. 保证充足睡眠；2. 避免强光刺激；3. 可以适当按摩太阳穴。如果症状持续或加重，建议到医院进一步检查。"}'
    $headers3 = @{"Content-Type"="application/json"; "Authorization"="Bearer $doctorToken"}
    try {
        $response4 = Invoke-WebRequest -Uri "http://localhost:8080/api/consultations/$consultationId/messages" -Method POST -Headers $headers3 -Body $replyBody
        $result4 = $response4.Content | ConvertFrom-Json
        Write-Host "状态码: $($response4.StatusCode)" -ForegroundColor Green
        Write-Host "响应: $($result4 | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
    } catch {
        Write-Host "医生回复失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试5: 医生查看预约患者列表
Write-Host "`n5. 测试医生查看预约患者列表" -ForegroundColor Yellow
try {
    $response5 = Invoke-WebRequest -Uri "http://localhost:8080/api/doctor/appointments/my" -Method GET -Headers $headers2
    $result5 = $response5.Content | ConvertFrom-Json
    Write-Host "状态码: $($response5.StatusCode)" -ForegroundColor Green
    Write-Host "响应: $($result5 | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
    
    if ($result5.data.content -and $result5.data.content.Count -gt 0) {
        $appointmentId = $result5.data.content[0].id
        Write-Host "预约ID: $appointmentId" -ForegroundColor Magenta
    }
} catch {
    Write-Host "获取预约列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试6: 医生完成诊疗
Write-Host "`n6. 测试医生完成诊疗" -ForegroundColor Yellow
if ($appointmentId) {
    $completeBody = '{"notes": "患者症状已缓解，建议继续观察，注意休息"}'
    try {
        $response6 = Invoke-WebRequest -Uri "http://localhost:8080/api/doctor/appointments/$appointmentId/complete" -Method POST -Headers $headers3 -Body $completeBody
        $result6 = $response6.Content | ConvertFrom-Json
        Write-Host "状态码: $($response6.StatusCode)" -ForegroundColor Green
        Write-Host "响应: $($result6 | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
    } catch {
        Write-Host "完成诊疗失败: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "跳过完成诊疗测试，因为没有找到预约记录" -ForegroundColor Yellow
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
