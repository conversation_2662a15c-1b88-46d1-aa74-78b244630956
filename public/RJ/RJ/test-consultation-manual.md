# 在线问诊功能手动测试指南

## 测试环境
- 服务器地址: http://localhost:8080
- 测试账号:
  - 居民: 13800000001 / 123456
  - 医生: 18610001001 / doctor666

## 测试步骤

### 1. 居民用户登录
```bash
curl -X POST http://localhost:8080/api/user/login \
  -H "Content-Type: application/json" \
  -d '{"phoneNumber":"13800000001","password":"123456"}'
```

**预期结果**: 返回包含token的成功响应
**记录token**: `RESIDENT_TOKEN=<返回的token>`

### 2. 医生用户登录
```bash
curl -X POST http://localhost:8080/api/user/login \
  -H "Content-Type: application/json" \
  -d '{"phoneNumber":"18610001001","password":"doctor666"}'
```

**预期结果**: 返回包含token的成功响应
**记录token**: `DOCTOR_TOKEN=<返回的token>`

### 3. 创建问诊会话（居民发起）
```bash
curl -X POST http://localhost:8080/api/consultations \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer RESIDENT_TOKEN" \
  -d '{"doctorId":7,"initialMessage":"医生您好，我最近感觉头痛，想咨询一下可能的原因和治疗建议。"}'
```

**预期结果**: 返回创建成功的问诊会话信息
**记录会话ID**: `CONSULTATION_ID=<返回的id>`

### 4. 居民发送消息
```bash
curl -X POST http://localhost:8080/api/consultations/CONSULTATION_ID/messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer RESIDENT_TOKEN" \
  -d '{"content":"补充一下，头痛主要是在太阳穴附近，持续了大概3天了，有时候还会感到恶心。"}'
```

**预期结果**: 返回消息发送成功

### 5. 医生发送回复
```bash
curl -X POST http://localhost:8080/api/consultations/CONSULTATION_ID/messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer DOCTOR_TOKEN" \
  -d '{"content":"根据您描述的症状，太阳穴疼痛伴随恶心可能是偏头痛的表现。建议您：1. 保证充足睡眠；2. 避免强光刺激；3. 可以适当按摩太阳穴。如果症状持续或加重，建议到医院进一步检查。"}'
```

**预期结果**: 返回消息发送成功

### 6. 获取消息历史记录
```bash
curl -X GET "http://localhost:8080/api/consultations/CONSULTATION_ID/messages?page=1&size=10" \
  -H "Authorization: Bearer RESIDENT_TOKEN"
```

**预期结果**: 返回包含所有消息的历史记录

### 7. 获取问诊列表（居民视角）
```bash
curl -X GET "http://localhost:8080/api/consultations?page=1&size=5" \
  -H "Authorization: Bearer RESIDENT_TOKEN"
```

**预期结果**: 返回居民的问诊列表

### 8. 获取问诊列表（医生视角）
```bash
curl -X GET "http://localhost:8080/api/consultations?page=1&size=5" \
  -H "Authorization: Bearer DOCTOR_TOKEN"
```

**预期结果**: 返回医生的问诊列表

### 9. 医生完成问诊
```bash
curl -X PUT http://localhost:8080/api/consultations/CONSULTATION_ID/complete \
  -H "Authorization: Bearer DOCTOR_TOKEN"
```

**预期结果**: 返回问诊完成成功

### 10. 验证问诊状态更新
```bash
curl -X GET "http://localhost:8080/api/consultations?page=1&size=5" \
  -H "Authorization: Bearer RESIDENT_TOKEN"
```

**预期结果**: 问诊状态应该显示为"已完成"

## 数据库验证

执行以下SQL查询验证数据是否正确保存：

```sql
-- 查看问诊会话
SELECT * FROM online_consultations ORDER BY created_at DESC LIMIT 5;

-- 查看消息记录
SELECT m.*, u.nickname as sender_name 
FROM messages m 
JOIN users u ON m.sender_id = u.id 
ORDER BY m.sent_at DESC LIMIT 10;

-- 验证外键关联
SELECT 
    oc.id as consultation_id,
    oc.status,
    u1.nickname as patient_name,
    u2.nickname as doctor_name,
    COUNT(m.id) as message_count
FROM online_consultations oc
JOIN users u1 ON oc.user_id = u1.id
JOIN users u2 ON oc.doctor_id = u2.id
LEFT JOIN messages m ON oc.id = m.consultation_id
GROUP BY oc.id;
```

## 测试检查点

- [ ] 居民可以成功登录并获取token
- [ ] 医生可以成功登录并获取token
- [ ] 居民可以向医生发起问诊
- [ ] 双方可以在问诊中发送消息
- [ ] 消息历史记录完整保存
- [ ] 问诊列表按角色正确显示
- [ ] 医生可以完成问诊会话
- [ ] 权限控制正确实施
- [ ] 数据库中数据正确保存
- [ ] 外键关联正确建立
