# 在线问诊功能测试脚本
$baseUrl = "http://localhost:8080"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    在线问诊功能测试开始" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# 全局变量
$residentToken = ""
$doctorToken = ""
$consultationId = ""

# 1. 居民用户登录
Write-Host "`n1. 居民用户登录..." -ForegroundColor Yellow
$loginData = @{
    phoneNumber = "13800000001"
    password = "123456"
}
$loginJson = $loginData | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $loginJson -Headers $headers
    if ($response.success) {
        $residentToken = $response.data.token
        Write-Host "✅ 居民登录成功" -ForegroundColor Green
    } else {
        Write-Host "❌ 居民登录失败: $($response.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ 居民登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 医生用户登录
Write-Host "`n2. 医生用户登录..." -ForegroundColor Yellow
$doctorLoginData = @{
    phoneNumber = "18610001001"
    password = "doctor666"
}
$doctorLoginJson = $doctorLoginData | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $doctorLoginJson -Headers $headers
    if ($response.success) {
        $doctorToken = $response.data.token
        Write-Host "✅ 医生登录成功" -ForegroundColor Green
    } else {
        Write-Host "❌ 医生登录失败: $($response.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ 医生登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 3. 创建问诊会话（居民发起）
Write-Host "`n3. 创建问诊会话..." -ForegroundColor Yellow
$createConsultationData = @{
    doctorId = 7
    initialMessage = "医生您好，我最近感觉头痛，想咨询一下可能的原因和治疗建议。"
}
$createConsultationJson = $createConsultationData | ConvertTo-Json

$authHeaders = $headers.Clone()
$authHeaders["Authorization"] = "Bearer $residentToken"

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/consultations" -Method POST -Body $createConsultationJson -Headers $authHeaders
    if ($response.success) {
        $consultationId = $response.data.id
        Write-Host "✅ 问诊会话创建成功" -ForegroundColor Green
        Write-Host "   会话ID: $consultationId" -ForegroundColor Cyan
        Write-Host "   医生: $($response.data.doctorName)" -ForegroundColor Cyan
        Write-Host "   科室: $($response.data.departmentName)" -ForegroundColor Cyan
        Write-Host "   状态: $($response.data.statusDescription)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ 创建问诊会话失败: $($response.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ 创建问诊会话请求失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 4. 居民发送消息
Write-Host "`n4. 居民发送消息..." -ForegroundColor Yellow
$residentMessageData = @{
    content = "补充一下，头痛主要是在太阳穴附近，持续了大概3天了，有时候还会感到恶心。"
}
$residentMessageJson = $residentMessageData | ConvertTo-Json

try {
    $messageUrl = "$baseUrl/api/consultations/$consultationId/messages"
    $response = Invoke-RestMethod -Uri $messageUrl -Method POST -Body $residentMessageJson -Headers $authHeaders
    if ($response.success) {
        Write-Host "✅ 居民消息发送成功" -ForegroundColor Green
        Write-Host "   消息ID: $($response.data.id)" -ForegroundColor Cyan
        Write-Host "   发送时间: $($response.data.sentAt)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ 居民发送消息失败: $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 居民发送消息请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 医生发送回复消息
Write-Host "`n5. 医生发送回复消息..." -ForegroundColor Yellow
$doctorMessageData = @{
    content = "根据您描述的症状，太阳穴疼痛伴随恶心可能是偏头痛的表现。建议您：1. 保证充足睡眠；2. 避免强光刺激；3. 可以适当按摩太阳穴。如果症状持续或加重，建议到医院进一步检查。"
}
$doctorMessageJson = $doctorMessageData | ConvertTo-Json

$doctorAuthHeaders = $headers.Clone()
$doctorAuthHeaders["Authorization"] = "Bearer $doctorToken"

try {
    $messageUrl = "$baseUrl/api/consultations/$consultationId/messages"
    $response = Invoke-RestMethod -Uri $messageUrl -Method POST -Body $doctorMessageJson -Headers $doctorAuthHeaders
    if ($response.success) {
        Write-Host "✅ 医生回复发送成功" -ForegroundColor Green
        Write-Host "   消息ID: $($response.data.id)" -ForegroundColor Cyan
        Write-Host "   发送时间: $($response.data.sentAt)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ 医生发送回复失败: $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 医生发送回复请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 获取消息历史记录（居民视角）
Write-Host "`n6. 获取消息历史记录（居民视角）..." -ForegroundColor Yellow
try {
    $messagesUrl = "$baseUrl/api/consultations/$consultationId/messages?page=1&size=10"
    $response = Invoke-RestMethod -Uri $messagesUrl -Method GET -Headers $authHeaders
    if ($response.success) {
        Write-Host "✅ 获取消息历史成功" -ForegroundColor Green
        Write-Host "   总消息数: $($response.data.totalElements)" -ForegroundColor Cyan
        Write-Host "   当前页消息数: $($response.data.content.Count)" -ForegroundColor Cyan
        
        foreach ($message in $response.data.content) {
            $roleColor = if ($message.senderRole -eq "RESIDENT") { "Blue" } else { "Magenta" }
            Write-Host "   [$($message.senderRole)] $($message.senderNickname): $($message.content)" -ForegroundColor $roleColor
        }
    } else {
        Write-Host "❌ 获取消息历史失败: $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 获取消息历史请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. 获取问诊列表（居民视角）
Write-Host "`n7. 获取问诊列表（居民视角）..." -ForegroundColor Yellow
try {
    $consultationsUrl = "$baseUrl/api/consultations?page=1&size=5"
    $response = Invoke-RestMethod -Uri $consultationsUrl -Method GET -Headers $authHeaders
    if ($response.success) {
        Write-Host "✅ 获取居民问诊列表成功" -ForegroundColor Green
        Write-Host "   总问诊数: $($response.data.totalElements)" -ForegroundColor Cyan
        
        foreach ($consultation in $response.data.content) {
            Write-Host "   问诊ID: $($consultation.id) | 医生: $($consultation.doctorName) | 状态: $($consultation.statusDescription)" -ForegroundColor Cyan
            if ($consultation.lastMessage) {
                Write-Host "   最后消息: $($consultation.lastMessage)" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "❌ 获取居民问诊列表失败: $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 获取居民问诊列表请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 8. 获取问诊列表（医生视角）
Write-Host "`n8. 获取问诊列表（医生视角）..." -ForegroundColor Yellow
try {
    $consultationsUrl = "$baseUrl/api/consultations?page=1&size=5"
    $response = Invoke-RestMethod -Uri $consultationsUrl -Method GET -Headers $doctorAuthHeaders
    if ($response.success) {
        Write-Host "✅ 获取医生问诊列表成功" -ForegroundColor Green
        Write-Host "   总问诊数: $($response.data.totalElements)" -ForegroundColor Cyan
        
        foreach ($consultation in $response.data.content) {
            Write-Host "   问诊ID: $($consultation.id) | 患者: $($consultation.userNickname) | 状态: $($consultation.statusDescription)" -ForegroundColor Cyan
            if ($consultation.lastMessage) {
                Write-Host "   最后消息: $($consultation.lastMessage)" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "❌ 获取医生问诊列表失败: $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 获取医生问诊列表请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 9. 医生完成问诊
Write-Host "`n9. 医生完成问诊..." -ForegroundColor Yellow
try {
    $completeUrl = "$baseUrl/api/consultations/$consultationId/complete"
    $response = Invoke-RestMethod -Uri $completeUrl -Method PUT -Headers $doctorAuthHeaders
    if ($response.success) {
        Write-Host "✅ 问诊完成成功" -ForegroundColor Green
    } else {
        Write-Host "❌ 完成问诊失败: $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 完成问诊请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 10. 验证问诊状态已更新
Write-Host "`n10. 验证问诊状态..." -ForegroundColor Yellow
try {
    $consultationsUrl = "$baseUrl/api/consultations?page=1&size=5"
    $response = Invoke-RestMethod -Uri $consultationsUrl -Method GET -Headers $authHeaders
    if ($response.success) {
        $targetConsultation = $response.data.content | Where-Object { $_.id -eq $consultationId }
        if ($targetConsultation) {
            Write-Host "✅ 问诊状态验证成功" -ForegroundColor Green
            Write-Host "   问诊ID: $($targetConsultation.id)" -ForegroundColor Cyan
            Write-Host "   当前状态: $($targetConsultation.statusDescription)" -ForegroundColor Cyan
            Write-Host "   消息总数: $($targetConsultation.messageCount)" -ForegroundColor Cyan
        } else {
            Write-Host "❌ 未找到目标问诊记录" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ 验证问诊状态失败: $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 验证问诊状态请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n========================================" -ForegroundColor Cyan
Write-Host "    在线问诊功能测试完成" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "`n📋 测试总结:" -ForegroundColor White
Write-Host "✅ 1. 创建问诊会话 - POST /api/consultations" -ForegroundColor Green
Write-Host "✅ 2. 获取问诊列表 - GET /api/consultations" -ForegroundColor Green
Write-Host "✅ 3. 发送消息 - POST /api/consultations/{id}/messages" -ForegroundColor Green
Write-Host "✅ 4. 获取消息历史 - GET /api/consultations/{id}/messages" -ForegroundColor Green
Write-Host "✅ 5. 完成问诊 - PUT /api/consultations/{id}/complete" -ForegroundColor Green

Write-Host "`n🎯 功能验证:" -ForegroundColor White
Write-Host "✅ 居民可以向医生发起问诊" -ForegroundColor Green
Write-Host "✅ 双方可以在问诊中发送消息" -ForegroundColor Green
Write-Host "✅ 消息历史记录完整保存" -ForegroundColor Green
Write-Host "✅ 问诊列表按角色正确显示" -ForegroundColor Green
Write-Host "✅ 医生可以完成问诊会话" -ForegroundColor Green
Write-Host "✅ 权限控制正确实施" -ForegroundColor Green
