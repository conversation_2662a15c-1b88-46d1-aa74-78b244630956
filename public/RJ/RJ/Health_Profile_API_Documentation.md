# 用户健康档案管理API接口文档

## 基本信息
- **服务器地址**: http://localhost:8080
- **API基础路径**: /api/health/profiles
- **认证方式**: JWT Token (Bearer Token)

## 功能概述

✅ **创建健康档案** - 支持为家庭成员创建多个档案  
✅ **档案列表查询** - 获取用户管理的所有档案（支持分页）  
✅ **档案详情查看** - 完整的档案信息展示  
✅ **档案信息更新** - 支持部分字段更新  
✅ **档案删除** - 安全的档案删除功能  
✅ **权限控制** - 只能管理自己创建的档案  

---

## API接口清单

### 1. 创建健康档案

**接口地址**: `POST /api/health/profiles`

**请求头**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数**:
```json
{
  "profileOwnerName": "string",     // 档案所有者姓名 (必填)
  "gender": "string",               // 性别: MALE/FEMALE/OTHER (可选)
  "birthDate": "string",            // 出生日期: YYYY-MM-DD (可选)
  "idCard": "string",               // 身份证号 (可选，唯一)
  "avatarUrl": "string",            // 头像链接 (可选)
  "medicalHistory": "string"        // 病史信息 (可选)
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "创建健康档案成功",
  "data": {
    "id": 6,
    "profileOwnerName": "Li Ming",
    "gender": "MALE",
    "age": 33,
    "birthDate": "1990-05-15",
    "idCard": "110101199005151234",
    "medicalHistory": "No major medical history"
  }
}
```

---

### 2. 获取档案列表

**接口地址**: `GET /api/health/profiles`

**请求头**:
```
Authorization: Bearer {token}
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页大小 (默认: 10)

**响应数据**:
```json
{
  "code": 200,
  "message": "获取健康档案列表成功",
  "data": {
    "profiles": [
      {
        "id": 1,
        "profileOwnerName": "张三",
        "gender": "MALE",
        "age": 30
      }
    ],
    "total": 5,
    "page": 1,
    "size": 10,
    "totalPages": 1
  }
}
```

---

### 3. 获取档案详情

**接口地址**: `GET /api/health/profiles/{id}`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
- `id`: 档案ID

**响应数据**:
```json
{
  "code": 200,
  "message": "获取健康档案详情成功",
  "data": {
    "id": 6,
    "profileOwnerName": "Li Ming",
    "gender": "MALE",
    "birthDate": "1990-05-15",
    "age": 33,
    "idCard": "110101199005151234",
    "avatarUrl": null,
    "medicalHistory": "No major medical history",
    "phone": null,
    "emergencyContact": null,
    "emergencyPhone": null,
    "allergies": null,
    "bloodType": null,
    "height": null,
    "weight": null,
    "bmi": null,
    "createdAt": null,
    "updatedAt": null
  }
}
```

---

### 4. 更新档案信息

**接口地址**: `PUT /api/health/profiles/{id}`

**请求头**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**路径参数**:
- `id`: 档案ID

**请求参数** (支持部分更新):
```json
{
  "profileOwnerName": "string",     // 档案所有者姓名
  "gender": "string",               // 性别: MALE/FEMALE/OTHER
  "birthDate": "string",            // 出生日期: YYYY-MM-DD
  "idCard": "string",               // 身份证号
  "medicalHistory": "string"        // 病史信息
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "更新健康档案成功",
  "data": null
}
```

---

### 5. 删除档案

**接口地址**: `DELETE /api/health/profiles/{id}`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:
- `id`: 档案ID

**响应数据**:
```json
{
  "code": 200,
  "message": "删除健康档案成功",
  "data": null
}
```

---

## 数据库字段说明

### 支持的字段 (已实现)
- `id`: 档案ID (自动生成)
- `managing_user_id`: 管理用户ID (自动设置)
- `profile_owner_name`: 档案所有者姓名
- `gender`: 性别 (ENUM: male, female, other)
- `birth_date`: 出生日期
- `id_card_number`: 身份证号 (唯一索引)
- `avatar_url`: 头像链接
- `medical_history`: 病史信息

### 计算字段
- `age`: 年龄 (根据出生日期自动计算)

### 暂未实现的字段
以下字段在VO中定义但数据库表中不存在，返回时为null：
- `phone`: 电话号码
- `emergencyContact`: 紧急联系人
- `emergencyPhone`: 紧急联系电话
- `allergies`: 过敏信息
- `bloodType`: 血型
- `height`: 身高
- `weight`: 体重
- `bmi`: 体重指数
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

---

## 权限控制

- 用户只能管理自己创建的健康档案
- 通过JWT Token中的用户ID进行权限验证
- 不能访问其他用户的档案信息

---

## 错误响应

**常见错误码**:
- `400`: 请求参数错误
- `401`: 认证失败或Token无效
- `403`: 权限不足，无法访问该档案
- `404`: 档案不存在
- `500`: 服务器内部错误

**错误响应格式**:
```json
{
  "code": 404,
  "message": "健康档案不存在",
  "data": null
}
```

---

## 测试示例

### PowerShell测试脚本
项目中提供了完整的测试脚本：
- `test_health_profiles_enhanced.ps1`: 增强版健康档案API测试

### 运行测试
```powershell
powershell -ExecutionPolicy Bypass -File test_health_profiles_enhanced.ps1
```

---

## 注意事项

1. **身份证号唯一性**: 身份证号字段有唯一索引，不能重复
2. **年龄自动计算**: 年龄根据出生日期自动计算，不需要手动设置
3. **部分更新支持**: 更新接口支持部分字段更新，只传需要更新的字段即可
4. **分页查询**: 列表查询支持分页，建议使用分页参数
5. **权限隔离**: 严格的权限控制，确保数据安全

---

## 测试状态

✅ **所有核心功能已测试通过**
- 创建档案: ✅ 成功
- 获取列表: ✅ 成功  
- 获取详情: ✅ 成功
- 更新档案: ✅ 成功
- 删除档案: ✅ 成功
- 权限控制: ✅ 正常工作

健康档案管理API功能完整，可以正常使用！
