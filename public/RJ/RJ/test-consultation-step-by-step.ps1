# 分步测试在线问诊功能

Write-Host "=== 在线问诊功能分步测试 ===" -ForegroundColor Cyan

# 1. 居民登录
Write-Host "`n1. 居民登录..." -ForegroundColor Yellow
$residentResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method POST -ContentType "application/json" -Body (Get-Content login-resident.json -Raw)
$residentToken = $residentResponse.data.token
Write-Host "居民登录成功，Token: $($residentToken.Substring(0,20))..." -ForegroundColor Green

# 2. 医生登录
Write-Host "`n2. 医生登录..." -ForegroundColor Yellow
$doctorResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method POST -ContentType "application/json" -Body (Get-Content login-doctor.json -Raw)
$doctorToken = $doctorResponse.data.token
Write-Host "医生登录成功，Token: $($doctorToken.Substring(0,20))..." -ForegroundColor Green

# 3. 创建问诊会话
Write-Host "`n3. 创建问诊会话..." -ForegroundColor Yellow
$headers = @{
    'Authorization' = "Bearer $residentToken"
    'Content-Type' = 'application/json'
}
$consultationResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations" -Method POST -Headers $headers -Body (Get-Content create-consultation.json -Raw)
$consultationId = $consultationResponse.data.id
Write-Host "问诊会话创建成功，ID: $consultationId" -ForegroundColor Green
Write-Host "医生: $($consultationResponse.data.doctorName)" -ForegroundColor Cyan
Write-Host "状态: $($consultationResponse.data.statusDescription)" -ForegroundColor Cyan

# 4. 居民发送消息
Write-Host "`n4. 居民发送消息..." -ForegroundColor Yellow
$messageBody = '{"content":"补充一下，头痛主要是在太阳穴附近，持续了大概3天了，有时候还会感到恶心。"}'
$messageResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations/$consultationId/messages" -Method POST -Headers $headers -Body $messageBody
Write-Host "居民消息发送成功，消息ID: $($messageResponse.data.id)" -ForegroundColor Green

# 5. 医生发送回复
Write-Host "`n5. 医生发送回复..." -ForegroundColor Yellow
$doctorHeaders = @{
    'Authorization' = "Bearer $doctorToken"
    'Content-Type' = 'application/json'
}
$doctorMessageBody = '{"content":"根据您描述的症状，太阳穴疼痛伴随恶心可能是偏头痛的表现。建议您：1. 保证充足睡眠；2. 避免强光刺激；3. 可以适当按摩太阳穴。如果症状持续或加重，建议到医院进一步检查。"}'
$doctorMessageResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations/$consultationId/messages" -Method POST -Headers $doctorHeaders -Body $doctorMessageBody
Write-Host "医生回复发送成功，消息ID: $($doctorMessageResponse.data.id)" -ForegroundColor Green

# 6. 获取消息历史
Write-Host "`n6. 获取消息历史..." -ForegroundColor Yellow
$messagesResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations/$consultationId/messages?page=1&size=10" -Method GET -Headers $headers
Write-Host "获取消息历史成功，总消息数: $($messagesResponse.data.totalElements)" -ForegroundColor Green

foreach ($message in $messagesResponse.data.content) {
    $roleColor = if ($message.senderRole -eq "RESIDENT") { "Blue" } else { "Magenta" }
    Write-Host "[$($message.senderRole)] $($message.senderNickname): $($message.content)" -ForegroundColor $roleColor
}

# 7. 获取问诊列表
Write-Host "`n7. 获取问诊列表..." -ForegroundColor Yellow
$consultationsResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations?page=1&size=5" -Method GET -Headers $headers
Write-Host "获取问诊列表成功，总问诊数: $($consultationsResponse.data.totalElements)" -ForegroundColor Green

foreach ($consultation in $consultationsResponse.data.content) {
    Write-Host "问诊ID: $($consultation.id) | 医生: $($consultation.doctorName) | 状态: $($consultation.statusDescription)" -ForegroundColor Cyan
}

# 8. 医生完成问诊
Write-Host "`n8. 医生完成问诊..." -ForegroundColor Yellow
$completeResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations/$consultationId/complete" -Method PUT -Headers $doctorHeaders
Write-Host "问诊完成成功" -ForegroundColor Green

Write-Host "`n=== 测试完成 ===" -ForegroundColor Cyan
Write-Host "所有在线问诊功能测试通过！" -ForegroundColor Green
