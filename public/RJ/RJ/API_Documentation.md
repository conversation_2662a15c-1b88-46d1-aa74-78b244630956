# 社区健康管理系统 - 用户认证API接口文档

## 基本信息
- **服务器地址**: http://localhost:8080
- **API基础路径**: /api/user
- **认证方式**: JWT <PERSON>ken (Bearer Token)

## 接口列表

### 1. 用户登录接口

**接口地址**: `POST /api/user/login`

**请求参数**:
```json
{
  "phoneNumber": "string",  // 手机号码
  "password": "string"      // 密码
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",  // JWT Token
    "userInfo": {
      "id": 1,
      "nickname": "张三",
      "phoneNumber": "13800000001",
      "realName": null,
      "gender": null,
      "age": null,
      "avatarUrl": null,
      "isDoctor": false,      // 是否为医生
      "isAdmin": false,       // 是否为管理员
      "doctorStatus": null,   // 医生状态(仅医生有效)
      "departmentName": null, // 科室名称(仅医生有效)
      "title": null          // 职称(仅医生有效)
    }
  }
}
```

**测试账号**:
- 居民用户: `13800000001` / `123456`
- 医生用户: `18610001001` / `doctor666`
- 管理员用户: `19999999999` / `admin888`

---

### 2. 用户注册接口

**接口地址**: `POST /api/user/register`

**请求参数**:
```json
{
  "phoneNumber": "string",  // 手机号码
  "password": "string",     // 密码
  "nickname": "string",     // 昵称
  "role": "string"         // 角色: RESIDENT(居民), DOCTOR(医生), ADMIN(管理员)
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": null
}
```

**角色说明**:
- `RESIDENT`: 居民用户
- `DOCTOR`: 医生用户
- `ADMIN`: 管理员用户

---

### 3. 获取用户信息接口

**接口地址**: `GET /api/user/profile`

**请求头**:
```
Authorization: Bearer {token}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "id": 1,
    "phoneNumber": "13800000001",
    "nickname": "张三",
    "realName": null,
    "gender": null,
    "age": null,
    "avatarUrl": null,
    "role": "RESIDENT",
    "createdAt": "2025-06-14T00:22:05",
    "admin": false,
    "doctor": false
  }
}
```

---

## 错误响应格式

当请求失败时，返回以下格式：

```json
{
  "code": 401,
  "message": "用户名或密码错误",
  "data": null
}
```

**常见错误码**:
- `400`: 请求参数错误
- `401`: 认证失败(用户名密码错误或Token无效)
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

---

## 使用示例

### 1. 登录示例 (PowerShell)

```powershell
$loginData = @{
    phoneNumber = "13800000001"
    password = "123456"
} | ConvertTo-Json

$response = Invoke-WebRequest -Uri "http://localhost:8888/api/user/login" -Method POST -ContentType "application/json" -Body $loginData
```

### 2. 注册示例 (PowerShell)

```powershell
$registerData = @{
    phoneNumber = "13800000011"
    password = "123456"
    nickname = "测试用户"
    role = "RESIDENT"
} | ConvertTo-Json

$response = Invoke-WebRequest -Uri "http://localhost:8888/api/user/register" -Method POST -ContentType "application/json" -Body $registerData
```

### 3. 获取用户信息示例 (PowerShell)

```powershell
$headers = @{
    "Authorization" = "Bearer eyJhbGciOiJIUzI1NiJ9..."
    "Content-Type" = "application/json"
}

$response = Invoke-WebRequest -Uri "http://localhost:8080/api/user/profile" -Method GET -Headers $headers
```

---

## 测试脚本

项目根目录下提供了完整的测试脚本：
- `api_test.ps1`: 完整的API测试脚本
- `simple_test.ps1`: 简单的登录测试脚本

运行测试脚本：
```powershell
powershell -ExecutionPolicy Bypass -File api_test.ps1
```

---

## 注意事项

1. **Token有效期**: JWT Token默认有效期为7天
2. **密码安全**: 生产环境中密码应该进行加密存储
3. **角色权限**: 不同角色用户具有不同的系统权限
4. **数据库**: 使用MySQL数据库，数据库名为 `community_health_db`
5. **端口配置**: 应用运行在8888端口

---

## 数据库用户角色映射

系统中的用户角色映射关系：
- 手机号以 `138` 开头: 居民用户 (RESIDENT)
- 手机号以 `186` 开头: 医生用户 (DOCTOR)  
- 手机号以 `199` 开头: 管理员用户 (ADMIN)

测试完成后，所有用户认证功能正常工作，数据成功持久化到MySQL数据库中。
