# 预约挂号模块 API 契约

## 概述
预约挂号模块提供完整的医生查询、排班查询、预约创建、预约管理功能。

## API 接口列表

### 1. 科室管理

#### 1.1 获取所有科室
- **URL**: `GET /api/appointments/departments`
- **描述**: 获取所有有医生的科室列表
- **认证**: 需要JWT Token
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "内科",
      "description": "内科疾病诊治",
      "doctorCount": 5
    }
  ]
}
```

### 2. 医生管理

#### 2.1 搜索医生
- **URL**: `GET /api/appointments/doctors`
- **描述**: 搜索医生（支持分页和筛选）
- **认证**: 需要JWT Token
- **查询参数**:
  - `name`: 医生姓名（可选）
  - `departmentId`: 科室ID（可选）
  - `page`: 页码（默认1）
  - `size`: 每页大小（默认10）

#### 2.2 获取科室下的医生列表
- **URL**: `GET /api/appointments/departments/{departmentId}/doctors`
- **描述**: 获取指定科室下的医生列表
- **认证**: 需要JWT Token

#### 2.3 获取医生详情
- **URL**: `GET /api/appointments/doctors/{doctorId}`
- **描述**: 获取医生详细信息
- **认证**: 需要JWT Token

### 3. 排班管理

#### 3.1 获取医生的可预约排班
- **URL**: `GET /api/appointments/doctors/{doctorId}/schedules`
- **描述**: 获取医生的可预约排班列表
- **认证**: 需要JWT Token
- **查询参数**:
  - `startDate`: 开始日期（可选，默认今天）
  - `endDate`: 结束日期（可选，默认30天后）

### 4. 预约管理

#### 4.1 创建预约
- **URL**: `POST /api/appointments`
- **描述**: 创建新的预约
- **认证**: 需要JWT Token
- **请求体**:
```json
{
  "scheduleId": 1,
  "profileId": 1,
  "notes": "预约备注"
}
```

#### 4.2 取消预约
- **URL**: `POST /api/appointments/{appointmentId}/cancel`
- **描述**: 取消预约
- **认证**: 需要JWT Token

#### 4.3 获取用户的预约列表
- **URL**: `GET /api/appointments/my`
- **描述**: 获取当前用户的预约列表
- **认证**: 需要JWT Token
- **查询参数**:
  - `status`: 预约状态（可选）
  - `page`: 页码（默认1）
  - `size`: 每页大小（默认10）

## 数据模型

### 科室 (Department)
```json
{
  "id": 1,
  "name": "内科",
  "description": "内科疾病诊治",
  "doctorCount": 5
}
```

### 医生 (Doctor)
```json
{
  "id": 2,
  "userId": 2,
  "realName": "张三",
  "title": "主任医师",
  "specialty": "心血管疾病诊治",
  "bio": "从事心血管疾病诊治20年",
  "status": "APPROVED",
  "departmentId": 1,
  "departmentName": "内科"
}
```

### 排班 (Schedule)
```json
{
  "id": 1,
  "doctorId": 2,
  "doctorName": "张三",
  "departmentName": "内科",
  "scheduleDate": "2025-06-11",
  "startTime": "09:00",
  "endTime": "12:00",
  "totalSlots": 10,
  "bookedSlots": 3,
  "availableSlots": 7,
  "isAvailable": true
}
```

### 预约 (Appointment)
```json
{
  "id": 1,
  "userId": 1,
  "status": "BOOKED",
  "statusDescription": "已预约",
  "createdAt": "2025-06-10T20:00:00",
  "notes": "预约备注",
  "scheduleId": 1,
  "appointmentDate": "2025-06-11",
  "appointmentTime": "09:00",
  "endTime": "12:00",
  "doctorId": 2,
  "doctorName": "张三",
  "doctorTitle": "主任医师",
  "departmentName": "内科",
  "profileId": 1,
  "profileOwnerName": "李四",
  "profileGender": "MALE",
  "canCancel": true,
  "canComplete": true
}
```

## 错误码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 无权限
- `404`: 资源不存在
- `500`: 服务器内部错误

## 业务规则

1. **权限控制**：
   - 用户只能查看已审核通过的医生
   - 用户只能预约自己管理的健康档案
   - 用户只能查看和操作自己的预约

2. **预约规则**：
   - 只能预约有可用号源的排班
   - 同一用户不能在同一排班重复预约
   - 只有状态为"已预约"的预约可以取消

3. **数据完整性**：
   - 预约创建时会自动更新排班的已预约数量
   - 预约取消时会自动释放排班号源
