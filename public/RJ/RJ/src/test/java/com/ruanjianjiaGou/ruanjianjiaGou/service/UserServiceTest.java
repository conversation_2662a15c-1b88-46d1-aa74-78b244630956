package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.dto.UserLoginDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.UserRegisterDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.User;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.UserRepository;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.LoginVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户服务测试类
 */
@SpringBootTest
@ActiveProfiles("dev")
@Transactional
public class UserServiceTest {

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;

    private UserRegisterDTO registerDTO;
    private UserLoginDTO loginDTO;

    @BeforeEach
    void setUp() {
        // 清理测试数据
        userRepository.deleteAll();
        
        // 准备测试数据
        registerDTO = new UserRegisterDTO();
        registerDTO.setPhoneNumber("13800138000");
        registerDTO.setPassword("test123456");
        registerDTO.setNickname("测试用户");

        loginDTO = new UserLoginDTO();
        loginDTO.setPhoneNumber("13800138000");
        loginDTO.setPassword("test123456");
    }

    @Test
    void testRegisterSuccess() {
        // 测试注册成功
        assertDoesNotThrow(() -> userService.register(registerDTO));
        
        // 验证用户是否保存到数据库
        assertTrue(userRepository.existsByPhoneNumber("13800138000"));
        
        User user = userRepository.findByPhoneNumber("13800138000").orElse(null);
        assertNotNull(user);
        assertEquals("测试用户", user.getNickname());
        assertNotNull(user.getCreatedAt());
    }

    @Test
    void testRegisterDuplicatePhone() {
        // 先注册一个用户
        userService.register(registerDTO);
        
        // 再次注册相同手机号，应该抛出异常
        assertThrows(RuntimeException.class, () -> userService.register(registerDTO));
    }

    @Test
    void testLoginSuccess() {
        // 先注册用户
        userService.register(registerDTO);
        
        // 测试登录成功
        LoginVO loginVO = userService.login(loginDTO);
        
        assertNotNull(loginVO);
        assertNotNull(loginVO.getToken());
        assertNotNull(loginVO.getUserInfo());
        assertEquals("13800138000", loginVO.getUserInfo().getPhoneNumber());
        assertEquals("测试用户", loginVO.getUserInfo().getNickname());
    }

    @Test
    void testLoginWithWrongPassword() {
        // 先注册用户
        userService.register(registerDTO);
        
        // 使用错误密码登录
        loginDTO.setPassword("wrongpassword");
        
        assertThrows(RuntimeException.class, () -> userService.login(loginDTO));
    }

    @Test
    void testLoginWithNonExistentUser() {
        // 使用不存在的用户登录
        assertThrows(RuntimeException.class, () -> userService.login(loginDTO));
    }
}
