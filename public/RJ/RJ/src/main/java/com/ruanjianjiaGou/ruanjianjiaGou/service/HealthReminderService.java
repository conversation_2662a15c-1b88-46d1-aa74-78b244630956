package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.dto.HealthReminderCreateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.HealthReminder;
import com.ruanjianjiaGou.ruanjianjiaGou.enums.ReminderType;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.HealthReminderRepository;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.TodayReminderVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class HealthReminderService {
    
    @Autowired
    private HealthReminderRepository reminderRepository;
    
    /**
     * 创建健康提醒
     */
    public HealthReminder createReminder(Long userId, HealthReminderCreateDTO dto) {
        HealthReminder reminder = new HealthReminder();
        reminder.setUserId(userId);
        
        BeanUtils.copyProperties(dto, reminder);
        
        // 设置默认值
        if (reminder.getStartDate() == null) {
            reminder.setStartDate(LocalDate.now());
        }
        if (reminder.getIsActive() == null) {
            reminder.setIsActive(true);
        }
        
        // 计算下次提醒时间
        reminder.setNextReminderTime(calculateNextReminderTime(reminder));
        
        return reminderRepository.save(reminder);
    }
    
    /**
     * 获取用户的健康提醒列表
     */
    @Transactional(readOnly = true)
    public List<HealthReminder> getUserReminders(Long userId, String status, String type) {
        Specification<HealthReminder> spec = Specification.where(null);
        spec = spec.and((root, criteriaQuery, criteriaBuilder) ->
            criteriaBuilder.equal(root.get("userId"), userId));

        if ("active".equals(status)) {
            spec = spec.and((root, criteriaQuery, criteriaBuilder) ->
                criteriaBuilder.isTrue(root.get("isActive")));
        } else if ("inactive".equals(status)) {
            spec = spec.and((root, criteriaQuery, criteriaBuilder) ->
                criteriaBuilder.isFalse(root.get("isActive")));
        }

        if (type != null) {
            try {
                ReminderType reminderType = ReminderType.valueOf(type.toUpperCase());
                spec = spec.and((root, criteriaQuery, criteriaBuilder) ->
                    criteriaBuilder.equal(root.get("reminderType"), reminderType));
            } catch (IllegalArgumentException e) {
                // 忽略无效的类型参数
            }
        }

        return reminderRepository.findAll(spec, Sort.by(Sort.Direction.DESC, "createdAt"));
    }

    /**
     * 获取用户的健康提醒列表（分页）
     */
    @Transactional(readOnly = true)
    public Page<HealthReminder> getUserReminders(Long userId, String status, String type, int page, int size) {
        Specification<HealthReminder> spec = Specification.where(null);
        spec = spec.and((root, criteriaQuery, criteriaBuilder) ->
            criteriaBuilder.equal(root.get("userId"), userId));

        if ("active".equals(status)) {
            spec = spec.and((root, criteriaQuery, criteriaBuilder) ->
                criteriaBuilder.isTrue(root.get("isActive")));
        } else if ("inactive".equals(status)) {
            spec = spec.and((root, criteriaQuery, criteriaBuilder) ->
                criteriaBuilder.isFalse(root.get("isActive")));
        }

        if (type != null) {
            try {
                ReminderType reminderType = ReminderType.valueOf(type.toUpperCase());
                spec = spec.and((root, criteriaQuery, criteriaBuilder) ->
                    criteriaBuilder.equal(root.get("reminderType"), reminderType));
            } catch (IllegalArgumentException e) {
                // 忽略无效的类型参数
            }
        }

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        return reminderRepository.findAll(spec, pageable);
    }
    
    /**
     * 获取今日提醒
     */
    @Transactional(readOnly = true)
    public List<TodayReminderVO> getTodayReminders(Long userId) {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDay = LocalDate.now().atTime(23, 59, 59);
        
        List<HealthReminder> reminders = reminderRepository.findByUserIdAndIsActiveTrueAndNextReminderTimeBetween(
            userId, startOfDay, endOfDay);
        
        return reminders.stream()
            .map(this::convertToTodayReminderVO)
            .collect(Collectors.toList());
    }
    
    /**
     * 标记提醒完成
     */
    public HealthReminder completeReminder(Long userId, Long reminderId) {
        HealthReminder reminder = reminderRepository.findByIdAndUserId(reminderId, userId)
            .orElseThrow(() -> new RuntimeException("健康提醒不存在或无权操作"));
        
        // 计算下次提醒时间
        LocalDateTime nextTime = calculateNextReminderTime(reminder);
        reminder.setNextReminderTime(nextTime);
        
        return reminderRepository.save(reminder);
    }
    
    /**
     * 更新健康提醒
     */
    public void updateReminder(Long userId, Long reminderId, HealthReminderCreateDTO dto) {
        HealthReminder reminder = reminderRepository.findByIdAndUserId(reminderId, userId)
            .orElseThrow(() -> new RuntimeException("健康提醒不存在或无权操作"));
        
        // 更新非空字段
        if (dto.getTitle() != null) {
            reminder.setTitle(dto.getTitle());
        }
        if (dto.getContent() != null) {
            reminder.setContent(dto.getContent());
        }
        if (dto.getReminderType() != null) {
            reminder.setReminderType(dto.getReminderType());
        }
        if (dto.getFrequencyType() != null) {
            reminder.setFrequencyType(dto.getFrequencyType());
        }
        if (dto.getFrequencyValue() != null) {
            reminder.setFrequencyValue(dto.getFrequencyValue());
        }
        if (dto.getStartDate() != null) {
            reminder.setStartDate(dto.getStartDate());
        }
        if (dto.getEndDate() != null) {
            reminder.setEndDate(dto.getEndDate());
        }
        if (dto.getIsActive() != null) {
            reminder.setIsActive(dto.getIsActive());
        }
        
        // 重新计算下次提醒时间
        reminder.setNextReminderTime(calculateNextReminderTime(reminder));
        
        reminderRepository.save(reminder);
    }
    
    /**
     * 删除健康提醒
     */
    public void deleteReminder(Long userId, Long reminderId) {
        HealthReminder reminder = reminderRepository.findByIdAndUserId(reminderId, userId)
            .orElseThrow(() -> new RuntimeException("健康提醒不存在或无权操作"));
        
        reminderRepository.delete(reminder);
    }
    
    /**
     * 计算下次提醒时间
     */
    private LocalDateTime calculateNextReminderTime(HealthReminder reminder) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime baseTime = reminder.getStartDate().atStartOfDay();
        
        if (now.isBefore(baseTime)) {
            baseTime = baseTime;
        } else {
            baseTime = now;
        }
        
        try {
            switch (reminder.getFrequencyType()) {
                case DAILY:
                    // frequencyValue 格式: "08:00"
                    LocalTime time = LocalTime.parse(reminder.getFrequencyValue());
                    LocalDateTime nextDaily = baseTime.toLocalDate().atTime(time);
                    if (nextDaily.isBefore(now) || nextDaily.equals(now)) {
                        nextDaily = nextDaily.plusDays(1);
                    }
                    return nextDaily;
                    
                case WEEKLY:
                    // frequencyValue 格式: "MONDAY,08:00"
                    String[] weeklyParts = reminder.getFrequencyValue().split(",");
                    DayOfWeek dayOfWeek = DayOfWeek.valueOf(weeklyParts[0]);
                    LocalTime weeklyTime = LocalTime.parse(weeklyParts[1]);
                    
                    LocalDateTime nextWeekly = baseTime.toLocalDate()
                        .with(TemporalAdjusters.nextOrSame(dayOfWeek))
                        .atTime(weeklyTime);
                    if (nextWeekly.isBefore(now) || nextWeekly.equals(now)) {
                        nextWeekly = nextWeekly.plusWeeks(1);
                    }
                    return nextWeekly;
                    
                case MONTHLY:
                    // frequencyValue 格式: "15,08:00" (每月15日8点)
                    String[] monthlyParts = reminder.getFrequencyValue().split(",");
                    int dayOfMonth = Integer.parseInt(monthlyParts[0]);
                    LocalTime monthlyTime = LocalTime.parse(monthlyParts[1]);
                    
                    LocalDateTime nextMonthly = baseTime.toLocalDate()
                        .withDayOfMonth(Math.min(dayOfMonth, baseTime.toLocalDate().lengthOfMonth()))
                        .atTime(monthlyTime);
                    if (nextMonthly.isBefore(now) || nextMonthly.equals(now)) {
                        nextMonthly = nextMonthly.plusMonths(1)
                            .withDayOfMonth(Math.min(dayOfMonth, nextMonthly.plusMonths(1).toLocalDate().lengthOfMonth()));
                    }
                    return nextMonthly;
                    
                case CUSTOM:
                default:
                    // 自定义逻辑，这里简化为每天
                    return baseTime.plusDays(1);
            }
        } catch (Exception e) {
            // 如果解析失败，默认为明天同一时间
            return baseTime.plusDays(1);
        }
    }
    
    /**
     * 转换为今日提醒VO
     */
    private TodayReminderVO convertToTodayReminderVO(HealthReminder reminder) {
        TodayReminderVO vo = new TodayReminderVO();
        vo.setId(reminder.getId());
        vo.setTitle(reminder.getTitle());
        vo.setContent(reminder.getContent());
        vo.setReminderTime(reminder.getNextReminderTime().toLocalTime().toString());
        vo.setIsCompleted(false); // 这里可以根据实际业务逻辑判断
        return vo;
    }
}
