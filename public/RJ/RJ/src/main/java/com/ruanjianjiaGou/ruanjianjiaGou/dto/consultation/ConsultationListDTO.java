package com.ruanjianjiaGou.ruanjianjiaGou.dto.consultation;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.OnlineConsultation;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 问诊列表DTO
 */
@Data
public class ConsultationListDTO {
    
    private Long id;
    private Long userId;
    private String userNickname;
    private String userPhoneNumber;
    private Long doctorId;
    private String doctorName;
    private String departmentName;
    private OnlineConsultation.ConsultationStatus status;
    private String statusDescription;
    private LocalDateTime createdAt;
    private String lastMessage; // 最后一条消息内容
    private LocalDateTime lastMessageTime; // 最后一条消息时间
    private Long messageCount; // 消息总数
}
