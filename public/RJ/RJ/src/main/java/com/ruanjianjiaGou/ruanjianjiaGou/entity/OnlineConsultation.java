package com.ruanjianjiaGou.ruanjianjiaGou.entity;

import com.ruanjianjiaGou.ruanjianjiaGou.converter.ConsultationStatusConverter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 在线问诊实体类
 */
@Entity
@Table(name = "online_consultations", indexes = {
    @Index(name = "idx_user_id", columnList = "user_id"),
    @Index(name = "idx_doctor_id", columnList = "doctor_id"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OnlineConsultation {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
    
    @Column(name = "doctor_id", nullable = false)
    private Long doctorId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "doctor_id", insertable = false, updatable = false)
    private Doctor doctor;
    
    @Convert(converter = ConsultationStatusConverter.class)
    @Column(name = "status", nullable = false)
    private ConsultationStatus status = ConsultationStatus.IN_PROGRESS;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        if (status == null) {
            status = ConsultationStatus.IN_PROGRESS;
        }
    }
    
    /**
     * 问诊状态枚举
     */
    public enum ConsultationStatus {
        IN_PROGRESS("进行中"),
        COMPLETED("已完成");
        
        private final String description;
        
        ConsultationStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
