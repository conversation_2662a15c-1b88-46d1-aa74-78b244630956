package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.ChangePasswordDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.DoctorPasswordUpdateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.UserLoginDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.UserRegisterDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.UserUpdateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.service.UserService;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.LoginVO;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.UserInfoVO;
import com.ruanjianjiaGou.ruanjianjiaGou.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@RequestMapping("/api/user")
public class UserController {
    
    @Autowired
    private UserService userService;

    @Autowired
    private SecurityUtils securityUtils;
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<Void> register(@Valid @RequestBody UserRegisterDTO userRegisterDTO) {
        try {
            userService.register(userRegisterDTO);
            return Result.success("注册成功");
        } catch (RuntimeException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("注册失败，请稍后重试");
        }
    }
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<LoginVO> login(@Valid @RequestBody UserLoginDTO userLoginDTO) {

        try {
            LoginVO loginVO = userService.login(userLoginDTO);
            return Result.success("登录成功", loginVO);
        } catch (RuntimeException e) {
            return Result.error(401, "用户名或密码错误");
        } catch (Exception e) {
            return Result.error("登录失败，请稍后重试");
        }
    }
    
    /**
     * 获取用户信息
     */
    @GetMapping("/profile")
    public Result<UserInfoVO> getProfile(HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            UserInfoVO userInfo = userService.getUserInfo(userId);
            return Result.success("获取用户信息成功", userInfo);
        } catch (Exception e) {
            return Result.error("获取用户信息失败，请稍后重试");
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/profile")
    public Result<Void> updateProfile(@Valid @RequestBody UserUpdateDTO dto,
                                     HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            userService.updateUserInfo(userId, dto);
            return Result.success("用户信息更新成功");
        } catch (RuntimeException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("更新失败，请稍后重试");
        }
    }

    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    public Result<Void> changePassword(@Valid @RequestBody ChangePasswordDTO dto,
                                      HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            userService.changePassword(userId, dto);
            return Result.success("密码修改成功");
        } catch (RuntimeException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("密码修改失败，请稍后重试");
        }
    }

    /**
     * 医生修改密码
     */
    @PostMapping("/doctor/change-password")
    public Result<Void> changeDoctorPassword(@Valid @RequestBody DoctorPasswordUpdateDTO dto,
                                            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            userService.changeDoctorPassword(userId, dto);
            return Result.success("医生密码修改成功");
        } catch (RuntimeException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("密码修改失败，请稍后重试");
        }
    }
}
