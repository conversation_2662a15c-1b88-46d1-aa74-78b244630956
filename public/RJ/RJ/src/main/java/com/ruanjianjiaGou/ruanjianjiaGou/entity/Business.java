package com.ruanjianjiaGou.ruanjianjiaGou.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.List;

@Data
public class Business {
    private Integer businessId;
    private String password;
    private String businessName;
    private String businessAddress;
    private Double XAddress;
    private Double YAddress;
    @JsonProperty("businessImg")
    private List<String> businessImgList;
    private BigDecimal starPrice;
    private BigDecimal deliveryPrice;
    private String businessExplain;
}
