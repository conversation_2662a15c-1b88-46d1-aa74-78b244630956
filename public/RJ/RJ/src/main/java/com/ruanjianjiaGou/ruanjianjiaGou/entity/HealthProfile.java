package com.ruanjianjiaGou.ruanjianjiaGou.entity;

import com.ruanjianjiaGou.ruanjianjiaGou.converter.GenderConverter;
import com.ruanjianjiaGou.ruanjianjiaGou.enums.Gender;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;

@Data
@Entity
@Table(name = "health_profiles")
public class HealthProfile {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "managing_user_id", nullable = false)
    private Long managingUserId;

    @Column(name = "profile_owner_name", nullable = false, length = 50)
    private String profileOwnerName;

    @Convert(converter = GenderConverter.class)
    @Column(name = "gender")
    private Gender gender;

    @Column(name = "birth_date")
    private LocalDate birthDate;

    @Column(name = "id_card_number", length = 25)
    private String idCardNumber;

    @Column(name = "avatar_url", length = 255)
    private String avatarUrl;

    @Column(name = "medical_history", columnDefinition = "TEXT")
    private String medicalHistory;

    // 计算年龄的方法
    public Integer getAge() {
        if (birthDate == null) {
            return null;
        }
        return Period.between(birthDate, LocalDate.now()).getYears();
    }
}
