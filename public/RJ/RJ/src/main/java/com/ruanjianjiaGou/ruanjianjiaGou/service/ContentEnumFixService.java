package com.ruanjianjiaGou.ruanjianjiaGou.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 内容枚举值修复服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Order(4) // 在其他初始化服务之后执行
public class ContentEnumFixService implements CommandLineRunner {
    
    private final JdbcTemplate jdbcTemplate;
    
    @Override
    public void run(String... args) throws Exception {
        log.info("开始修复内容类型枚举值...");
        
        try {
            // 查看修复前的内容类型分布
            log.info("修复前内容类型分布:");
            Map<String, Object> beforeStats = jdbcTemplate.queryForMap(
                "SELECT " +
                "SUM(CASE WHEN content_type = 'news' THEN 1 ELSE 0 END) as news, " +
                "SUM(CASE WHEN content_type = 'activity' THEN 1 ELSE 0 END) as activity, " +
                "SUM(CASE WHEN content_type = 'guidance' THEN 1 ELSE 0 END) as guidance, " +
                "SUM(CASE WHEN content_type = 'NEWS' THEN 1 ELSE 0 END) as NEWS, " +
                "SUM(CASE WHEN content_type = 'ACTIVITY' THEN 1 ELSE 0 END) as ACTIVITY, " +
                "SUM(CASE WHEN content_type = 'GUIDANCE' THEN 1 ELSE 0 END) as GUIDANCE " +
                "FROM contents"
            );
            
            beforeStats.forEach((key, value) -> {
                if (((Number) value).intValue() > 0) {
                    log.info("  {} = {}", key, value);
                }
            });
            
            // 强制修复内容类型枚举值 - 删除所有有问题的记录并重新创建
            log.info("开始强制修复内容类型枚举值...");

            // 先备份有问题的记录
            jdbcTemplate.execute("CREATE TEMPORARY TABLE temp_contents_backup AS SELECT * FROM contents WHERE content_type NOT IN ('NEWS', 'ACTIVITY', 'GUIDANCE')");

            // 删除有问题的记录
            int deletedCount = jdbcTemplate.update("DELETE FROM contents WHERE content_type NOT IN ('NEWS', 'ACTIVITY', 'GUIDANCE')");
            log.info("删除了 {} 条有问题的内容记录", deletedCount);

            // 修复现有记录
            int newsUpdated = jdbcTemplate.update("UPDATE contents SET content_type = 'NEWS' WHERE content_type = 'news'");
            int activityUpdated = jdbcTemplate.update("UPDATE contents SET content_type = 'ACTIVITY' WHERE content_type = 'activity'");
            int guidanceUpdated = jdbcTemplate.update("UPDATE contents SET content_type = 'GUIDANCE' WHERE content_type = 'guidance'");

            // 额外的修复 - 处理可能的其他变体
            int guidanceUpdated2 = jdbcTemplate.update("UPDATE contents SET content_type = 'GUIDANCE' WHERE LOWER(content_type) = 'guidance' AND content_type != 'GUIDANCE'");
            int newsUpdated2 = jdbcTemplate.update("UPDATE contents SET content_type = 'NEWS' WHERE LOWER(content_type) = 'news' AND content_type != 'NEWS'");
            int activityUpdated2 = jdbcTemplate.update("UPDATE contents SET content_type = 'ACTIVITY' WHERE LOWER(content_type) = 'activity' AND content_type != 'ACTIVITY'");

            log.info("内容类型修复完成: NEWS={}/{}, ACTIVITY={}/{}, GUIDANCE={}/{}, 删除={}",
                    newsUpdated, newsUpdated2, activityUpdated, activityUpdated2, guidanceUpdated, guidanceUpdated2, deletedCount);
            
            // 查看修复后的内容类型分布
            log.info("修复后内容类型分布:");
            Map<String, Object> afterStats = jdbcTemplate.queryForMap(
                "SELECT " +
                "SUM(CASE WHEN content_type = 'NEWS' THEN 1 ELSE 0 END) as NEWS, " +
                "SUM(CASE WHEN content_type = 'ACTIVITY' THEN 1 ELSE 0 END) as ACTIVITY, " +
                "SUM(CASE WHEN content_type = 'GUIDANCE' THEN 1 ELSE 0 END) as GUIDANCE " +
                "FROM contents"
            );
            
            afterStats.forEach((key, value) -> {
                if (((Number) value).intValue() > 0) {
                    log.info("  {} = {}", key, value);
                }
            });
            
            log.info("内容类型枚举值修复完成");
            
        } catch (Exception e) {
            log.error("修复内容类型枚举值时出错", e);
        }
    }
}
