package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.dto.HealthProfileCreateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.HealthProfileUpdateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.HealthProfile;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.HealthProfileRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Transactional
public class HealthProfileService {
    
    @Autowired
    private HealthProfileRepository profileRepository;
    
    /**
     * 创建健康档案
     */
    public HealthProfile createProfile(Long userId, HealthProfileCreateDTO dto) {
        HealthProfile profile = new HealthProfile();
        profile.setManagingUserId(userId);

        // 手动设置字段，确保字段名匹配
        profile.setProfileOwnerName(dto.getProfileOwnerName());
        profile.setGender(dto.getGender());
        profile.setBirthDate(dto.getBirthDate());
        profile.setIdCardNumber(dto.getIdCard()); // DTO中是idCard，实体中是idCardNumber
        profile.setMedicalHistory(dto.getMedicalHistory());
        // 注意：其他字段如phone, emergencyContact等在数据库表中不存在，暂时忽略

        return profileRepository.save(profile);
    }
    
    /**
     * 获取用户管理的所有档案
     */
    @Transactional(readOnly = true)
    public List<HealthProfile> getUserProfiles(Long userId) {
        return profileRepository.findByManagingUserId(userId);
    }

    /**
     * 获取用户管理的所有档案（分页）
     */
    @Transactional(readOnly = true)
    public Page<HealthProfile> getUserProfiles(Long userId, int page, int size) {
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "id"));
        return profileRepository.findByManagingUserId(userId, pageable);
    }
    
    /**
     * 获取档案详情（带权限验证）
     */
    @Transactional(readOnly = true)
    public HealthProfile getProfileById(Long userId, Long profileId) {
        return profileRepository.findByIdAndManagingUserId(profileId, userId)
                .orElseThrow(() -> new RuntimeException("健康档案不存在或无权访问"));
    }
    
    /**
     * 更新健康档案
     */
    public void updateProfile(Long userId, Long profileId, HealthProfileUpdateDTO dto) {
        HealthProfile profile = getProfileById(userId, profileId);
        
        // 更新非空字段
        if (dto.getProfileOwnerName() != null) {
            profile.setProfileOwnerName(dto.getProfileOwnerName());
        }
        if (dto.getGender() != null) {
            profile.setGender(dto.getGender());
        }
        if (dto.getBirthDate() != null) {
            profile.setBirthDate(dto.getBirthDate());
        }
        if (dto.getIdCard() != null) {
            profile.setIdCardNumber(dto.getIdCard());
        }
        if (dto.getMedicalHistory() != null) {
            profile.setMedicalHistory(dto.getMedicalHistory());
        }
        profileRepository.save(profile);
    }
    
    /**
     * 删除健康档案
     */
    public void deleteProfile(Long userId, Long profileId) {
        HealthProfile profile = getProfileById(userId, profileId);
        profileRepository.delete(profile);
    }
    
    /**
     * 验证档案权限（供其他服务调用）
     */
    @Transactional(readOnly = true)
    public void validateProfileAccess(Long userId, Long profileId) {
        getProfileById(userId, profileId);
    }
}
