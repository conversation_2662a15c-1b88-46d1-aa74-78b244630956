package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment.*;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.*;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 预约挂号服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AppointmentService {
    
    private final DepartmentRepository departmentRepository;
    private final DoctorRepository doctorRepository;
    private final DoctorScheduleRepository scheduleRepository;
    private final AppointmentRepository appointmentRepository;
    private final HealthProfileRepository healthProfileRepository;
    
    /**
     * 获取所有科室
     */
    public List<DepartmentDTO> getAllDepartments() {
        // 返回所有科室，而不是只返回有医生的科室
        return departmentRepository.findAllByOrderByName()
                .stream()
                .map(this::convertToDepartmentDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取科室下的医生列表
     */
    public Page<DoctorDTO> getDoctorsByDepartment(Long departmentId, int page, int size, String name) {
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("realName"));
        
        Page<Doctor> doctors;
        if (name != null && !name.trim().isEmpty()) {
            doctors = doctorRepository.findByDepartmentIdAndStatusAndRealNameContaining(
                departmentId, Doctor.DoctorStatus.APPROVED, name.trim(), pageable);
        } else {
            doctors = doctorRepository.findByDepartmentIdAndStatus(
                departmentId, Doctor.DoctorStatus.APPROVED, pageable);
        }
        
        return doctors.map(this::convertToDoctorDTO);
    }
    
    /**
     * 搜索医生
     */
    public Page<DoctorDTO> searchDoctors(String name, Long departmentId, int page, int size) {
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("realName"));
        
        Page<Doctor> doctors;
        if (departmentId != null && name != null && !name.trim().isEmpty()) {
            doctors = doctorRepository.findByDepartmentIdAndStatusAndRealNameContaining(
                departmentId, Doctor.DoctorStatus.APPROVED, name.trim(), pageable);
        } else if (departmentId != null) {
            doctors = doctorRepository.findByDepartmentIdAndStatus(
                departmentId, Doctor.DoctorStatus.APPROVED, pageable);
        } else if (name != null && !name.trim().isEmpty()) {
            doctors = doctorRepository.findByStatusAndRealNameContaining(
                Doctor.DoctorStatus.APPROVED, name.trim(), pageable);
        } else {
            // 临时解决方案：使用原生查询处理状态值问题
            try {
                doctors = doctorRepository.findByStatus(Doctor.DoctorStatus.APPROVED, pageable);
            } catch (Exception e) {
                // 如果枚举值有问题，返回空结果
                log.warn("查询医生时遇到状态值问题: {}", e.getMessage());
                doctors = Page.empty(pageable);
            }
        }
        
        return doctors.map(this::convertToDoctorDTO);
    }
    
    /**
     * 获取医生详情
     */
    public DoctorDTO getDoctorDetail(Long doctorId) {
        Doctor doctor = doctorRepository.findById(doctorId)
                .orElseThrow(() -> new RuntimeException("医生不存在"));
        
        if (doctor.getStatus() != Doctor.DoctorStatus.APPROVED) {
            throw new RuntimeException("医生未通过审核");
        }
        
        return convertToDoctorDTO(doctor);
    }
    
    /**
     * 获取医生的可预约排班
     */
    public List<DoctorScheduleDTO> getDoctorSchedules(Long doctorId, LocalDate startDate, LocalDate endDate) {
        if (startDate == null) {
            startDate = LocalDate.now();
        }
        if (endDate == null) {
            endDate = startDate.plusDays(30); // 默认查询30天内的排班
        }
        
        List<DoctorSchedule> schedules = scheduleRepository.findAvailableSchedules(doctorId, startDate, endDate);
        return schedules.stream()
                .map(this::convertToScheduleDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 创建预约
     */
    @Transactional
    public AppointmentDTO createAppointment(Long userId, CreateAppointmentDTO createDTO) {
        log.info("创建预约: userId={}, profileId={}, scheduleId={}", userId, createDTO.getProfileId(), createDTO.getScheduleId());

        // 验证健康档案权限
        HealthProfile profile = healthProfileRepository.findByIdAndManagingUserId(createDTO.getProfileId(), userId)
                .orElseThrow(() -> {
                    log.error("健康档案验证失败: profileId={}, userId={}", createDTO.getProfileId(), userId);
                    // 检查档案是否存在
                    boolean profileExists = healthProfileRepository.existsById(createDTO.getProfileId());
                    if (!profileExists) {
                        return new RuntimeException("健康档案不存在，请先创建健康档案");
                    } else {
                        return new RuntimeException("无权限访问该健康档案");
                    }
                });

        // 验证排班
        DoctorSchedule schedule = scheduleRepository.findById(createDTO.getScheduleId())
                .orElseThrow(() -> new RuntimeException("排班不存在"));

        // 检查是否还有可预约号源
        if (!schedule.hasAvailableSlots()) {
            throw new RuntimeException("该时段已满，无法预约");
        }

        // 检查用户是否已在该时段预约
        if (appointmentRepository.existsByUserIdAndScheduleIdAndStatus(
                userId, createDTO.getScheduleId(), Appointment.AppointmentStatus.BOOKED)) {
            throw new RuntimeException("您已在该时段预约，请勿重复预约");
        }
        
        // 创建预约记录
        Appointment appointment = new Appointment();
        appointment.setUserId(userId);
        appointment.setScheduleId(createDTO.getScheduleId());
        appointment.setProfileId(createDTO.getProfileId());
        appointment.setNotes(createDTO.getNotes());
        appointment.setStatus(Appointment.AppointmentStatus.BOOKED);

        // 保存预约记录
        appointment = appointmentRepository.save(appointment);
        log.info("预约记录已保存，ID: {}", appointment.getId());

        // 更新排班的已预约数量
        schedule.bookSlot();
        scheduleRepository.save(schedule);
        log.info("排班号源已更新，剩余: {}", schedule.getAvailableSlots());

        // 强制刷新到数据库
        appointmentRepository.flush();
        scheduleRepository.flush();

        log.info("用户 {} 成功预约排班 {}，预约ID: {}", userId, createDTO.getScheduleId(), appointment.getId());

        return convertToAppointmentDTO(appointment);
    }
    
    /**
     * 取消预约
     */
    @Transactional
    public void cancelAppointment(Long userId, Long appointmentId) {
        log.info("取消预约: userId={}, appointmentId={}", userId, appointmentId);

        // 先检查预约是否存在
        if (!appointmentRepository.existsById(appointmentId)) {
            log.error("预约记录不存在: appointmentId={}", appointmentId);
            throw new RuntimeException("预约记录不存在");
        }

        // 查找预约记录
        Appointment appointment = appointmentRepository.findByIdAndUserId(appointmentId, userId)
                .orElseThrow(() -> {
                    log.error("预约记录权限验证失败: appointmentId={}, userId={}", appointmentId, userId);
                    return new RuntimeException("无权限访问该预约记录");
                });

        log.info("找到预约记录: id={}, status={}, userId={}", appointment.getId(), appointment.getStatus(), appointment.getUserId());

        if (!appointment.canCancel()) {
            log.error("预约无法取消: appointmentId={}, status={}", appointmentId, appointment.getStatus());
            throw new RuntimeException("该预约无法取消，当前状态：" + appointment.getStatus().getDescription());
        }

        // 更新预约状态
        appointment.cancel();
        appointment = appointmentRepository.save(appointment);
        log.info("预约状态已更新为: {}", appointment.getStatus());

        // 释放排班号源
        DoctorSchedule schedule = scheduleRepository.findById(appointment.getScheduleId())
                .orElseThrow(() -> new RuntimeException("排班不存在"));
        schedule.cancelSlot();
        scheduleRepository.save(schedule);
        log.info("排班号源已释放，剩余: {}", schedule.getAvailableSlots());

        // 强制刷新到数据库
        appointmentRepository.flush();
        scheduleRepository.flush();

        log.info("用户 {} 成功取消预约 {}，状态: {}", userId, appointmentId, appointment.getStatus());
    }
    
    /**
     * 获取用户的预约列表
     */
    public Page<AppointmentDTO> getUserAppointments(Long userId, String status, int page, int size) {
        Pageable pageable = PageRequest.of(page - 1, size,
                Sort.by(Sort.Direction.DESC, "createdAt"));

        Page<Appointment> appointments;
        if (status != null && !status.trim().isEmpty()) {
            Appointment.AppointmentStatus appointmentStatus = Appointment.AppointmentStatus.valueOf(status.toUpperCase());
            appointments = appointmentRepository.findByUserIdAndStatus(userId, appointmentStatus, pageable);
        } else {
            appointments = appointmentRepository.findByUserId(userId, pageable);
        }

        return appointments.map(this::convertToAppointmentDTO);
    }

    /**
     * 获取指定日期的可预约排班
     */
    public List<DoctorScheduleDTO> getAvailableSchedules(LocalDate date, Long departmentId) {
        List<DoctorSchedule> schedules;

        if (departmentId != null) {
            // 如果指定了科室，需要通过医生关联查询
            schedules = scheduleRepository.findAvailableSchedulesByDate(date)
                    .stream()
                    .filter(schedule -> {
                        Doctor doctor = doctorRepository.findById(schedule.getDoctorId()).orElse(null);
                        return doctor != null && doctor.getDepartmentId().equals(departmentId);
                    })
                    .collect(Collectors.toList());
        } else {
            schedules = scheduleRepository.findAvailableSchedulesByDate(date);
        }

        return schedules.stream()
                .map(this::convertToScheduleDTO)
                .collect(Collectors.toList());
    }

    /**
     * 获取预约详情
     */
    public AppointmentDTO getAppointmentDetail(Long userId, Long appointmentId) {
        log.info("获取预约详情: userId={}, appointmentId={}", userId, appointmentId);

        // 先检查预约是否存在
        if (!appointmentRepository.existsById(appointmentId)) {
            log.error("预约记录不存在: appointmentId={}", appointmentId);
            throw new RuntimeException("预约记录不存在");
        }

        Appointment appointment = appointmentRepository.findByIdAndUserId(appointmentId, userId)
                .orElseThrow(() -> {
                    log.error("预约详情权限验证失败: appointmentId={}, userId={}", appointmentId, userId);
                    return new RuntimeException("无权限访问该预约记录");
                });

        log.info("成功获取预约详情: id={}, status={}", appointment.getId(), appointment.getStatus());
        return convertToAppointmentDTO(appointment);
    }

    /**
     * 获取即将到来的预约
     */
    public List<AppointmentDTO> getUpcomingAppointments(Long userId) {
        List<Appointment> appointments = appointmentRepository.findUpcomingAppointments(userId);
        return appointments.stream()
                .map(this::convertToAppointmentDTO)
                .collect(Collectors.toList());
    }

    /**
     * 获取科室统计信息
     */
    public DepartmentStatsDTO getDepartmentStats(Long departmentId) {
        Department department = departmentRepository.findById(departmentId)
                .orElseThrow(() -> new RuntimeException("科室不存在"));

        DepartmentStatsDTO stats = new DepartmentStatsDTO();
        stats.setDepartmentId(departmentId);
        stats.setDepartmentName(department.getName());

        // 统计医生数量
        stats.setTotalDoctors(doctorRepository.countByDepartmentId(departmentId));
        stats.setApprovedDoctors(doctorRepository.countByDepartmentIdAndStatus(departmentId, Doctor.DoctorStatus.APPROVED));

        // 统计今日排班和号源
        LocalDate today = LocalDate.now();
        List<Doctor> doctors = doctorRepository.findByDepartmentIdAndStatus(departmentId, Doctor.DoctorStatus.APPROVED);

        long todaySchedules = 0;
        long todayAvailableSlots = 0;
        long todayBookedSlots = 0;

        for (Doctor doctor : doctors) {
            List<DoctorSchedule> schedules = scheduleRepository.findByDoctorIdAndScheduleDate(doctor.getUserId(), today);
            todaySchedules += schedules.size();
            for (DoctorSchedule schedule : schedules) {
                todayAvailableSlots += schedule.getAvailableSlots();
                todayBookedSlots += schedule.getBookedSlots();
            }
        }

        stats.setTodaySchedules(todaySchedules);
        stats.setTodayAvailableSlots(todayAvailableSlots);
        stats.setTodayBookedSlots(todayBookedSlots);

        // 统计预约数量（简化实现）
        stats.setTotalAppointments(0L);
        stats.setTodayAppointments(0L);
        stats.setAverageRating(0.0);

        return stats;
    }

    /**
     * 获取医生统计信息
     */
    public DoctorStatsDTO getDoctorStats(Long doctorId) {
        Doctor doctor = doctorRepository.findById(doctorId)
                .orElseThrow(() -> new RuntimeException("医生不存在"));

        DoctorStatsDTO stats = new DoctorStatsDTO();
        stats.setDoctorId(doctorId);
        stats.setDoctorName(doctor.getRealName());

        if (doctor.getDepartment() != null) {
            stats.setDepartmentName(doctor.getDepartment().getName());
        }

        // 统计排班数量
        stats.setTotalSchedules(scheduleRepository.countByDoctorId(doctorId));

        // 统计预约数量
        stats.setTotalAppointments(appointmentRepository.countByDoctorId(doctorId));
        stats.setCompletedAppointments(appointmentRepository.countByDoctorIdAndStatus(doctorId, Appointment.AppointmentStatus.COMPLETED));
        stats.setCancelledAppointments(appointmentRepository.countByDoctorIdAndStatus(doctorId, Appointment.AppointmentStatus.CANCELLED));

        // 统计今日数据
        LocalDate today = LocalDate.now();
        List<DoctorSchedule> todaySchedules = scheduleRepository.findByDoctorIdAndScheduleDate(doctorId, today);
        stats.setTodaySchedules((long) todaySchedules.size());

        long todayAvailable = 0;
        long todayBooked = 0;
        for (DoctorSchedule schedule : todaySchedules) {
            todayAvailable += schedule.getAvailableSlots();
            todayBooked += schedule.getBookedSlots();
        }

        stats.setTodayAvailableSlots(todayAvailable);
        stats.setTodayBookedSlots(todayBooked);
        stats.setTodayAppointments(todayBooked);

        // 计算预约率
        long totalSlots = todayAvailable + todayBooked;
        if (totalSlots > 0) {
            stats.setAppointmentRate((double) todayBooked / totalSlots * 100);
        } else {
            stats.setAppointmentRate(0.0);
        }

        stats.setAverageRating(0.0); // 简化实现

        return stats;
    }
    
    // 转换方法
    private DepartmentDTO convertToDepartmentDTO(Department department) {
        long doctorCount = doctorRepository.countByDepartmentIdAndStatus(
                department.getId(), Doctor.DoctorStatus.APPROVED);
        return new DepartmentDTO(department.getId(), department.getName(),
                department.getDescription(), doctorCount);
    }
    
    private DoctorDTO convertToDoctorDTO(Doctor doctor) {
        DoctorDTO dto = new DoctorDTO();
        dto.setId(doctor.getUserId());
        dto.setUserId(doctor.getUserId());
        dto.setRealName(doctor.getRealName());
        dto.setTitle(doctor.getTitle());
        dto.setSpecialty(doctor.getSpecialty());
        dto.setBio(doctor.getBio());
        dto.setStatus(doctor.getStatus().name());
        dto.setDepartmentId(doctor.getDepartmentId());
        
        // 设置科室名称
        if (doctor.getDepartment() != null) {
            dto.setDepartmentName(doctor.getDepartment().getName());
        }
        
        return dto;
    }
    
    private DoctorScheduleDTO convertToScheduleDTO(DoctorSchedule schedule) {
        DoctorScheduleDTO dto = new DoctorScheduleDTO();
        dto.setId(schedule.getId());
        dto.setDoctorId(schedule.getDoctorId());
        dto.setScheduleDate(schedule.getScheduleDate());
        dto.setStartTime(schedule.getStartTime());
        dto.setEndTime(schedule.getEndTime());
        dto.setTotalSlots(schedule.getTotalSlots());
        dto.setBookedSlots(schedule.getBookedSlots());
        dto.setAvailableSlots(schedule.getAvailableSlots());
        dto.setIsAvailable(schedule.hasAvailableSlots());
        
        // 设置医生和科室信息
        if (schedule.getDoctor() != null) {
            dto.setDoctorName(schedule.getDoctor().getRealName());
            if (schedule.getDoctor().getDepartment() != null) {
                dto.setDepartmentName(schedule.getDoctor().getDepartment().getName());
            }
        }
        
        return dto;
    }
    
    private AppointmentDTO convertToAppointmentDTO(Appointment appointment) {
        AppointmentDTO dto = new AppointmentDTO();
        dto.setId(appointment.getId());
        dto.setUserId(appointment.getUserId());
        dto.setStatus(appointment.getStatus().name());
        dto.setStatusDescription(appointment.getStatus().getDescription());
        dto.setCreatedAt(appointment.getCreatedAt());
        dto.setNotes(appointment.getNotes());
        dto.setCanCancel(appointment.canCancel());
        dto.setCanComplete(appointment.getStatus() == Appointment.AppointmentStatus.BOOKED);
        
        // 设置排班信息
        if (appointment.getSchedule() != null) {
            DoctorSchedule schedule = appointment.getSchedule();
            dto.setScheduleId(schedule.getId());
            dto.setAppointmentDate(schedule.getScheduleDate());
            dto.setAppointmentTime(schedule.getStartTime());
            dto.setEndTime(schedule.getEndTime());
            
            // 设置医生信息
            if (schedule.getDoctor() != null) {
                Doctor doctor = schedule.getDoctor();
                dto.setDoctorId(doctor.getUserId());
                dto.setDoctorName(doctor.getRealName());
                dto.setDoctorTitle(doctor.getTitle());
                
                if (doctor.getDepartment() != null) {
                    dto.setDepartmentName(doctor.getDepartment().getName());
                }
            }
        }
        
        // 设置就诊人信息
        if (appointment.getProfile() != null) {
            HealthProfile profile = appointment.getProfile();
            dto.setProfileId(profile.getId());
            dto.setProfileOwnerName(profile.getProfileOwnerName());
            dto.setProfileGender(null); // 新数据库结构中暂时设为null
        }
        
        return dto;
    }
}
