package com.ruanjianjiaGou.ruanjianjiaGou.entity;

import com.ruanjianjiaGou.ruanjianjiaGou.converter.DoctorStatusConverter;
import com.ruanjianjiaGou.ruanjianjiaGou.converter.GenderConverter;
import com.ruanjianjiaGou.ruanjianjiaGou.enums.Gender;
import lombok.Data;
import javax.persistence.*;
import java.time.LocalDate;
import java.time.Period;

@Data
@Entity
@Table(name = "doctors")
public class Doctor {
    @Id
    @Column(name = "user_id")
    private Long userId;

    @OneToOne
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;

    @Column(name = "real_name", nullable = false, length = 50)
    private String realName;

    @Convert(converter = GenderConverter.class)
    @Column(name = "gender")
    private Gender gender;

    @Column(name = "birth_date")
    private LocalDate birthDate;

    @Column(name = "id_card_number", length = 25)
    private String idCardNumber;

    @Column(name = "avatar_url", length = 255)
    private String avatarUrl;

    @Column(name = "department_id")
    private Long departmentId;

    @ManyToOne
    @JoinColumn(name = "department_id", insertable = false, updatable = false)
    private Department department;

    @Column(name = "title", length = 50)
    private String title;

    @Column(name = "specialty", columnDefinition = "TEXT")
    private String specialty;

    @Column(name = "bio", columnDefinition = "TEXT")
    private String bio;

    @Convert(converter = DoctorStatusConverter.class)
    @Column(name = "status", nullable = false)
    private DoctorStatus status = DoctorStatus.PENDING;

    /**
     * 计算年龄
     */
    public Integer getAge() {
        if (birthDate == null) {
            return null;
        }
        return Period.between(birthDate, LocalDate.now()).getYears();
    }

    public enum DoctorStatus {
        PENDING("待审核"),
        APPROVED("已通过"),
        REJECTED("已拒绝");

        private final String description;

        DoctorStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
