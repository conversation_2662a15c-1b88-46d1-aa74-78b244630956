package com.ruanjianjiaGou.ruanjianjiaGou.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 内容实体类
 */
@Entity
@Table(name = "contents", indexes = {
    @Index(name = "idx_author_id", columnList = "author_id"),
    @Index(name = "idx_content_type", columnList = "content_type"),
    @Index(name = "idx_published_at", columnList = "published_at")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Content {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "author_id", nullable = false)
    private Long authorId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "author_id", insertable = false, updatable = false)
    private User author;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "content_type", nullable = false)
    private ContentType contentType;
    
    @Column(name = "title", nullable = false)
    private String title;
    
    @Column(name = "body", columnDefinition = "TEXT")
    private String body;
    
    @Column(name = "published_at")
    private LocalDateTime publishedAt;
    
    @Column(name = "activity_time")
    private LocalDateTime activityTime;
    
    @Column(name = "activity_location")
    private String activityLocation;
    
    /**
     * 内容类型枚举
     */
    public enum ContentType {
        NEWS("新闻"),
        ACTIVITY("活动"),
        GUIDANCE("健康指导");
        
        private final String description;
        
        ContentType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
