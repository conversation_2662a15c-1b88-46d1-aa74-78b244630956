package com.ruanjianjiaGou.ruanjianjiaGou.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

/**
 * 数据初始化器
 * 在应用启动时初始化测试数据
 */
@Component
@Slf4j
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始执行DataInitializer...");
        initializeTestData();
    }

    private void initializeTestData() {
        try {
            log.info("开始初始化测试数据...");

            // 检查是否已经有数据，如果有则跳过初始化
            Integer userCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM users", Integer.class);
            if (userCount != null && userCount > 0) {
                log.info("数据库中已存在用户数据，跳过初始化");
                return;
            }

            // 执行新的数据库初始化脚本
            log.info("执行新的数据库初始化脚本...");

            // 读取并执行init-mysql.sql脚本
            String initScript = """
                -- 使用目标数据库
                USE `community_health_db`;
                SET NAMES utf8mb4;

                -- 暂时禁用外键检查
                SET FOREIGN_KEY_CHECKS=0;

                -- 清空所有相关表
                TRUNCATE TABLE `activity_registrations`;
                TRUNCATE TABLE `contents`;
                TRUNCATE TABLE `e_prescriptions`;
                TRUNCATE TABLE `messages`;
                TRUNCATE TABLE `online_consultations`;
                TRUNCATE TABLE `appointments`;
                TRUNCATE TABLE `doctor_schedules`;
                TRUNCATE TABLE `health_reminders`;
                TRUNCATE TABLE `health_metric_records`;
                TRUNCATE TABLE `health_profiles`;
                TRUNCATE TABLE `doctors`;
                TRUNCATE TABLE `users`;
                TRUNCATE TABLE `departments`;

                -- 重新开启外键检查
                SET FOREIGN_KEY_CHECKS=1;

                -- 填充科室数据
                INSERT INTO `departments` (`id`, `name`) VALUES
                    (1, '内科'),
                    (2, '外科'),
                    (3, '儿科'),
                    (4, '皮肤科'),
                    (5, '中医科');

                -- 填充用户数据（包含角色）
                INSERT INTO `users` (`id`, `phone_number`, `password`, `nickname`, `role`) VALUES
                    -- 5个居民用户
                    (1, '13800000001', '123456', '张三', 'resident'),
                    (2, '13800000002', '123456', '李四', 'resident'),
                    (3, '13800000003', '123456', '王五', 'resident'),
                    (4, '13800000004', '123456', '赵六', 'resident'),
                    (5, '13800000005', '123456', '孙七', 'resident'),

                    -- 4个医生用户
                    (6, '18610001001', 'doctor666', '王医生', 'doctor'),
                    (7, '18610001002', 'doctor666', '刘医生', 'doctor'),
                    (8, '18610001003', 'doctor666', '陈医生', 'doctor'),
                    (9, '18610001004', 'doctor666', '林医生', 'doctor'),

                    -- 1个管理员用户
                    (10, '19999999999', 'admin888', '系统管理员', 'admin');

                -- 填充医生信息
                INSERT INTO `doctors` (`user_id`, `real_name`, `department_id`, `title`, `specialty`, `bio`, `status`) VALUES
                    (6, '王健康', 1, '主任医师', '心脑血管疾病、内科杂症', '医术精湛，擅长各种内科疑难杂症的诊断与治疗。', 'approved'),
                    (7, '刘平安', 2, '副主任医师', '普外科、创伤急救', '拥有丰富的外科手术经验，尤其擅长微创手术。', 'approved'),
                    (8, '陈爱婴', 3, '主治医师', '小儿常见病、新生儿护理', '对儿科呼吸系统、消化系统疾病有深入研究，深受家长信赖。', 'approved'),
                    (9, '林之善', 4, '医师', '各种皮肤病、医学美容', '致力于将传统疗法与现代皮肤科治疗技术相结合。', 'pending');
                """;

            // 分割并执行SQL语句
            String[] statements = initScript.split(";");
            for (String statement : statements) {
                statement = statement.trim();
                if (!statement.isEmpty() && !statement.startsWith("--")) {
                    try {
                        jdbcTemplate.execute(statement);
                    } catch (Exception e) {
                        log.warn("执行SQL语句失败: {}, 错误: {}", statement, e.getMessage());
                    }
                }
            }

            log.info("✅ 测试数据初始化完成！");

        } catch (Exception e) {
            log.error("❌ 数据初始化失败: " + e.getMessage(), e);
            // 不抛出异常，避免影响应用启动
        }
    }
}
