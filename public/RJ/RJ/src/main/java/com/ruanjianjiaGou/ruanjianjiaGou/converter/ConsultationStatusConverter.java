package com.ruanjianjiaGou.ruanjianjiaGou.converter;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.OnlineConsultation;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * 问诊状态枚举转换器
 * 将数据库中的小写下划线格式转换为Java枚举
 */
@Converter(autoApply = true)
public class ConsultationStatusConverter implements AttributeConverter<OnlineConsultation.ConsultationStatus, String> {

    @Override
    public String convertToDatabaseColumn(OnlineConsultation.ConsultationStatus attribute) {
        if (attribute == null) {
            return null;
        }
        // 将枚举转换为大写格式存储到数据库
        return attribute.name();
    }

    @Override
    public OnlineConsultation.ConsultationStatus convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.trim().isEmpty()) {
            return null;
        }
        
        // 将数据库中的字符串转换为枚举
        String normalizedData = dbData.trim().toLowerCase();
        
        switch (normalizedData) {
            case "in_progress":
                return OnlineConsultation.ConsultationStatus.IN_PROGRESS;
            case "completed":
                return OnlineConsultation.ConsultationStatus.COMPLETED;
            default:
                // 如果是大写的枚举名称，也要处理
                try {
                    return OnlineConsultation.ConsultationStatus.valueOf(dbData.toUpperCase());
                } catch (IllegalArgumentException e) {
                    throw new IllegalArgumentException("Unknown consultation status: " + dbData);
                }
        }
    }
}
