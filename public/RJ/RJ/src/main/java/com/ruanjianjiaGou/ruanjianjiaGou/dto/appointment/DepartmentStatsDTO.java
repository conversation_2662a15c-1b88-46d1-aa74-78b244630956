package com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 科室统计DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentStatsDTO {
    
    private Long departmentId;
    private String departmentName;
    private Long totalDoctors;           // 总医生数
    private Long approvedDoctors;        // 已审核医生数
    private Long todaySchedules;         // 今日排班数
    private Long todayAvailableSlots;    // 今日可预约号源
    private Long todayBookedSlots;       // 今日已预约号源
    private Long totalAppointments;      // 总预约数
    private Long todayAppointments;      // 今日预约数
    private Double averageRating;        // 平均评分
}
