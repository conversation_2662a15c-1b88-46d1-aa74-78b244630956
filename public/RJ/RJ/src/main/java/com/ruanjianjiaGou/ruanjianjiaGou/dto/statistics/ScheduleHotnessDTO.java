package com.ruanjianjiaGou.ruanjianjiaGou.dto.statistics;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 预约时间段分析DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleHotnessDTO {
    
    /**
     * 时间段热度数据列表
     */
    private List<TimeSlotData> timeSlotData;
    
    /**
     * 时间段数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeSlotData {
        
        /**
         * 时间段描述（如：08-10点）
         */
        private String timeSlot;
        
        /**
         * 开始小时
         */
        private Integer startHour;
        
        /**
         * 结束小时
         */
        private Integer endHour;
        
        /**
         * 预约数量
         */
        private Long appointmentCount;
        
        /**
         * 热度百分比
         */
        private Double hotnessPercentage;
    }
}
