package com.ruanjianjiaGou.ruanjianjiaGou.converter;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.Appointment;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * 预约状态转换器
 * 处理数据库中小写状态值与Java枚举的转换
 */
@Converter(autoApply = true)
public class AppointmentStatusConverter implements AttributeConverter<Appointment.AppointmentStatus, String> {

    @Override
    public String convertToDatabaseColumn(Appointment.AppointmentStatus attribute) {
        if (attribute == null) {
            return null;
        }
        // 存储为大写格式
        return attribute.name();
    }

    @Override
    public Appointment.AppointmentStatus convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.trim().isEmpty()) {
            return null;
        }
        
        // 将数据库中的字符串转换为枚举
        String normalizedData = dbData.trim().toLowerCase();
        
        switch (normalizedData) {
            case "booked":
                return Appointment.AppointmentStatus.BOOKED;
            case "completed":
                return Appointment.AppointmentStatus.COMPLETED;
            case "cancelled":
                return Appointment.AppointmentStatus.CANCELLED;
            default:
                // 如果是大写的枚举名称，也要处理
                try {
                    return Appointment.AppointmentStatus.valueOf(dbData.toUpperCase());
                } catch (IllegalArgumentException e) {
                    throw new IllegalArgumentException("Unknown appointment status: " + dbData);
                }
        }
    }
}
