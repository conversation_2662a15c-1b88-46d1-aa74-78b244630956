package com.ruanjianjiaGou.ruanjianjiaGou.entity;

import com.ruanjianjiaGou.ruanjianjiaGou.converter.AppointmentStatusConverter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 预约记录实体类
 */
@Entity
@Table(name = "appointments", indexes = {
    @Index(name = "idx_user_id", columnList = "user_id"),
    @Index(name = "idx_schedule_id", columnList = "schedule_id"),
    @Index(name = "idx_profile_id", columnList = "profile_id"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Appointment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
    
    @Column(name = "schedule_id", nullable = false)
    private Long scheduleId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "schedule_id", insertable = false, updatable = false)
    private DoctorSchedule schedule;
    
    @Column(name = "profile_id", nullable = false)
    private Long profileId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "profile_id", insertable = false, updatable = false)
    private HealthProfile profile;
    
    @Convert(converter = AppointmentStatusConverter.class)
    @Column(name = "status", nullable = false)
    private AppointmentStatus status = AppointmentStatus.BOOKED;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes; // 预约备注
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (status == null) {
            status = AppointmentStatus.BOOKED;
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 预约状态枚举
     */
    public enum AppointmentStatus {
        BOOKED("已预约"),
        COMPLETED("已完成"),
        CANCELLED("已取消");
        
        private final String description;
        
        AppointmentStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 检查预约是否可以取消
     */
    public boolean canCancel() {
        return status == AppointmentStatus.BOOKED;
    }
    
    /**
     * 取消预约
     */
    public void cancel() {
        if (canCancel()) {
            status = AppointmentStatus.CANCELLED;
        }
    }
    
    /**
     * 完成预约
     */
    public void complete() {
        if (status == AppointmentStatus.BOOKED) {
            status = AppointmentStatus.COMPLETED;
        }
    }
}
