package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.prescription.*;
import com.ruanjianjiaGou.ruanjianjiaGou.service.PrescriptionService;
import com.ruanjianjiaGou.ruanjianjiaGou.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Min;

/**
 * 电子处方控制器
 */
@RestController
@RequestMapping("/api")
@Slf4j
@Validated
public class PrescriptionController {
    
    @Autowired
    private PrescriptionService prescriptionService;
    
    @Autowired
    private SecurityUtils securityUtils;
    
    /**
     * 开具电子处方
     * 功能: 医生为患者开具一张新的电子处方
     * 权限: 医生 (Doctor)
     */
    @PostMapping("/prescriptions")
    public Result<PrescriptionListDTO> createPrescription(@Valid @RequestBody PrescriptionCreateDTO createDTO, HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            String userRole = securityUtils.getCurrentUser(request).getRole().name();
            
            // 只有医生可以开具处方
            if (!"DOCTOR".equals(userRole)) {
                return Result.error("只有医生可以开具处方");
            }
            
            PrescriptionListDTO result = prescriptionService.createPrescription(userId, createDTO);
            return Result.success("处方开具成功", result);
        } catch (Exception e) {
            log.error("开具处方失败", e);
            return Result.error("开具处方失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取患者的处方列表
     * 功能: 查看某个患者的所有历史处方记录
     * 权限: 该健康档案的管理人，或为该患者服务过的医生
     */
    @GetMapping("/profiles/{profileId}/prescriptions")
    public Result<Page<PrescriptionListDTO>> getPatientPrescriptions(
            @PathVariable Long profileId,
            @RequestParam(defaultValue = "1") @Min(1) int page,
            @RequestParam(defaultValue = "10") @Min(1) int size,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            String userRole = securityUtils.getCurrentUser(request).getRole().name();
            
            Page<PrescriptionListDTO> result = prescriptionService.getPatientPrescriptions(profileId, userId, userRole, page, size);
            return Result.success("获取处方列表成功", result);
        } catch (Exception e) {
            log.error("获取处方列表失败", e);
            return Result.error("获取处方列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取医生开具的处方列表（额外功能）
     * 功能: 医生查看自己开具的所有处方记录
     * 权限: 医生 (Doctor)
     */
    @GetMapping("/prescriptions/doctor")
    public Result<Page<PrescriptionListDTO>> getDoctorPrescriptions(
            @RequestParam(defaultValue = "1") @Min(1) int page,
            @RequestParam(defaultValue = "10") @Min(1) int size,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            String userRole = securityUtils.getCurrentUser(request).getRole().name();
            
            // 只有医生可以查看自己的处方列表
            if (!"DOCTOR".equals(userRole)) {
                return Result.error("只有医生可以查看处方列表");
            }
            
            Page<PrescriptionListDTO> result = prescriptionService.getDoctorPrescriptions(userId, page, size);
            return Result.success("获取医生处方列表成功", result);
        } catch (Exception e) {
            log.error("获取医生处方列表失败", e);
            return Result.error("获取医生处方列表失败: " + e.getMessage());
        }
    }
}
