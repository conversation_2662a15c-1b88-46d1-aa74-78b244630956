package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.HealthRecordCreateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.HealthRecordQueryDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.HealthMetricRecord;
import com.ruanjianjiaGou.ruanjianjiaGou.service.HealthRecordService;
import com.ruanjianjiaGou.ruanjianjiaGou.utils.SecurityUtils;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.HealthRecordVO;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.HealthStatisticsVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/health/records")
public class HealthRecordController {
    
    @Autowired
    private HealthRecordService recordService;
    
    @Autowired
    private SecurityUtils securityUtils;
    
    /**
     * 添加健康数据记录
     */
    @PostMapping
    public Result<HealthRecordVO> addRecord(@Valid @RequestBody HealthRecordCreateDTO dto,
                                           HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            HealthMetricRecord record = recordService.addRecord(userId, dto);
            HealthRecordVO vo = convertToVO(record);
            return Result.success("健康数据记录成功", vo);
        } catch (RuntimeException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("记录失败，请稍后重试");
        }
    }
    
    /**
     * 获取健康数据记录列表
     */
    @GetMapping
    public Result<Map<String, Object>> getRecords(@Valid HealthRecordQueryDTO query,
                                                  HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            Page<HealthMetricRecord> page = recordService.getRecords(userId, query);
            
            List<HealthRecordVO> records = page.getContent().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
            
            Map<String, Object> result = new HashMap<>();
            result.put("records", records);
            
            Map<String, Object> pagination = new HashMap<>();
            pagination.put("currentPage", page.getNumber() + 1);
            pagination.put("totalPages", page.getTotalPages());
            pagination.put("totalRecords", page.getTotalElements());
            pagination.put("pageSize", page.getSize());
            result.put("pagination", pagination);
            
            return Result.success("获取成功", result);
        } catch (RuntimeException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("获取失败，请稍后重试");
        }
    }
    
    /**
     * 获取健康数据统计
     */
    @GetMapping("/statistics")
    public Result<HealthStatisticsVO> getStatistics(@RequestParam Long profileId,
                                                   @RequestParam String metricType,
                                                   @RequestParam(defaultValue = "month") String period,
                                                   HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            HealthStatisticsVO statistics = recordService.getStatistics(userId, profileId, metricType, period);
            return Result.success("获取成功", statistics);
        } catch (RuntimeException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("获取失败，请稍后重试");
        }
    }
    
    /**
     * 更新健康数据记录
     */
    @PutMapping("/{id}")
    public Result<Void> updateRecord(@PathVariable Long id,
                                    @Valid @RequestBody HealthRecordCreateDTO dto,
                                    HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            recordService.updateRecord(userId, id, dto);
            return Result.success("健康数据更新成功");
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            return Result.error("更新失败，请稍后重试");
        }
    }
    
    /**
     * 删除健康数据记录
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteRecord(@PathVariable Long id,
                                    HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            recordService.deleteRecord(userId, id);
            return Result.success("健康数据删除成功");
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            return Result.error("删除失败，请稍后重试");
        }
    }
    
    /**
     * 转换为VO对象
     */
    private HealthRecordVO convertToVO(HealthMetricRecord record) {
        HealthRecordVO vo = new HealthRecordVO();
        BeanUtils.copyProperties(record, vo);
        return vo;
    }
}
