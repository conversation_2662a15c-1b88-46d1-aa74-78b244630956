package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.dto.statistics.*;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.Appointment;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.Content;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.OnlineConsultation;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 医生统计分析服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DoctorStatisticsService {
    
    private final AppointmentRepository appointmentRepository;
    private final OnlineConsultationRepository consultationRepository;
    private final ContentRepository contentRepository;
    private final DoctorScheduleRepository scheduleRepository;
    private final HealthProfileRepository profileRepository;
    
    /**
     * 获取医生核心指标KPI
     */
    public DoctorKpiDTO getDoctorKpi(Long doctorId, String range) {
        DateRange dateRange = getDateRange(range);
        DateRange previousRange = getPreviousDateRange(range);
        
        // 1. 总服务人次（预约 + 在线问诊）
        long currentAppointmentCount = appointmentRepository.countByDoctorIdAndStatusAndCreatedAtBetween(
            doctorId, Appointment.AppointmentStatus.COMPLETED, dateRange.getStartTime(), dateRange.getEndTime());
        long currentConsultationCount = consultationRepository.countByDoctorIdAndStatusAndCreatedAtBetween(
            doctorId, OnlineConsultation.ConsultationStatus.COMPLETED, dateRange.getStartTime(), dateRange.getEndTime());
        long totalServiceCount = currentAppointmentCount + currentConsultationCount;
        
        // 上一周期对比
        long previousAppointmentCount = appointmentRepository.countByDoctorIdAndStatusAndCreatedAtBetween(
            doctorId, Appointment.AppointmentStatus.COMPLETED, previousRange.getStartTime(), previousRange.getEndTime());
        long previousConsultationCount = consultationRepository.countByDoctorIdAndStatusAndCreatedAtBetween(
            doctorId, OnlineConsultation.ConsultationStatus.COMPLETED, previousRange.getStartTime(), previousRange.getEndTime());
        long previousTotalServiceCount = previousAppointmentCount + previousConsultationCount;
        long totalServiceCountChange = totalServiceCount - previousTotalServiceCount;
        
        // 2. 预约完成率
        long completedAppointments = currentAppointmentCount;
        long totalAppointments = appointmentRepository.countByDoctorIdAndCreatedAtBetweenExcludingCancelled(
            doctorId, dateRange.getStartTime(), dateRange.getEndTime());
        double appointmentCompletionRate = totalAppointments > 0 ? 
            (double) completedAppointments / totalAppointments * 100 : 0.0;
        
        // 3. 新增患者数量
        long newPatientCount = calculateNewPatientCount(doctorId, dateRange);
        long previousNewPatientCount = calculateNewPatientCount(doctorId, previousRange);
        long newPatientCountChange = newPatientCount - previousNewPatientCount;
        
        // 4. 发布健康指南
        long publishedGuidanceCount = contentRepository.countByAuthorIdAndContentTypeAndPublishedAtBetween(
            doctorId, Content.ContentType.GUIDANCE, dateRange.getStartTime(), dateRange.getEndTime());
        
        // 本月发布的健康指南总数
        DateRange monthRange = getDateRange("month");
        long monthlyGuidanceCount = contentRepository.countByAuthorIdAndContentTypeAndPublishedAtBetween(
            doctorId, Content.ContentType.GUIDANCE, monthRange.getStartTime(), monthRange.getEndTime());
        
        return new DoctorKpiDTO(
            totalServiceCount,
            totalServiceCountChange,
            appointmentCompletionRate,
            completedAppointments,
            totalAppointments,
            newPatientCount,
            newPatientCountChange,
            publishedGuidanceCount,
            monthlyGuidanceCount
        );
    }
    
    /**
     * 获取服务量趋势
     */
    public ServiceTrendDTO getServiceTrend(Long doctorId, String range) {
        DateRange dateRange = getDateRange(range);
        
        // 获取预约数据（按日期分组）
        List<Object[]> appointmentData = appointmentRepository.countByDoctorIdAndStatusGroupByDate(
            doctorId, Appointment.AppointmentStatus.COMPLETED, dateRange.getStartTime(), dateRange.getEndTime());
        
        // 获取在线问诊数据（按日期分组）
        List<Object[]> consultationData = consultationRepository.countByDoctorIdAndStatusGroupByDate(
            doctorId, OnlineConsultation.ConsultationStatus.COMPLETED, dateRange.getStartTime(), dateRange.getEndTime());
        
        // 合并数据
        Map<LocalDate, Long> appointmentMap = appointmentData.stream()
            .collect(Collectors.toMap(
                row -> ((java.sql.Date) row[0]).toLocalDate(),
                row -> ((Number) row[1]).longValue()
            ));
        
        Map<LocalDate, Long> consultationMap = consultationData.stream()
            .collect(Collectors.toMap(
                row -> ((java.sql.Date) row[0]).toLocalDate(),
                row -> ((Number) row[1]).longValue()
            ));
        
        // 生成完整的日期范围数据
        List<ServiceTrendDTO.DailyServiceData> dailyData = new ArrayList<>();
        LocalDate currentDate = dateRange.getStartTime().toLocalDate();
        LocalDate endDate = dateRange.getEndTime().toLocalDate();
        
        while (!currentDate.isAfter(endDate)) {
            long appointmentCount = appointmentMap.getOrDefault(currentDate, 0L);
            long consultationCount = consultationMap.getOrDefault(currentDate, 0L);
            long totalCount = appointmentCount + consultationCount;
            
            dailyData.add(new ServiceTrendDTO.DailyServiceData(
                currentDate, appointmentCount, consultationCount, totalCount));
            
            currentDate = currentDate.plusDays(1);
        }
        
        return new ServiceTrendDTO(dailyData);
    }

    /**
     * 获取预约状态分配
     */
    public AppointmentStatusDTO getAppointmentStatus(Long doctorId, String range) {
        DateRange dateRange = getDateRange(range);

        // 统计各状态的预约数量
        long bookedCount = appointmentRepository.countByDoctorIdAndStatusAndCreatedAtBetween(
            doctorId, Appointment.AppointmentStatus.BOOKED, dateRange.getStartTime(), dateRange.getEndTime());
        long completedCount = appointmentRepository.countByDoctorIdAndStatusAndCreatedAtBetween(
            doctorId, Appointment.AppointmentStatus.COMPLETED, dateRange.getStartTime(), dateRange.getEndTime());
        long cancelledCount = appointmentRepository.countByDoctorIdAndStatusAndCreatedAtBetween(
            doctorId, Appointment.AppointmentStatus.CANCELLED, dateRange.getStartTime(), dateRange.getEndTime());

        long totalCount = bookedCount + completedCount + cancelledCount;

        List<AppointmentStatusDTO.StatusData> statusData = new ArrayList<>();
        if (totalCount > 0) {
            statusData.add(new AppointmentStatusDTO.StatusData(
                "BOOKED", "已预约", bookedCount, (double) bookedCount / totalCount * 100));
            statusData.add(new AppointmentStatusDTO.StatusData(
                "COMPLETED", "已完成", completedCount, (double) completedCount / totalCount * 100));
            statusData.add(new AppointmentStatusDTO.StatusData(
                "CANCELLED", "已取消", cancelledCount, (double) cancelledCount / totalCount * 100));
        }

        return new AppointmentStatusDTO(statusData);
    }

    /**
     * 获取高频服务患者排行
     */
    public TopPatientsDTO getTopPatients(Long doctorId, String range) {
        DateRange dateRange = getDateRange(range);

        // 这里需要复杂的查询来统计每个患者的服务次数
        // 暂时返回空数据，后续实现
        List<TopPatientsDTO.PatientData> patientData = new ArrayList<>();

        // TODO: 实现复杂的患者服务次数统计

        return new TopPatientsDTO(patientData);
    }

    /**
     * 获取预约时间段分析
     */
    public ScheduleHotnessDTO getScheduleHotness(Long doctorId, String range) {
        DateRange dateRange = getDateRange(range);

        // 查询预约数据并按时间段分组
        List<Object[]> hotnessData = appointmentRepository.countByDoctorIdAndTimeSlotGroupByHour(
            doctorId, dateRange.getStartTime(), dateRange.getEndTime());

        // 定义时间段
        Map<String, ScheduleHotnessDTO.TimeSlotData> timeSlotMap = new LinkedHashMap<>();
        timeSlotMap.put("08-10点", new ScheduleHotnessDTO.TimeSlotData("08-10点", 8, 10, 0L, 0.0));
        timeSlotMap.put("10-12点", new ScheduleHotnessDTO.TimeSlotData("10-12点", 10, 12, 0L, 0.0));
        timeSlotMap.put("14-16点", new ScheduleHotnessDTO.TimeSlotData("14-16点", 14, 16, 0L, 0.0));
        timeSlotMap.put("16-18点", new ScheduleHotnessDTO.TimeSlotData("16-18点", 16, 18, 0L, 0.0));

        // 处理查询结果
        long totalAppointments = 0;
        for (Object[] row : hotnessData) {
            int hour = ((Number) row[0]).intValue();
            long count = ((Number) row[1]).longValue();
            totalAppointments += count;

            // 根据小时分配到时间段
            String timeSlot = getTimeSlotByHour(hour);
            if (timeSlot != null && timeSlotMap.containsKey(timeSlot)) {
                ScheduleHotnessDTO.TimeSlotData data = timeSlotMap.get(timeSlot);
                data.setAppointmentCount(data.getAppointmentCount() + count);
            }
        }

        // 计算百分比
        if (totalAppointments > 0) {
            for (ScheduleHotnessDTO.TimeSlotData data : timeSlotMap.values()) {
                double percentage = (double) data.getAppointmentCount() / totalAppointments * 100;
                data.setHotnessPercentage(percentage);
            }
        }

        return new ScheduleHotnessDTO(new ArrayList<>(timeSlotMap.values()));
    }

    /**
     * 根据小时获取时间段
     */
    private String getTimeSlotByHour(int hour) {
        if (hour >= 8 && hour < 10) return "08-10点";
        if (hour >= 10 && hour < 12) return "10-12点";
        if (hour >= 14 && hour < 16) return "14-16点";
        if (hour >= 16 && hour < 18) return "16-18点";
        return null;
    }

    /**
     * 计算新增患者数量
     */
    private long calculateNewPatientCount(Long doctorId, DateRange dateRange) {
        // 这是一个复杂查询，需要找到在指定时间范围内首次接受该医生服务的患者
        // 暂时简化实现，后续可以优化
        return 0L; // TODO: 实现复杂的新增患者计算逻辑
    }
    
    /**
     * 根据范围获取日期范围
     */
    private DateRange getDateRange(String range) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime;
        LocalDateTime endTime = now;
        
        switch (range.toLowerCase()) {
            case "week":
                startTime = now.minusWeeks(1);
                break;
            case "month":
                startTime = now.minusMonths(1);
                break;
            case "quarter":
                startTime = now.minusMonths(3);
                break;
            default:
                startTime = now.minusMonths(1); // 默认一个月
        }
        
        return new DateRange(startTime, endTime);
    }
    
    /**
     * 获取上一个周期的日期范围
     */
    private DateRange getPreviousDateRange(String range) {
        DateRange current = getDateRange(range);
        long duration = java.time.Duration.between(current.getStartTime(), current.getEndTime()).toDays();
        
        LocalDateTime previousEndTime = current.getStartTime();
        LocalDateTime previousStartTime = previousEndTime.minusDays(duration);
        
        return new DateRange(previousStartTime, previousEndTime);
    }
    
    /**
     * 日期范围内部类
     */
    private static class DateRange {
        private final LocalDateTime startTime;
        private final LocalDateTime endTime;
        
        public DateRange(LocalDateTime startTime, LocalDateTime endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }
        
        public LocalDateTime getStartTime() {
            return startTime;
        }
        
        public LocalDateTime getEndTime() {
            return endTime;
        }
    }
}
