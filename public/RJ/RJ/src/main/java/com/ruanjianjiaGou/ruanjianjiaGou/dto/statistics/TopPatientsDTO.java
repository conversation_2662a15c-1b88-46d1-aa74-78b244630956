package com.ruanjianjiaGou.ruanjianjiaGou.dto.statistics;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 高频服务患者排行DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TopPatientsDTO {
    
    /**
     * 患者排行数据列表
     */
    private List<PatientData> patientData;
    
    /**
     * 患者数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PatientData {
        
        /**
         * 患者ID
         */
        private Long patientId;
        
        /**
         * 患者姓名
         */
        private String patientName;
        
        /**
         * 服务次数
         */
        private Long serviceCount;
        
        /**
         * 预约次数
         */
        private Long appointmentCount;
        
        /**
         * 在线问诊次数
         */
        private Long consultationCount;
    }
}
