package com.ruanjianjiaGou.ruanjianjiaGou.utils;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.User;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

@Component
public class SecurityUtils {
    
    @Autowired
    private JwtUtils jwtUtils;
    
    @Autowired
    private UserRepository userRepository;
    
    /**
     * 从请求中获取当前用户ID
     */
    public Long getCurrentUserId(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        String phoneNumber = jwtUtils.getPhoneNumberFromToken(token);
        
        User user = userRepository.findByPhoneNumber(phoneNumber)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        return user.getId();
    }
    
    /**
     * 从请求中获取当前用户信息
     */
    public User getCurrentUser(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        String phoneNumber = jwtUtils.getPhoneNumberFromToken(token);
        
        return userRepository.findByPhoneNumber(phoneNumber)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
    }
    
    /**
     * 从请求头获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        throw new RuntimeException("未找到有效的认证令牌");
    }
}
