package com.ruanjianjiaGou.ruanjianjiaGou.dto.statistics;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 医生核心指标DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DoctorKpiDTO {
    
    /**
     * 总服务人次
     */
    private Long totalServiceCount;
    
    /**
     * 总服务人次变化（与上一周期对比）
     */
    private Long totalServiceCountChange;
    
    /**
     * 预约完成率（百分比）
     */
    private Double appointmentCompletionRate;
    
    /**
     * 已完成预约数
     */
    private Long completedAppointments;
    
    /**
     * 总预约数（不包括取消的）
     */
    private Long totalAppointments;
    
    /**
     * 新增患者数量
     */
    private Long newPatientCount;
    
    /**
     * 新增患者数量变化（与上一周期对比）
     */
    private Long newPatientCountChange;
    
    /**
     * 发布健康指南数量
     */
    private Long publishedGuidanceCount;
    
    /**
     * 本月发布健康指南总数
     */
    private Long monthlyGuidanceCount;
}
