package com.ruanjianjiaGou.ruanjianjiaGou.dto.content;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.Content;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 创建内容请求DTO
 */
@Data
public class ContentCreateDTO {
    
    @NotNull(message = "内容类型不能为空")
    private Content.ContentType contentType;
    
    @NotBlank(message = "标题不能为空")
    private String title;
    
    @NotBlank(message = "内容不能为空")
    private String body;
    
    private LocalDateTime activityTime; // 活动时间（仅对活动类型有效）
    
    private String activityLocation; // 活动地点（仅对活动类型有效）
}
