package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.*;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

/**
 * 数据初始化服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Order(2) // 在DatabaseFixService之后执行
public class DataInitService implements CommandLineRunner {
    
    private final DepartmentRepository departmentRepository;
    private final DoctorRepository doctorRepository;
    private final DoctorScheduleRepository scheduleRepository;
    private final UserRepository userRepository;
    
    @Override
    @Transactional
    public void run(String... args) throws Exception {
        initTestData();
    }
    
    private void initTestData() {
        try {
            // 初始化科室数据
            initDepartments();
            
            // 初始化医生排班数据
            initDoctorSchedules();
            
            log.info("测试数据初始化完成");
        } catch (Exception e) {
            log.error("测试数据初始化失败", e);
        }
    }
    
    private void initDepartments() {
        List<String> departmentNames = Arrays.asList(
            "内科", "外科", "儿科", "妇产科", "眼科", "耳鼻喉科", "皮肤科", "心理科"
        );
        
        for (String name : departmentNames) {
            if (!departmentRepository.existsByName(name)) {
                Department department = new Department();
                department.setName(name);
                departmentRepository.save(department);
                log.info("创建科室: {}", name);
            }
        }
    }
    
    private void initDoctorSchedules() {
        try {
            // 暂时跳过医生排班初始化，避免枚举值问题
            log.info("跳过医生排班初始化，避免数据库状态值问题");
        } catch (Exception e) {
            log.warn("医生排班初始化失败，可能是数据库中存在旧的状态值: {}", e.getMessage());
            log.info("请执行SQL更新语句修复数据：UPDATE doctors SET status = 'APPROVED' WHERE status = 'approved'");
        }
    }
    
    private void createSchedulesForDoctor(Long doctorId) {
        LocalDate today = LocalDate.now();
        
        // 为未来7天创建排班
        for (int i = 1; i <= 7; i++) {
            LocalDate scheduleDate = today.plusDays(i);
            
            // 上午排班 9:00-12:00
            if (!hasScheduleConflict(doctorId, scheduleDate, LocalTime.of(9, 0), LocalTime.of(12, 0))) {
                createSchedule(doctorId, scheduleDate, LocalTime.of(9, 0), LocalTime.of(12, 0), 10);
            }
            
            // 下午排班 14:00-17:00
            if (!hasScheduleConflict(doctorId, scheduleDate, LocalTime.of(14, 0), LocalTime.of(17, 0))) {
                createSchedule(doctorId, scheduleDate, LocalTime.of(14, 0), LocalTime.of(17, 0), 8);
            }
        }
    }
    
    private boolean hasScheduleConflict(Long doctorId, LocalDate date, LocalTime startTime, LocalTime endTime) {
        return scheduleRepository.existsConflictingSchedule(doctorId, date, startTime, endTime);
    }
    
    private void createSchedule(Long doctorId, LocalDate date, LocalTime startTime, LocalTime endTime, int totalSlots) {
        DoctorSchedule schedule = new DoctorSchedule();
        schedule.setDoctorId(doctorId);
        schedule.setScheduleDate(date);
        schedule.setStartTime(startTime);
        schedule.setEndTime(endTime);
        schedule.setTotalSlots(totalSlots);
        schedule.setBookedSlots(0);
        
        scheduleRepository.save(schedule);
        log.info("为医生 {} 创建排班: {} {} - {}", doctorId, date, startTime, endTime);
    }
}
