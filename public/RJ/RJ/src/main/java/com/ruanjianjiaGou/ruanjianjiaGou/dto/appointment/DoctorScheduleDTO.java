package com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 医生排班DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DoctorScheduleDTO {
    
    private Long id;
    private Long doctorId;
    private String doctorName;
    private String departmentName;
    private LocalDate scheduleDate;
    private LocalTime startTime;
    private LocalTime endTime;
    private Integer totalSlots;
    private Integer bookedSlots;
    private Integer availableSlots;
    private Boolean isAvailable;
    
    public DoctorScheduleDTO(Long id, Long doctorId, LocalDate scheduleDate,
                           LocalTime startTime, LocalTime endTime,
                           Integer totalSlots, Integer bookedSlots) {
        this.id = id;
        this.doctorId = doctorId;
        this.scheduleDate = scheduleDate;
        this.startTime = startTime;
        this.endTime = endTime;
        this.totalSlots = totalSlots;
        this.bookedSlots = bookedSlots;
        this.availableSlots = totalSlots - bookedSlots;
        this.isAvailable = this.availableSlots > 0;
    }
}
