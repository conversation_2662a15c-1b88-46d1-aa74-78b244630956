package com.ruanjianjiaGou.ruanjianjiaGou.repository;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.EPrescription;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface EPrescriptionRepository extends JpaRepository<EPrescription, Long> {
    
    /**
     * 根据健康档案ID查找处方列表（分页）
     */
    Page<EPrescription> findByProfileIdOrderByCreatedAtDesc(Long profileId, Pageable pageable);
    
    /**
     * 根据医生ID查找处方列表（分页）
     */
    Page<EPrescription> findByDoctorIdOrderByCreatedAtDesc(Long doctorId, Pageable pageable);
    
    /**
     * 根据问诊ID查找处方
     */
    List<EPrescription> findByConsultationIdOrderByCreatedAtDesc(Long consultationId);
    
    /**
     * 查找医生为特定患者开具的处方
     */
    @Query("SELECT p FROM EPrescription p WHERE p.doctorId = :doctorId AND p.profileId = :profileId ORDER BY p.createdAt DESC")
    Page<EPrescription> findByDoctorIdAndProfileId(@Param("doctorId") Long doctorId, @Param("profileId") Long profileId, Pageable pageable);
    
    /**
     * 统计医生开具的处方总数
     */
    long countByDoctorId(Long doctorId);
    
    /**
     * 统计患者的处方总数
     */
    long countByProfileId(Long profileId);
    
    /**
     * 查找指定时间范围内的处方
     */
    List<EPrescription> findByCreatedAtBetweenOrderByCreatedAtDesc(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找医生在指定时间范围内开具的处方
     */
    List<EPrescription> findByDoctorIdAndCreatedAtBetweenOrderByCreatedAtDesc(
        Long doctorId, LocalDateTime startTime, LocalDateTime endTime);
}
