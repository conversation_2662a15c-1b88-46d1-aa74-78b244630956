package com.ruanjianjiaGou.ruanjianjiaGou.dto;

import com.ruanjianjiaGou.ruanjianjiaGou.enums.Gender;
import lombok.Data;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDate;

@Data
public class HealthProfileUpdateDTO {
    
    @Size(max = 50, message = "档案姓名长度不能超过50个字符")
    private String profileOwnerName;
    
    private Gender gender;
    
    private LocalDate birthDate;
    
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    private String idCard;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    @Size(max = 50, message = "紧急联系人姓名长度不能超过50个字符")
    private String emergencyContact;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "紧急联系人手机号格式不正确")
    private String emergencyPhone;
    
    private String medicalHistory;
    
    private String allergies;
    
    @Size(max = 10, message = "血型长度不能超过10个字符")
    private String bloodType;
    
    private Double height; // 身高(cm)
    
    private Double weight; // 体重(kg)
}
