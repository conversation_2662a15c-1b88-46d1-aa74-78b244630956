package com.ruanjianjiaGou.ruanjianjiaGou.dto.content;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.Content;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 内容列表DTO
 */
@Data
public class ContentListDTO {
    
    private Long id;
    private Long authorId;
    private String authorName;
    private Content.ContentType contentType;
    private String contentTypeDescription;
    private String title;
    private String summary; // 内容摘要（前100字符）
    private LocalDateTime publishedAt;
    private LocalDateTime activityTime;
    private String activityLocation;
    private Long registrationCount; // 报名人数（仅对活动类型有效）
    private Boolean isRegistered; // 当前用户是否已报名（仅对活动类型有效）
}
