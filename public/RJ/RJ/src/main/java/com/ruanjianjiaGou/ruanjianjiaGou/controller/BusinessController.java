package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.dto.BusinessBriefDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.service.BusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/business")
public class BusinessController {

    @Autowired
    private BusinessService businessService;

    @GetMapping("/brief-list")
    public ResponseEntity<List<BusinessBriefDTO>> getBusinessBriefList() {
        List<BusinessBriefDTO> businessList = businessService.getBusinessBriefList();
        return ResponseEntity.ok(businessList);
    }
}
