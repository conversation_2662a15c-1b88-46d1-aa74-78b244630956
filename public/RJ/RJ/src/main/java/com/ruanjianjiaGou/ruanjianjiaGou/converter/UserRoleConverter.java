package com.ruanjianjiaGou.ruanjianjiaGou.converter;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.User;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * 用户角色枚举转换器
 * 将数据库中的小写字符串转换为Java枚举
 */
@Converter(autoApply = true)
public class UserRoleConverter implements AttributeConverter<User.UserRole, String> {

    @Override
    public String convertToDatabaseColumn(User.UserRole attribute) {
        if (attribute == null) {
            return null;
        }
        // 将枚举转换为小写字符串存储到数据库
        return attribute.getCode();
    }

    @Override
    public User.UserRole convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.trim().isEmpty()) {
            return null;
        }
        
        // 将数据库中的字符串转换为枚举
        String normalizedData = dbData.trim().toLowerCase();
        
        switch (normalizedData) {
            case "resident":
                return User.UserRole.RESIDENT;
            case "doctor":
                return User.UserRole.DOCTOR;
            case "admin":
                return User.UserRole.ADMIN;
            default:
                // 如果是大写的枚举名称，也要处理
                try {
                    return User.UserRole.valueOf(dbData.toUpperCase());
                } catch (IllegalArgumentException e) {
                    throw new IllegalArgumentException("Unknown user role: " + dbData);
                }
        }
    }
}
