package com.ruanjianjiaGou.ruanjianjiaGou.dto;

import com.ruanjianjiaGou.ruanjianjiaGou.enums.FrequencyType;
import com.ruanjianjiaGou.ruanjianjiaGou.enums.ReminderType;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;

@Data
public class HealthReminderCreateDTO {
    
    @NotBlank(message = "提醒标题不能为空")
    @Size(max = 100, message = "提醒标题长度不能超过100个字符")
    private String title;
    
    private String content;
    
    @NotNull(message = "提醒类型不能为空")
    private ReminderType reminderType;
    
    @NotNull(message = "频率类型不能为空")
    private FrequencyType frequencyType;
    
    @NotBlank(message = "频率值不能为空")
    @Size(max = 100, message = "频率值长度不能超过100个字符")
    private String frequencyValue; // 如 "08:00" 或 "MONDAY,08:00" 或 "15,08:00"
    
    private LocalDate startDate; // 如果为空，则使用当前日期
    
    private LocalDate endDate; // 结束日期，可为空表示永久有效
    
    private Boolean isActive = true; // 是否激活，默认true
}
