package com.ruanjianjiaGou.ruanjianjiaGou.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class HealthRecordVO {
    private Long id;
    private Long profileId;
    private String metricType;
    private BigDecimal metricValue;
    private Integer systolicPressure;
    private Integer diastolicPressure;
    private String unit;
    private String notes;
    private LocalDateTime recordedAt;
    private LocalDateTime createdAt;
}
