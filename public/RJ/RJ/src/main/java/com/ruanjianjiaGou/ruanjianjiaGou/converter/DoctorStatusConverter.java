package com.ruanjianjiaGou.ruanjianjiaGou.converter;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.Doctor;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * 医生状态枚举转换器
 * 将数据库中的小写字符串转换为Java枚举
 */
@Converter(autoApply = true)
public class DoctorStatusConverter implements AttributeConverter<Doctor.DoctorStatus, String> {

    @Override
    public String convertToDatabaseColumn(Doctor.DoctorStatus attribute) {
        if (attribute == null) {
            return null;
        }
        // 将枚举转换为小写字符串存储到数据库
        return attribute.name().toLowerCase();
    }

    @Override
    public Doctor.DoctorStatus convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.trim().isEmpty()) {
            return null;
        }
        
        // 将数据库中的字符串转换为枚举
        String normalizedData = dbData.trim().toLowerCase();
        
        switch (normalizedData) {
            case "pending":
                return Doctor.DoctorStatus.PENDING;
            case "approved":
                return Doctor.DoctorStatus.APPROVED;
            case "rejected":
                return Doctor.DoctorStatus.REJECTED;
            default:
                // 如果是大写的枚举名称，也要处理
                try {
                    return Doctor.DoctorStatus.valueOf(dbData.toUpperCase());
                } catch (IllegalArgumentException e) {
                    throw new IllegalArgumentException("Unknown doctor status: " + dbData);
                }
        }
    }
}
