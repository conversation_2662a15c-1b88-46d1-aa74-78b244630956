package com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 医生信息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DoctorDTO {
    
    private Long id;
    private Long userId;
    private String realName;
    private String title;
    private String specialty;
    private String bio;
    private String status;
    
    // 科室信息
    private Long departmentId;
    private String departmentName;
    
    // 统计信息
    private Long totalAppointments;
    private Boolean hasAvailableSchedules;
    
    public DoctorDTO(Long id, Long userId, String realName, String title, 
                    String specialty, String bio, String status,
                    Long departmentId, String departmentName) {
        this.id = id;
        this.userId = userId;
        this.realName = realName;
        this.title = title;
        this.specialty = specialty;
        this.bio = bio;
        this.status = status;
        this.departmentId = departmentId;
        this.departmentName = departmentName;
    }
}
