package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.UserLoginDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.UserRegisterDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.User;
import com.ruanjianjiaGou.ruanjianjiaGou.service.UserService;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.LoginVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 测试控制器 - 用于测试注册和登录功能
 */
@RestController
@RequestMapping("/api/test")
public class TestController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 测试用户注册
     */
    @PostMapping("/register/user")
    public Result<Void> testRegisterUser() {
        try {
            UserRegisterDTO registerDTO = new UserRegisterDTO();
            registerDTO.setPhoneNumber("13800000006");
            registerDTO.setPassword("123456");
            registerDTO.setNickname("张六");
            registerDTO.setRole(User.UserRole.RESIDENT);
            
            userService.register(registerDTO);
            return Result.success("用户注册成功");
        } catch (Exception e) {
            return Result.error("用户注册失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试医生注册
     */
    @PostMapping("/register/doctor")
    public Result<Void> testRegisterDoctor() {
        try {
            UserRegisterDTO registerDTO = new UserRegisterDTO();
            registerDTO.setPhoneNumber("18610001005");
            registerDTO.setPassword("123456");
            registerDTO.setNickname("五医生");
            registerDTO.setRole(User.UserRole.DOCTOR);
            registerDTO.setRealName("五医生");
            registerDTO.setDepartmentId(5L); // 中医科
            registerDTO.setTitle("主治医师");
            registerDTO.setSpecialty("中医内科、针灸推拿");
            registerDTO.setBio("专业的中医医师，擅长中医诊疗和针灸治疗。");
            
            userService.register(registerDTO);
            return Result.success("医生注册成功");
        } catch (Exception e) {
            return Result.error("医生注册失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试用户登录 - 使用数据库中现有的用户
     */
    @PostMapping("/login/user")
    public Result<LoginVO> testLoginUser() {
        try {
            UserLoginDTO loginDTO = new UserLoginDTO();
            loginDTO.setPhoneNumber("13800000001"); // 使用数据库中现有的用户
            loginDTO.setPassword("123456");

            LoginVO loginVO = userService.login(loginDTO);
            return Result.success("用户登录成功", loginVO);
        } catch (Exception e) {
            return Result.error("用户登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试医生登录 - 使用数据库中现有的医生
     */
    @PostMapping("/login/doctor")
    public Result<LoginVO> testLoginDoctor() {
        try {
            UserLoginDTO loginDTO = new UserLoginDTO();
            loginDTO.setPhoneNumber("18610001001"); // 使用数据库中现有的医生
            loginDTO.setPassword("doctor666");

            LoginVO loginVO = userService.login(loginDTO);
            return Result.success("医生登录成功", loginVO);
        } catch (Exception e) {
            return Result.error("医生登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试管理员登录
     */
    @PostMapping("/login/admin")
    public Result<LoginVO> testLoginAdmin() {
        try {
            UserLoginDTO loginDTO = new UserLoginDTO();
            loginDTO.setPhoneNumber("19999999999");
            loginDTO.setPassword("admin888");
            
            LoginVO loginVO = userService.login(loginDTO);
            return Result.success("管理员登录成功", loginVO);
        } catch (Exception e) {
            return Result.error("管理员登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试数据库现有用户登录
     */
    @PostMapping("/login/existing")
    public Result<LoginVO> testLoginExisting() {
        try {
            UserLoginDTO loginDTO = new UserLoginDTO();
            loginDTO.setPhoneNumber("18610001001");
            loginDTO.setPassword("doctor666");
            
            LoginVO loginVO = userService.login(loginDTO);
            return Result.success("现有用户登录成功", loginVO);
        } catch (Exception e) {
            return Result.error("现有用户登录失败: " + e.getMessage());
        }
    }
}
