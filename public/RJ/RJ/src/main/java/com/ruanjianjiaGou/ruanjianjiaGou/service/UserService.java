package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.dto.ChangePasswordDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.DoctorPasswordUpdateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.UserLoginDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.UserRegisterDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.UserUpdateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.User;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.Doctor;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.UserRepository;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.DoctorRepository;
import com.ruanjianjiaGou.ruanjianjiaGou.utils.JwtUtils;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.LoginVO;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.UserInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import java.util.Optional;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

@Service
public class UserService {
    
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private DoctorRepository doctorRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtUtils jwtUtils;
    
    /**
     * 用户注册
     */
    @Transactional
    public void register(UserRegisterDTO registerDTO) {
        // 1. 检查手机号是否已存在
        if (userRepository.existsByPhoneNumber(registerDTO.getPhoneNumber())) {
            throw new RuntimeException("手机号已被注册");
        }

        // 2. 创建新用户
        User user = new User();
        user.setPhoneNumber(registerDTO.getPhoneNumber());
        // 新数据库设计：密码明文存储
        user.setPassword(registerDTO.getPassword());

        // 设置昵称
        if (StringUtils.hasText(registerDTO.getNickname())) {
            user.setNickname(registerDTO.getNickname());
        } else {
            user.setNickname("健康用户" + registerDTO.getPhoneNumber().substring(7));
        }

        // 设置用户角色
        if (registerDTO.getRole() != null) {
            user.setRole(registerDTO.getRole());
        } else {
            user.setRole(User.UserRole.RESIDENT);
        }

        // 3. 保存用户
        User savedUser = userRepository.save(user);

        // 4. 如果是医生注册，创建医生信息
        if (User.UserRole.DOCTOR.equals(savedUser.getRole())) {
            createDoctorProfile(savedUser, registerDTO);
        }
    }

    /**
     * 创建医生档案
     */
    private void createDoctorProfile(User user, UserRegisterDTO registerDTO) {
        Doctor doctor = new Doctor();
        doctor.setUserId(user.getId());

        // 设置真实姓名
        if (StringUtils.hasText(registerDTO.getRealName())) {
            doctor.setRealName(registerDTO.getRealName());
        } else {
            doctor.setRealName(user.getNickname());
        }

        // 设置科室ID，默认为1（内科）
        if (registerDTO.getDepartmentId() != null) {
            doctor.setDepartmentId(registerDTO.getDepartmentId());
        } else {
            doctor.setDepartmentId(1L); // 默认内科
        }

        // 设置职称
        if (StringUtils.hasText(registerDTO.getTitle())) {
            doctor.setTitle(registerDTO.getTitle());
        } else {
            doctor.setTitle("医师");
        }

        // 设置专长
        if (StringUtils.hasText(registerDTO.getSpecialty())) {
            doctor.setSpecialty(registerDTO.getSpecialty());
        }

        // 设置个人简介
        if (StringUtils.hasText(registerDTO.getBio())) {
            doctor.setBio(registerDTO.getBio());
        }

        // 设置状态为待审核
        doctor.setStatus(Doctor.DoctorStatus.PENDING);

        doctorRepository.save(doctor);
    }
    
    /**
     * 用户登录
     */
    public LoginVO login(UserLoginDTO loginDTO) {
        // 1. 查找用户
        User user = userRepository.findByPhoneNumber(loginDTO.getPhoneNumber())
                .orElseThrow(() -> new RuntimeException("用户名或密码错误"));

        // 2. 验证密码（明文比较）
        if (!loginDTO.getPassword().equals(user.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 3. 生成JWT Token
        String token = jwtUtils.generateToken(user.getPhoneNumber());

        // 4. 构建用户信息VO（包含角色信息）
        UserInfoVO userInfo = getUserInfo(user.getId());

        // 5. 返回登录结果
        return new LoginVO(token, userInfo);
    }

    /**
     * 获取用户信息
     */
    public UserInfoVO getUserInfo(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        UserInfoVO userInfo = new UserInfoVO();
        userInfo.setId(user.getId());
        userInfo.setNickname(user.getNickname());
        userInfo.setPhoneNumber(user.getPhoneNumber());

        // 基于role字段设置角色信息
        userInfo.setIsDoctor(user.isDoctor());
        userInfo.setIsAdmin(user.isAdmin());

        // 如果是医生，获取医生详细信息
        if (user.isDoctor()) {
            Optional<Doctor> doctorOpt = doctorRepository.findById(userId);
            if (doctorOpt.isPresent()) {
                Doctor doctor = doctorOpt.get();
                userInfo.setRealName(doctor.getRealName());
                userInfo.setGender(doctor.getGender() != null ? doctor.getGender().name() : null);
                userInfo.setBirthDate(doctor.getBirthDate());
                userInfo.setAge(doctor.getAge());
                userInfo.setIdCardNumber(doctor.getIdCardNumber());
                userInfo.setAvatarUrl(doctor.getAvatarUrl());
                userInfo.setDoctorStatus(doctor.getStatus().name());
                userInfo.setTitle(doctor.getTitle());

                // 获取科室名称
                if (doctor.getDepartment() != null) {
                    userInfo.setDepartmentName(doctor.getDepartment().getName());
                }
            }
        }

        return userInfo;
    }

    /**
     * 更新用户信息
     */
    @Transactional
    public void updateUserInfo(Long userId, UserUpdateDTO dto) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 更新昵称
        if (dto.getNickname() != null) {
            user.setNickname(dto.getNickname());
        }

        // 更新手机号（需要检查唯一性）
        if (dto.getPhoneNumber() != null && !dto.getPhoneNumber().equals(user.getPhoneNumber())) {
            if (userRepository.existsByPhoneNumber(dto.getPhoneNumber())) {
                throw new RuntimeException("手机号已被其他用户使用");
            }
            user.setPhoneNumber(dto.getPhoneNumber());
        }

        userRepository.save(user);

        // 如果是医生，更新医生表中的信息
        Optional<Doctor> doctorOpt = doctorRepository.findById(userId);
        if (doctorOpt.isPresent()) {
            Doctor doctor = doctorOpt.get();
            boolean needUpdate = false;

            if (dto.getRealName() != null) {
                doctor.setRealName(dto.getRealName());
                needUpdate = true;
            }

            if (dto.getGender() != null) {
                doctor.setGender(dto.getGender());
                needUpdate = true;
            }

            if (dto.getBirthDate() != null) {
                doctor.setBirthDate(dto.getBirthDate());
                needUpdate = true;
            }

            if (dto.getIdCardNumber() != null) {
                // 检查身份证号唯一性
                Optional<Doctor> existingDoctor = doctorRepository.findByIdCardNumber(dto.getIdCardNumber());
                if (existingDoctor.isPresent() && !existingDoctor.get().getUserId().equals(userId)) {
                    throw new RuntimeException("身份证号已被其他医生使用");
                }
                doctor.setIdCardNumber(dto.getIdCardNumber());
                needUpdate = true;
            }

            if (dto.getAvatarUrl() != null) {
                doctor.setAvatarUrl(dto.getAvatarUrl());
                needUpdate = true;
            }

            if (needUpdate) {
                doctorRepository.save(doctor);
            }
        }
    }

    /**
     * 修改密码
     */
    @Transactional
    public void changePassword(Long userId, ChangePasswordDTO dto) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 验证当前密码
        if (!dto.getCurrentPassword().equals(user.getPassword())) {
            throw new RuntimeException("当前密码不正确");
        }

        // 验证新密码和确认密码是否一致
        if (!dto.getNewPassword().equals(dto.getConfirmPassword())) {
            throw new RuntimeException("新密码和确认密码不一致");
        }

        // 检查新密码是否与当前密码相同
        if (dto.getNewPassword().equals(user.getPassword())) {
            throw new RuntimeException("新密码不能与当前密码相同");
        }

        // 更新密码
        user.setPassword(dto.getNewPassword());
        userRepository.save(user);
    }

    /**
     * 医生修改密码
     */
    @Transactional
    public void changeDoctorPassword(Long userId, DoctorPasswordUpdateDTO dto) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 验证是否为医生用户
        if (!user.isDoctor()) {
            throw new RuntimeException("只有医生用户可以使用此接口");
        }

        // 验证当前密码
        if (!dto.getCurrentPassword().equals(user.getPassword())) {
            throw new RuntimeException("当前密码不正确");
        }

        // 验证新密码和确认密码是否一致
        if (!dto.getNewPassword().equals(dto.getConfirmPassword())) {
            throw new RuntimeException("新密码和确认密码不一致");
        }

        // 检查新密码是否与当前密码相同
        if (dto.getNewPassword().equals(user.getPassword())) {
            throw new RuntimeException("新密码不能与当前密码相同");
        }

        // 更新密码
        user.setPassword(dto.getNewPassword());
        userRepository.save(user);
    }
}
