package com.ruanjianjiaGou.ruanjianjiaGou.repository;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.Message;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface MessageRepository extends JpaRepository<Message, Long> {
    
    /**
     * 根据问诊ID查找消息列表（分页）
     */
    Page<Message> findByConsultationIdOrderBySentAt(Long consultationId, Pageable pageable);
    
    /**
     * 根据问诊ID查找消息列表（不分页）
     */
    List<Message> findByConsultationIdOrderBySentAt(Long consultationId);
    
    /**
     * 根据问诊ID查找最新的消息
     */
    @Query("SELECT m FROM Message m WHERE m.consultationId = :consultationId ORDER BY m.sentAt DESC")
    List<Message> findLatestMessagesByConsultationId(@Param("consultationId") Long consultationId, Pageable pageable);
    
    /**
     * 统计问诊的消息数量
     */
    long countByConsultationId(Long consultationId);
    
    /**
     * 根据发送者ID查找消息
     */
    Page<Message> findBySenderIdOrderBySentAtDesc(Long senderId, Pageable pageable);
    
    /**
     * 查找指定时间范围内的消息
     */
    List<Message> findByConsultationIdAndSentAtBetweenOrderBySentAt(
        Long consultationId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 删除指定问诊的所有消息
     */
    void deleteByConsultationId(Long consultationId);
}
