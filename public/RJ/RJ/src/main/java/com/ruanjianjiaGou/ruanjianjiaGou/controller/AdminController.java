package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.User;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.UserRepository;
import com.ruanjianjiaGou.ruanjianjiaGou.service.AdminService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/api/admin")
@RequiredArgsConstructor
@Slf4j
public class AdminController {

    private final AdminService adminService;
    private final UserRepository userRepository;

    /**
     * 创建医生记录
     */
    @PostMapping("/doctors")
    public Result<Map<String, Object>> createDoctor(
            Authentication authentication,
            @RequestBody Map<String, Object> doctorData) {
        try {
            // 从JWT Token中获取手机号，然后查找用户ID
            String phoneNumber = authentication.getName();
            User user = userRepository.findByPhoneNumber(phoneNumber)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            Map<String, Object> doctor = adminService.createDoctor(user.getId(), doctorData);
            return Result.success(doctor);
        } catch (Exception e) {
            log.error("创建医生记录失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }
}

