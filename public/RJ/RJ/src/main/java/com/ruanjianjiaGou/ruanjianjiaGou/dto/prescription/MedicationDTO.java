package com.ruanjianjiaGou.ruanjianjiaGou.dto.prescription;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 药品信息DTO
 */
@Data
public class MedicationDTO {
    
    @NotBlank(message = "药品名称不能为空")
    private String name;
    
    @NotBlank(message = "药品规格不能为空")
    private String specification;
    
    @NotNull(message = "药品数量不能为空")
    @Positive(message = "药品数量必须大于0")
    private Integer quantity;
    
    @NotBlank(message = "用药频次不能为空")
    private String frequency;
    
    @NotBlank(message = "用法用量不能为空")
    private String dosage;
    
    private String notes; // 备注信息（可选）
}
