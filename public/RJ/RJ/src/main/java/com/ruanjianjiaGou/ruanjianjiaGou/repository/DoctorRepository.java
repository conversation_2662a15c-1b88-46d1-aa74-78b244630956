package com.ruanjianjiaGou.ruanjianjiaGou.repository;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.Doctor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DoctorRepository extends JpaRepository<Doctor, Long> {
    
    /**
     * 根据科室ID查找已审核通过的医生
     */
    List<Doctor> findByDepartmentIdAndStatus(Long departmentId, Doctor.DoctorStatus status);
    
    /**
     * 根据状态查找医生
     */
    List<Doctor> findByStatus(Doctor.DoctorStatus status);
    
    /**
     * 分页查找已审核通过的医生
     */
    Page<Doctor> findByStatus(Doctor.DoctorStatus status, Pageable pageable);
    
    /**
     * 根据科室ID分页查找已审核通过的医生
     */
    Page<Doctor> findByDepartmentIdAndStatus(Long departmentId, Doctor.DoctorStatus status, Pageable pageable);
    
    /**
     * 根据医生姓名模糊查询已审核通过的医生
     */
    @Query("SELECT d FROM Doctor d WHERE d.status = :status AND d.realName LIKE %:name%")
    Page<Doctor> findByStatusAndRealNameContaining(@Param("status") Doctor.DoctorStatus status, 
                                                  @Param("name") String name, 
                                                  Pageable pageable);
    
    /**
     * 根据科室ID和医生姓名模糊查询已审核通过的医生
     */
    @Query("SELECT d FROM Doctor d WHERE d.departmentId = :departmentId AND d.status = :status AND d.realName LIKE %:name%")
    Page<Doctor> findByDepartmentIdAndStatusAndRealNameContaining(@Param("departmentId") Long departmentId,
                                                                 @Param("status") Doctor.DoctorStatus status,
                                                                 @Param("name") String name,
                                                                 Pageable pageable);
    
    /**
     * 根据用户ID查找医生信息
     */
    Optional<Doctor> findByUserId(Long userId);

    /**
     * 根据身份证号查找医生
     */
    Optional<Doctor> findByIdCardNumber(String idCardNumber);
    
    /**
     * 统计各状态的医生数量
     */
    long countByStatus(Doctor.DoctorStatus status);
    
    /**
     * 统计科室下已审核通过的医生数量
     */
    long countByDepartmentIdAndStatus(Long departmentId, Doctor.DoctorStatus status);

    /**
     * 统计科室下的医生总数
     */
    long countByDepartmentId(Long departmentId);
}
