package com.ruanjianjiaGou.ruanjianjiaGou.dto.content;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.Content;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 内容详情DTO
 */
@Data
public class ContentDetailDTO {
    
    private Long id;
    private Long authorId;
    private String authorName;
    private String authorTitle; // 作者职称（如果是医生）
    private String departmentName; // 科室名称（如果是医生）
    private Content.ContentType contentType;
    private String contentTypeDescription;
    private String title;
    private String body;
    private LocalDateTime publishedAt;
    private LocalDateTime activityTime;
    private String activityLocation;
    private Long registrationCount; // 报名人数（仅对活动类型有效）
    private Boolean isRegistered; // 当前用户是否已报名（仅对活动类型有效）
    private Boolean canEdit; // 当前用户是否可以编辑
    private Boolean canDelete; // 当前用户是否可以删除
}
