package com.ruanjianjiaGou.ruanjianjiaGou.repository;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.Appointment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface AppointmentRepository extends JpaRepository<Appointment, Long> {
    
    /**
     * 根据用户ID查找预约记录
     */
    Page<Appointment> findByUserId(Long userId, Pageable pageable);
    
    /**
     * 根据用户ID和状态查找预约记录
     */
    Page<Appointment> findByUserIdAndStatus(Long userId, Appointment.AppointmentStatus status, Pageable pageable);
    
    /**
     * 根据排班ID查找预约记录
     */
    List<Appointment> findByScheduleId(Long scheduleId);
    
    /**
     * 根据排班ID和状态查找预约记录
     */
    List<Appointment> findByScheduleIdAndStatus(Long scheduleId, Appointment.AppointmentStatus status);
    
    /**
     * 根据ID和用户ID查找预约记录（用于权限验证）
     */
    Optional<Appointment> findByIdAndUserId(Long id, Long userId);
    
    /**
     * 查找用户的最近预约
     */
    @Query("SELECT a FROM Appointment a JOIN a.schedule s " +
           "WHERE a.userId = :userId AND a.status = 'BOOKED' " +
           "AND s.scheduleDate >= CURRENT_DATE " +
           "ORDER BY s.scheduleDate ASC, s.startTime ASC")
    List<Appointment> findUpcomingAppointments(@Param("userId") Long userId);
    
    /**
     * 查找医生的预约记录
     */
    @Query("SELECT a FROM Appointment a JOIN a.schedule s " +
           "WHERE s.doctorId = :doctorId " +
           "ORDER BY s.scheduleDate DESC, s.startTime DESC")
    Page<Appointment> findByDoctorId(@Param("doctorId") Long doctorId, Pageable pageable);
    
    /**
     * 查找医生指定状态的预约记录
     */
    @Query("SELECT a FROM Appointment a JOIN a.schedule s " +
           "WHERE s.doctorId = :doctorId AND a.status = :status " +
           "ORDER BY s.scheduleDate DESC, s.startTime DESC")
    Page<Appointment> findByDoctorIdAndStatus(@Param("doctorId") Long doctorId, 
                                             @Param("status") Appointment.AppointmentStatus status,
                                             Pageable pageable);
    
    /**
     * 统计用户的预约数量
     */
    long countByUserId(Long userId);
    
    /**
     * 统计用户指定状态的预约数量
     */
    long countByUserIdAndStatus(Long userId, Appointment.AppointmentStatus status);
    
    /**
     * 统计医生的预约数量
     */
    @Query("SELECT COUNT(a) FROM Appointment a JOIN a.schedule s WHERE s.doctorId = :doctorId")
    long countByDoctorId(@Param("doctorId") Long doctorId);
    
    /**
     * 统计医生指定状态的预约数量
     */
    @Query("SELECT COUNT(a) FROM Appointment a JOIN a.schedule s " +
           "WHERE s.doctorId = :doctorId AND a.status = :status")
    long countByDoctorIdAndStatus(@Param("doctorId") Long doctorId, 
                                 @Param("status") Appointment.AppointmentStatus status);
    
    /**
     * 检查用户是否已在指定排班预约
     */
    boolean existsByUserIdAndScheduleIdAndStatus(Long userId, Long scheduleId, Appointment.AppointmentStatus status);
    
    /**
     * 查找指定时间范围内的预约
     */
    @Query("SELECT a FROM Appointment a JOIN a.schedule s " +
           "WHERE s.scheduleDate BETWEEN :startDate AND :endDate " +
           "ORDER BY s.scheduleDate ASC, s.startTime ASC")
    List<Appointment> findAppointmentsBetweenDates(@Param("startDate") LocalDateTime startDate,
                                                  @Param("endDate") LocalDateTime endDate);

    /**
     * 根据患者ID查找预约记录
     */
    @Query("SELECT a FROM Appointment a JOIN a.schedule s " +
           "WHERE a.userId = :patientId " +
           "ORDER BY s.scheduleDate DESC, s.startTime DESC")
    List<Appointment> findByPatientIdOrderByAppointmentDateDesc(@Param("patientId") Long patientId);

    /**
     * 统计医生今日预约数
     */
    @Query("SELECT COUNT(a) FROM Appointment a JOIN a.schedule s " +
           "WHERE s.doctorId = :doctorId AND s.scheduleDate = :appointmentDate")
    long countByDoctorIdAndAppointmentDate(@Param("doctorId") Long doctorId,
                                          @Param("appointmentDate") LocalDate appointmentDate);

    /**
     * 统计医生指定日期范围内的预约数
     */
    @Query("SELECT COUNT(a) FROM Appointment a JOIN a.schedule s " +
           "WHERE s.doctorId = :doctorId AND s.scheduleDate BETWEEN :startDate AND :endDate")
    long countByDoctorIdAndAppointmentDateBetween(@Param("doctorId") Long doctorId,
                                                 @Param("startDate") LocalDate startDate,
                                                 @Param("endDate") LocalDate endDate);

    /**
     * 根据医生ID和日期范围查找预约
     */
    @Query("SELECT a FROM Appointment a JOIN a.schedule s " +
           "WHERE s.doctorId = :doctorId AND s.scheduleDate BETWEEN :startDate AND :endDate " +
           "ORDER BY s.scheduleDate DESC, s.startTime DESC")
    List<Appointment> findByDoctorIdAndAppointmentDateBetween(@Param("doctorId") Long doctorId,
                                                             @Param("startDate") LocalDate startDate,
                                                             @Param("endDate") LocalDate endDate);

    /**
     * 按状态统计预约数量
     */
    long countByStatus(Appointment.AppointmentStatus status);

    /**
     * 统计指定时间范围内创建的预约数
     */
    long countByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计医生在指定时间范围内的预约数量（排除已取消的）
     */
    @Query("SELECT COUNT(a) FROM Appointment a JOIN a.schedule s " +
           "WHERE s.doctorId = :doctorId AND a.status != 'CANCELLED' " +
           "AND a.createdAt BETWEEN :startTime AND :endTime")
    long countByDoctorIdAndCreatedAtBetweenExcludingCancelled(@Param("doctorId") Long doctorId,
                                                             @Param("startTime") LocalDateTime startTime,
                                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 统计医生在指定时间范围内指定状态的预约数量
     */
    @Query("SELECT COUNT(a) FROM Appointment a JOIN a.schedule s " +
           "WHERE s.doctorId = :doctorId AND a.status = :status " +
           "AND a.createdAt BETWEEN :startTime AND :endTime")
    long countByDoctorIdAndStatusAndCreatedAtBetween(@Param("doctorId") Long doctorId,
                                                    @Param("status") Appointment.AppointmentStatus status,
                                                    @Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 按日期统计医生的预约数量
     */
    @Query("SELECT DATE(a.createdAt), COUNT(a) FROM Appointment a JOIN a.schedule s " +
           "WHERE s.doctorId = :doctorId AND a.status = :status " +
           "AND a.createdAt BETWEEN :startTime AND :endTime " +
           "GROUP BY DATE(a.createdAt) ORDER BY DATE(a.createdAt)")
    List<Object[]> countByDoctorIdAndStatusGroupByDate(@Param("doctorId") Long doctorId,
                                                      @Param("status") Appointment.AppointmentStatus status,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 按时间段统计医生的预约数量
     */
    @Query("SELECT HOUR(s.startTime), COUNT(a) FROM Appointment a JOIN a.schedule s " +
           "WHERE s.doctorId = :doctorId " +
           "AND a.createdAt BETWEEN :startTime AND :endTime " +
           "GROUP BY HOUR(s.startTime) ORDER BY HOUR(s.startTime)")
    List<Object[]> countByDoctorIdAndTimeSlotGroupByHour(@Param("doctorId") Long doctorId,
                                                        @Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);
}
