package com.ruanjianjiaGou.ruanjianjiaGou.repository;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.ActivityRegistration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ActivityRegistrationRepository extends JpaRepository<ActivityRegistration, ActivityRegistration.ActivityRegistrationId> {
    
    /**
     * 查找用户的所有活动报名记录
     */
    Page<ActivityRegistration> findByIdUserId(Long userId, Pageable pageable);
    
    /**
     * 查找活动的所有报名记录
     */
    Page<ActivityRegistration> findByIdActivityId(Long activityId, Pageable pageable);
    
    /**
     * 检查用户是否已报名某个活动
     */
    boolean existsByIdActivityIdAndIdUserId(Long activityId, Long userId);
    
    /**
     * 查找用户对特定活动的报名记录
     */
    Optional<ActivityRegistration> findByIdActivityIdAndIdUserId(Long activityId, Long userId);
    
    /**
     * 统计活动的报名人数
     */
    long countByIdActivityId(Long activityId);
    
    /**
     * 统计用户的报名活动数量
     */
    long countByIdUserId(Long userId);
    
    /**
     * 查找用户在指定时间范围内的报名记录
     */
    List<ActivityRegistration> findByIdUserIdAndRegisteredAtBetween(Long userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找活动在指定时间范围内的报名记录
     */
    List<ActivityRegistration> findByIdActivityIdAndRegisteredAtBetween(Long activityId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取用户报名的活动列表（包含活动详情）
     */
    @Query(value = "SELECT ar FROM ActivityRegistration ar JOIN FETCH ar.activity a WHERE ar.id.userId = :userId ORDER BY ar.registeredAt DESC",
           countQuery = "SELECT COUNT(ar) FROM ActivityRegistration ar WHERE ar.id.userId = :userId")
    Page<ActivityRegistration> findUserRegistrationsWithActivity(@Param("userId") Long userId, Pageable pageable);

    /**
     * 获取活动的报名用户列表（包含用户详情）
     */
    @Query(value = "SELECT ar FROM ActivityRegistration ar JOIN FETCH ar.user u WHERE ar.id.activityId = :activityId ORDER BY ar.registeredAt ASC",
           countQuery = "SELECT COUNT(ar) FROM ActivityRegistration ar WHERE ar.id.activityId = :activityId")
    Page<ActivityRegistration> findActivityRegistrationsWithUser(@Param("activityId") Long activityId, Pageable pageable);
    
    /**
     * 删除用户对特定活动的报名
     */
    void deleteByIdActivityIdAndIdUserId(Long activityId, Long userId);
}
