package com.ruanjianjiaGou.ruanjianjiaGou.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 活动报名实体类
 */
@Entity
@Table(name = "activity_registrations", indexes = {
    @Index(name = "fk_registrations_users_idx", columnList = "user_id")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityRegistration {
    
    @EmbeddedId
    private ActivityRegistrationId id;
    
    @Column(name = "registered_at", nullable = false)
    private LocalDateTime registeredAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "activity_id", insertable = false, updatable = false)
    private Content activity;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
    
    @PrePersist
    protected void onCreate() {
        if (registeredAt == null) {
            registeredAt = LocalDateTime.now();
        }
    }
    
    /**
     * 复合主键类
     */
    @Embeddable
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActivityRegistrationId implements Serializable {
        
        @Column(name = "activity_id")
        private Long activityId;
        
        @Column(name = "user_id")
        private Long userId;
    }
}
