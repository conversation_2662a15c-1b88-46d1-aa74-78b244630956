package com.ruanjianjiaGou.ruanjianjiaGou.dto;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.User;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 用户注册DTO
 */
@Data
public class UserRegisterDTO {

    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phoneNumber;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20位之间")
    private String password;

    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;

    // 用户角色，默认为居民
    private User.UserRole role = User.UserRole.RESIDENT;

    // 医生注册时的额外信息
    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;

    private Long departmentId; // 科室ID

    @Size(max = 50, message = "职称长度不能超过50个字符")
    private String title;

    @Size(max = 500, message = "专长描述长度不能超过500个字符")
    private String specialty;

    @Size(max = 1000, message = "个人简介长度不能超过1000个字符")
    private String bio;
}
