package com.ruanjianjiaGou.ruanjianjiaGou.dto;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class HealthRecordCreateDTO {
    
    @NotNull(message = "档案ID不能为空")
    private Long profileId;
    
    @NotBlank(message = "指标类型不能为空")
    @Size(max = 50, message = "指标类型长度不能超过50个字符")
    private String metricType; // blood_pressure, blood_sugar, weight, heart_rate, temperature, etc.
    
    @NotNull(message = "指标值不能为空")
    @DecimalMin(value = "0.0", message = "指标值必须大于0")
    private BigDecimal metricValue;
    
    // 血压专用字段
    private Integer systolicPressure; // 收缩压
    private Integer diastolicPressure; // 舒张压
    
    @Size(max = 20, message = "单位长度不能超过20个字符")
    private String unit;
    
    private String notes;
    
    private LocalDateTime recordedAt; // 如果为空，则使用当前时间
}
