package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.statistics.*;
import com.ruanjianjiaGou.ruanjianjiaGou.service.DoctorStatisticsService;
import com.ruanjianjiaGou.ruanjianjiaGou.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 医生统计分析控制器
 */
@RestController
@RequestMapping("/api/doctor/statistics")
@RequiredArgsConstructor
@Slf4j
public class DoctorStatisticsController {
    
    private final DoctorStatisticsService statisticsService;
    private final SecurityUtils securityUtils;
    
    /**
     * 获取医生核心指标KPI
     * 
     * @param range 时间范围：week(本周)、month(本月)、quarter(近三个月)
     */
    @GetMapping("/kpi")
    public Result<DoctorKpiDTO> getDoctorKpi(
            @RequestParam(defaultValue = "month") String range,
            HttpServletRequest request) {
        
        try {
            Long doctorId = securityUtils.getCurrentUserId(request);
            log.info("获取医生KPI统计，医生ID: {}, 时间范围: {}", doctorId, range);
            
            DoctorKpiDTO kpi = statisticsService.getDoctorKpi(doctorId, range);
            return Result.success("获取成功", kpi);
            
        } catch (Exception e) {
            log.error("获取医生KPI统计失败", e);
            return Result.error("获取统计数据失败");
        }
    }
    
    /**
     * 获取服务量趋势
     * 
     * @param range 时间范围：week(本周)、month(本月)、quarter(近三个月)
     */
    @GetMapping("/service-trend")
    public Result<ServiceTrendDTO> getServiceTrend(
            @RequestParam(defaultValue = "month") String range,
            HttpServletRequest request) {
        
        try {
            Long doctorId = securityUtils.getCurrentUserId(request);
            log.info("获取服务量趋势，医生ID: {}, 时间范围: {}", doctorId, range);
            
            ServiceTrendDTO trend = statisticsService.getServiceTrend(doctorId, range);
            return Result.success("获取成功", trend);
            
        } catch (Exception e) {
            log.error("获取服务量趋势失败", e);
            return Result.error("获取趋势数据失败");
        }
    }
    
    /**
     * 获取预约状态分配
     * 
     * @param range 时间范围：week(本周)、month(本月)、quarter(近三个月)
     */
    @GetMapping("/appointment-status")
    public Result<AppointmentStatusDTO> getAppointmentStatus(
            @RequestParam(defaultValue = "month") String range,
            HttpServletRequest request) {
        
        try {
            Long doctorId = securityUtils.getCurrentUserId(request);
            log.info("获取预约状态分配，医生ID: {}, 时间范围: {}", doctorId, range);
            
            AppointmentStatusDTO status = statisticsService.getAppointmentStatus(doctorId, range);
            return Result.success("获取成功", status);
            
        } catch (Exception e) {
            log.error("获取预约状态分配失败", e);
            return Result.error("获取状态数据失败");
        }
    }
    
    /**
     * 获取高频服务患者排行
     * 
     * @param range 时间范围：week(本周)、month(本月)、quarter(近三个月)
     */
    @GetMapping("/top-patients")
    public Result<TopPatientsDTO> getTopPatients(
            @RequestParam(defaultValue = "month") String range,
            HttpServletRequest request) {
        
        try {
            Long doctorId = securityUtils.getCurrentUserId(request);
            log.info("获取高频服务患者排行，医生ID: {}, 时间范围: {}", doctorId, range);
            
            TopPatientsDTO topPatients = statisticsService.getTopPatients(doctorId, range);
            return Result.success("获取成功", topPatients);
            
        } catch (Exception e) {
            log.error("获取高频服务患者排行失败", e);
            return Result.error("获取患者排行数据失败");
        }
    }
    
    /**
     * 获取预约时间段分析
     * 
     * @param range 时间范围：week(本周)、month(本月)、quarter(近三个月)
     */
    @GetMapping("/schedule-hotness")
    public Result<ScheduleHotnessDTO> getScheduleHotness(
            @RequestParam(defaultValue = "month") String range,
            HttpServletRequest request) {
        
        try {
            Long doctorId = securityUtils.getCurrentUserId(request);
            log.info("获取预约时间段分析，医生ID: {}, 时间范围: {}", doctorId, range);
            
            ScheduleHotnessDTO hotness = statisticsService.getScheduleHotness(doctorId, range);
            return Result.success("获取成功", hotness);
            
        } catch (Exception e) {
            log.error("获取预约时间段分析失败", e);
            return Result.error("获取时间段分析数据失败");
        }
    }
}
