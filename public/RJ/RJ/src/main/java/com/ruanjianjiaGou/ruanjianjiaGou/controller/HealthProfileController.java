package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.HealthProfileCreateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.HealthProfileUpdateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.HealthProfile;
import com.ruanjianjiaGou.ruanjianjiaGou.service.HealthProfileService;
import com.ruanjianjiaGou.ruanjianjiaGou.utils.SecurityUtils;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.HealthProfileDetailVO;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.HealthProfileVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/health/profiles")
@Slf4j
public class HealthProfileController {
    
    @Autowired
    private HealthProfileService profileService;
    
    @Autowired
    private SecurityUtils securityUtils;
    
    /**
     * 创建健康档案
     */
    @PostMapping
    public Result<HealthProfileVO> createProfile(@Valid @RequestBody HealthProfileCreateDTO dto,
                                                HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            HealthProfile profile = profileService.createProfile(userId, dto);
            HealthProfileVO vo = convertToVO(profile);
            return Result.success("健康档案创建成功", vo);
        } catch (RuntimeException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("创建失败，请稍后重试");
        }
    }
    
    /**
     * 获取健康档案列表
     */
    @GetMapping
    public Result<Map<String, Object>> getProfiles(@RequestParam(defaultValue = "1") int page,
                                                  @RequestParam(defaultValue = "10") int size,
                                                  HttpServletRequest request) {
        Long userId = null;
        try {
            userId = securityUtils.getCurrentUserId(request);
            Page<HealthProfile> profilePage = profileService.getUserProfiles(userId, page, size);

            List<HealthProfileVO> profiles = profilePage.getContent().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

            Map<String, Object> result = new HashMap<>();
            result.put("profiles", profiles);

            Map<String, Object> pagination = new HashMap<>();
            pagination.put("currentPage", profilePage.getNumber() + 1);
            pagination.put("totalPages", profilePage.getTotalPages());
            pagination.put("totalRecords", profilePage.getTotalElements());
            pagination.put("pageSize", profilePage.getSize());
            result.put("pagination", pagination);

            return Result.success("获取成功", result);
        } catch (Exception e) {
            log.error("获取健康档案列表失败，用户ID: {}, 错误信息: {}", userId, e.getMessage(), e);
            return Result.error("获取失败，请稍后重试");
        }
    }
    
    /**
     * 获取健康档案详情
     */
    @GetMapping("/{id}")
    public Result<HealthProfileDetailVO> getProfile(@PathVariable Long id,
                                                   HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            HealthProfile profile = profileService.getProfileById(userId, id);
            HealthProfileDetailVO vo = convertToDetailVO(profile);
            return Result.success("获取成功", vo);
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            return Result.error("获取失败，请稍后重试");
        }
    }
    
    /**
     * 更新健康档案
     */
    @PutMapping("/{id}")
    public Result<Void> updateProfile(@PathVariable Long id,
                                     @Valid @RequestBody HealthProfileUpdateDTO dto,
                                     HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            profileService.updateProfile(userId, id, dto);
            return Result.success("健康档案更新成功");
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            return Result.error("更新失败，请稍后重试");
        }
    }
    
    /**
     * 删除健康档案
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteProfile(@PathVariable Long id,
                                     HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            profileService.deleteProfile(userId, id);
            return Result.success("健康档案删除成功");
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            return Result.error("删除失败，请稍后重试");
        }
    }
    
    /**
     * 转换为VO对象
     */
    private HealthProfileVO convertToVO(HealthProfile profile) {
        HealthProfileVO vo = new HealthProfileVO();
        vo.setId(profile.getId());
        vo.setProfileOwnerName(profile.getProfileOwnerName());
        vo.setGender(profile.getGender() != null ? profile.getGender().name() : null);
        vo.setAge(profile.getAge());
        vo.setBloodType(null); // 这个字段数据库中没有
        vo.setHeight(null); // 这个字段数据库中没有
        vo.setWeight(null); // 这个字段数据库中没有
        vo.setBmi(null); // 这个字段数据库中没有
        vo.setCreatedAt(null); // 这个字段数据库中没有
        return vo;
    }

    /**
     * 转换为详情VO对象
     */
    private HealthProfileDetailVO convertToDetailVO(HealthProfile profile) {
        HealthProfileDetailVO vo = new HealthProfileDetailVO();
        vo.setId(profile.getId());
        vo.setProfileOwnerName(profile.getProfileOwnerName());
        vo.setMedicalHistory(profile.getMedicalHistory());
        vo.setGender(profile.getGender() != null ? profile.getGender().name() : null);
        vo.setBirthDate(profile.getBirthDate());
        vo.setAge(profile.getAge());
        vo.setIdCard(profile.getIdCardNumber());
        vo.setPhone(null); // 这个字段数据库中没有
        vo.setEmergencyContact(null); // 这个字段数据库中没有
        vo.setEmergencyPhone(null); // 这个字段数据库中没有
        vo.setAllergies(null); // 这个字段数据库中没有
        vo.setBloodType(null); // 这个字段数据库中没有
        vo.setHeight(null); // 这个字段数据库中没有
        vo.setWeight(null); // 这个字段数据库中没有
        vo.setBmi(null); // 这个字段数据库中没有
        vo.setCreatedAt(null); // 这个字段数据库中没有
        vo.setUpdatedAt(null); // 这个字段数据库中没有
        return vo;
    }
}
