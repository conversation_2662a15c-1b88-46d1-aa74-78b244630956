package com.ruanjianjiaGou.ruanjianjiaGou.repository;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.HealthReminder;
import com.ruanjianjiaGou.ruanjianjiaGou.enums.ReminderType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface HealthReminderRepository extends JpaRepository<HealthReminder, Long>, JpaSpecificationExecutor<HealthReminder> {
    
    /**
     * 根据用户ID查找提醒列表
     */
    List<HealthReminder> findByUserIdOrderByCreatedAtDesc(Long userId);
    
    /**
     * 根据用户ID和状态查找提醒
     */
    List<HealthReminder> findByUserIdAndIsActiveOrderByCreatedAtDesc(Long userId, Boolean isActive);
    
    /**
     * 根据用户ID和类型查找提醒
     */
    List<HealthReminder> findByUserIdAndReminderTypeOrderByCreatedAtDesc(Long userId, ReminderType reminderType);
    
    /**
     * 根据用户ID和ID查找提醒（用于权限验证）
     */
    Optional<HealthReminder> findByIdAndUserId(Long id, Long userId);
    
    /**
     * 查找今日需要提醒的记录
     */
    List<HealthReminder> findByUserIdAndIsActiveTrueAndNextReminderTimeBetween(
        Long userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找所有需要执行的提醒（用于定时任务）
     */
    @Query("SELECT r FROM HealthReminder r WHERE r.isActive = true " +
           "AND r.nextReminderTime <= :currentTime " +
           "AND (r.endDate IS NULL OR r.endDate >= CURRENT_DATE)")
    List<HealthReminder> findActiveRemindersToExecute(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 统计用户的提醒数量
     */
    long countByUserIdAndIsActive(Long userId, Boolean isActive);
}
