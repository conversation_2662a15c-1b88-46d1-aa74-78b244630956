package com.ruanjianjiaGou.ruanjianjiaGou.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * 枚举值修复服务
 */
@Service
@Slf4j
public class EnumFixService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @PostConstruct
    @Transactional
    public void fixEnumValues() {
        try {
            log.info("开始修复数据库枚举值...");
            
            // 修复用户角色枚举值
            fixUserRoles();

            // 修复医生状态枚举值
            fixDoctorStatus();

            // 修复预约状态枚举值
            fixAppointmentStatus();

            // 显示修复结果
            showResults();
            
            log.info("数据库枚举值修复完成");
        } catch (Exception e) {
            log.error("修复数据库枚举值时出错: {}", e.getMessage(), e);
        }
    }

    private void fixUserRoles() {
        try {
            // 先查看当前数据
            List<Map<String, Object>> currentRoles = jdbcTemplate.queryForList(
                "SELECT role, COUNT(*) as count FROM users GROUP BY role"
            );
            log.info("修复前用户角色分布:");
            for (Map<String, Object> row : currentRoles) {
                log.info("  {} = {}", row.get("role"), row.get("count"));
            }

            // 修复用户角色 - 强制更新所有记录
            int residentCount = jdbcTemplate.update("UPDATE users SET role = 'RESIDENT' WHERE role IN ('resident', 'RESIDENT')");
            int doctorCount = jdbcTemplate.update("UPDATE users SET role = 'DOCTOR' WHERE role IN ('doctor', 'DOCTOR')");
            int adminCount = jdbcTemplate.update("UPDATE users SET role = 'ADMIN' WHERE role IN ('admin', 'ADMIN')");

            log.info("用户角色修复完成: RESIDENT={}, DOCTOR={}, ADMIN={}", residentCount, doctorCount, adminCount);
        } catch (Exception e) {
            log.warn("修复用户角色时出错: {}", e.getMessage());
        }
    }

    private void fixDoctorStatus() {
        try {
            // 先查看当前数据
            List<Map<String, Object>> currentStatus = jdbcTemplate.queryForList(
                "SELECT status, COUNT(*) as count FROM doctors GROUP BY status"
            );
            log.info("修复前医生状态分布:");
            for (Map<String, Object> row : currentStatus) {
                log.info("  {} = {}", row.get("status"), row.get("count"));
            }

            // 修复医生状态 - 强制更新所有记录
            int approvedCount = jdbcTemplate.update("UPDATE doctors SET status = 'APPROVED' WHERE status IN ('approved', 'APPROVED')");
            int pendingCount = jdbcTemplate.update("UPDATE doctors SET status = 'PENDING' WHERE status IN ('pending', 'PENDING')");
            int rejectedCount = jdbcTemplate.update("UPDATE doctors SET status = 'REJECTED' WHERE status IN ('rejected', 'REJECTED')");

            log.info("医生状态修复完成: APPROVED={}, PENDING={}, REJECTED={}", approvedCount, pendingCount, rejectedCount);
        } catch (Exception e) {
            log.warn("修复医生状态时出错: {}", e.getMessage());
        }
    }

    private void fixAppointmentStatus() {
        try {
            // 先查看当前数据
            List<Map<String, Object>> currentStatus = jdbcTemplate.queryForList(
                "SELECT status, COUNT(*) as count FROM appointments GROUP BY status"
            );
            log.info("修复前预约状态分布:");
            for (Map<String, Object> row : currentStatus) {
                log.info("  {} = {}", row.get("status"), row.get("count"));
            }

            // 修复预约状态 - 强制更新所有记录
            int bookedCount = jdbcTemplate.update("UPDATE appointments SET status = 'BOOKED' WHERE status IN ('booked', 'BOOKED')");
            int completedCount = jdbcTemplate.update("UPDATE appointments SET status = 'COMPLETED' WHERE status IN ('completed', 'COMPLETED')");
            int cancelledCount = jdbcTemplate.update("UPDATE appointments SET status = 'CANCELLED' WHERE status IN ('cancelled', 'CANCELLED')");

            log.info("预约状态修复完成: BOOKED={}, COMPLETED={}, CANCELLED={}", bookedCount, completedCount, cancelledCount);
        } catch (Exception e) {
            log.warn("修复预约状态时出错: {}", e.getMessage());
        }
    }

    private void showResults() {
        try {
            // 显示用户角色分布
            List<Map<String, Object>> userRoles = jdbcTemplate.queryForList(
                "SELECT role, COUNT(*) as count FROM users GROUP BY role"
            );
            log.info("用户角色分布:");
            for (Map<String, Object> row : userRoles) {
                log.info("  {} = {}", row.get("role"), row.get("count"));
            }

            // 显示医生状态分布
            List<Map<String, Object>> doctorStatus = jdbcTemplate.queryForList(
                "SELECT status, COUNT(*) as count FROM doctors GROUP BY status"
            );
            log.info("医生状态分布:");
            for (Map<String, Object> row : doctorStatus) {
                log.info("  {} = {}", row.get("status"), row.get("count"));
            }

            // 显示预约状态分布
            List<Map<String, Object>> appointmentStatus = jdbcTemplate.queryForList(
                "SELECT status, COUNT(*) as count FROM appointments GROUP BY status"
            );
            log.info("预约状态分布:");
            for (Map<String, Object> row : appointmentStatus) {
                log.info("  {} = {}", row.get("status"), row.get("count"));
            }
        } catch (Exception e) {
            log.warn("显示结果时出错: {}", e.getMessage());
        }
    }
}
