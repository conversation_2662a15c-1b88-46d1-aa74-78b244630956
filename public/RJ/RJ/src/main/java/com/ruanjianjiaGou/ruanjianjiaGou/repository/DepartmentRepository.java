package com.ruanjianjiaGou.ruanjianjiaGou.repository;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.Department;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DepartmentRepository extends JpaRepository<Department, Long> {
    
    /**
     * 根据科室名称查找科室
     */
    Optional<Department> findByName(String name);
    
    /**
     * 检查科室名称是否存在
     */
    boolean existsByName(String name);
    
    /**
     * 查找所有科室，按名称排序
     */
    List<Department> findAllByOrderByName();
    
    /**
     * 查找有医生的科室
     */
    @Query("SELECT DISTINCT d FROM Department d JOIN d.doctors doc WHERE doc.status = 'APPROVED'")
    List<Department> findDepartmentsWithApprovedDoctors();
}
