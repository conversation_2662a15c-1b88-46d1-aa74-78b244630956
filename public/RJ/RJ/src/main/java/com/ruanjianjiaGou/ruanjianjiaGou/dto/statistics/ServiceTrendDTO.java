package com.ruanjianjiaGou.ruanjianjiaGou.dto.statistics;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 服务量趋势DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceTrendDTO {
    
    /**
     * 趋势数据列表
     */
    private List<DailyServiceData> dailyData;
    
    /**
     * 每日服务数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DailyServiceData {
        
        /**
         * 日期
         */
        private LocalDate date;
        
        /**
         * 预约服务数量
         */
        private Long appointmentCount;
        
        /**
         * 在线问诊数量
         */
        private Long consultationCount;
        
        /**
         * 总服务数量
         */
        private Long totalCount;
    }
}
