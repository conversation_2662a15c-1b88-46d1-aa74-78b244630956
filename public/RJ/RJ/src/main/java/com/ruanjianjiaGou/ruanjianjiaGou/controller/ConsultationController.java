package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.consultation.*;
import com.ruanjianjiaGou.ruanjianjiaGou.service.ConsultationService;
import com.ruanjianjiaGou.ruanjianjiaGou.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Min;

/**
 * 在线问诊控制器
 */
@RestController
@RequestMapping("/api/consultations")
@Slf4j
@Validated
public class ConsultationController {

    @Autowired
    private ConsultationService consultationService;

    @Autowired
    private SecurityUtils securityUtils;
    
    /**
     * 创建问诊会话
     * 功能: 居民向指定医生发起一次新的图文问诊
     * 权限: 居民 (Resident)
     */
    @PostMapping
    public Result<ConsultationListDTO> createConsultation(@Valid @RequestBody ConsultationCreateDTO createDTO, HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            String userRole = securityUtils.getCurrentUser(request).getRole().name();

            // 只有居民可以发起问诊
            if (!"RESIDENT".equals(userRole)) {
                return Result.error("只有居民用户可以发起问诊");
            }

            ConsultationListDTO result = consultationService.createConsultation(userId, createDTO);
            return Result.success("问诊会话创建成功", result);
        } catch (Exception e) {
            log.error("创建问诊会话失败", e);
            return Result.error("创建问诊会话失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取问诊列表
     * 功能: 获取当前用户（居民或医生）的所有问诊会话列表
     * 权限: 居民 (Resident) 或 医生 (Doctor)
     */
    @GetMapping
    public Result<Page<ConsultationListDTO>> getConsultations(
            @RequestParam(defaultValue = "1") @Min(1) int page,
            @RequestParam(defaultValue = "10") @Min(1) int size,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            String userRole = securityUtils.getCurrentUser(request).getRole().name();

            // 只有居民和医生可以查看问诊列表
            if (!"RESIDENT".equals(userRole) && !"DOCTOR".equals(userRole)) {
                return Result.error("无权限访问问诊列表");
            }

            Page<ConsultationListDTO> result = consultationService.getConsultations(userId, userRole, page, size);
            return Result.success("获取问诊列表成功", result);
        } catch (Exception e) {
            log.error("获取问诊列表失败", e);
            return Result.error("获取问诊列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送消息
     * 功能: 在指定的问诊会话中发送一条新消息
     * 权限: 参与该会话的居民或医生
     */
    @PostMapping("/{consultationId}/messages")
    public Result<MessageListDTO> sendMessage(
            @PathVariable Long consultationId,
            @Valid @RequestBody MessageCreateDTO messageDTO,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            String userRole = securityUtils.getCurrentUser(request).getRole().name();

            // 只有居民和医生可以发送消息
            if (!"RESIDENT".equals(userRole) && !"DOCTOR".equals(userRole)) {
                return Result.error("无权限发送消息");
            }

            MessageListDTO result = consultationService.sendMessage(consultationId, userId, messageDTO);
            return Result.success("消息发送成功", result);
        } catch (Exception e) {
            log.error("发送消息失败", e);
            return Result.error("发送消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取消息历史记录
     * 功能: 获取指定问诊会话的历史消息
     * 权限: 参与该会话的居民或医生
     */
    @GetMapping("/{consultationId}/messages")
    public Result<Page<MessageListDTO>> getMessages(
            @PathVariable Long consultationId,
            @RequestParam(defaultValue = "1") @Min(1) int page,
            @RequestParam(defaultValue = "20") @Min(1) int size,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            String userRole = securityUtils.getCurrentUser(request).getRole().name();

            // 只有居民和医生可以查看消息
            if (!"RESIDENT".equals(userRole) && !"DOCTOR".equals(userRole)) {
                return Result.error("无权限查看消息");
            }

            Page<MessageListDTO> result = consultationService.getMessages(consultationId, userId, page, size);
            return Result.success("获取消息历史成功", result);
        } catch (Exception e) {
            log.error("获取消息历史失败", e);
            return Result.error("获取消息历史失败: " + e.getMessage());
        }
    }
    
    /**
     * 完成问诊（仅医生可操作）
     * 功能: 医生标记问诊会话为已完成
     * 权限: 负责该问诊的医生
     */
    @PutMapping("/{consultationId}/complete")
    public Result<String> completeConsultation(@PathVariable Long consultationId, HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            String userRole = securityUtils.getCurrentUser(request).getRole().name();

            // 只有医生可以完成问诊
            if (!"DOCTOR".equals(userRole)) {
                return Result.error("只有医生可以完成问诊");
            }

            consultationService.completeConsultation(consultationId, userId);
            return Result.success("问诊已完成");
        } catch (Exception e) {
            log.error("完成问诊失败", e);
            return Result.error("完成问诊失败: " + e.getMessage());
        }
    }
}
