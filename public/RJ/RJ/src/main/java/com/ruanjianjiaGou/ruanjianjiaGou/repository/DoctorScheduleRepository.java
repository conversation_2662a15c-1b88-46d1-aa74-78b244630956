package com.ruanjianjiaGou.ruanjianjiaGou.repository;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.DoctorSchedule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface DoctorScheduleRepository extends JpaRepository<DoctorSchedule, Long> {
    
    /**
     * 根据医生ID和日期查找排班
     */
    List<DoctorSchedule> findByDoctorIdAndScheduleDate(Long doctorId, LocalDate scheduleDate);
    
    /**
     * 根据医生ID查找指定日期范围内的排班
     */
    List<DoctorSchedule> findByDoctorIdAndScheduleDateBetweenOrderByScheduleDateAscStartTimeAsc(
        Long doctorId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 查找指定日期范围内有可预约号源的排班
     */
    @Query("SELECT s FROM DoctorSchedule s WHERE s.doctorId = :doctorId " +
           "AND s.scheduleDate BETWEEN :startDate AND :endDate " +
           "AND s.bookedSlots < s.totalSlots " +
           "ORDER BY s.scheduleDate ASC, s.startTime ASC")
    List<DoctorSchedule> findAvailableSchedules(@Param("doctorId") Long doctorId,
                                               @Param("startDate") LocalDate startDate,
                                               @Param("endDate") LocalDate endDate);
    
    /**
     * 查找指定日期有可预约号源的所有排班
     */
    @Query("SELECT s FROM DoctorSchedule s WHERE s.scheduleDate = :date " +
           "AND s.bookedSlots < s.totalSlots " +
           "ORDER BY s.startTime ASC")
    List<DoctorSchedule> findAvailableSchedulesByDate(@Param("date") LocalDate date);
    
    /**
     * 检查医生在指定时间是否已有排班
     */
    @Query("SELECT COUNT(s) > 0 FROM DoctorSchedule s WHERE s.doctorId = :doctorId " +
           "AND s.scheduleDate = :date " +
           "AND ((s.startTime <= :startTime AND s.endTime > :startTime) " +
           "OR (s.startTime < :endTime AND s.endTime >= :endTime) " +
           "OR (s.startTime >= :startTime AND s.endTime <= :endTime))")
    boolean existsConflictingSchedule(@Param("doctorId") Long doctorId,
                                     @Param("date") LocalDate date,
                                     @Param("startTime") LocalTime startTime,
                                     @Param("endTime") LocalTime endTime);
    
    /**
     * 根据ID和医生ID查找排班（用于权限验证）
     */
    Optional<DoctorSchedule> findByIdAndDoctorId(Long id, Long doctorId);
    
    /**
     * 查找医生未来的排班
     */
    @Query("SELECT s FROM DoctorSchedule s WHERE s.doctorId = :doctorId " +
           "AND s.scheduleDate >= :currentDate " +
           "ORDER BY s.scheduleDate ASC, s.startTime ASC")
    List<DoctorSchedule> findFutureSchedules(@Param("doctorId") Long doctorId,
                                           @Param("currentDate") LocalDate currentDate);
    
    /**
     * 统计医生的排班数量
     */
    long countByDoctorId(Long doctorId);
    
    /**
     * 统计指定日期范围内的排班数量
     */
    long countByDoctorIdAndScheduleDateBetween(Long doctorId, LocalDate startDate, LocalDate endDate);
}
