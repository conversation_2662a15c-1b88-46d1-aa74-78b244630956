package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.dto.HealthRecordCreateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.HealthRecordQueryDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.HealthMetricRecord;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.HealthMetricRecordRepository;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.HealthStatisticsVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.DoubleSummaryStatistics;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class HealthRecordService {
    
    @Autowired
    private HealthMetricRecordRepository recordRepository;
    
    @Autowired
    private HealthProfileService profileService;
    
    /**
     * 添加健康数据记录
     */
    public HealthMetricRecord addRecord(Long userId, HealthRecordCreateDTO dto) {
        // 验证档案权限
        profileService.validateProfileAccess(userId, dto.getProfileId());
        
        HealthMetricRecord record = new HealthMetricRecord();
        BeanUtils.copyProperties(dto, record);
        
        // 如果没有指定记录时间，使用当前时间
        if (record.getRecordedAt() == null) {
            record.setRecordedAt(LocalDateTime.now());
        }
        
        return recordRepository.save(record);
    }
    
    /**
     * 获取健康数据记录列表（分页）
     */
    @Transactional(readOnly = true)
    public Page<HealthMetricRecord> getRecords(Long userId, HealthRecordQueryDTO query) {
        // 验证档案权限
        profileService.validateProfileAccess(userId, query.getProfileId());
        
        Pageable pageable = PageRequest.of(query.getPage() - 1, query.getSize(), 
            Sort.by(Sort.Direction.DESC, "recordedAt"));
        
        // 构建查询条件
        Specification<HealthMetricRecord> spec = buildSpecification(query);
        
        return recordRepository.findAll(spec, pageable);
    }
    
    /**
     * 获取健康数据统计
     */
    @Transactional(readOnly = true)
    public HealthStatisticsVO getStatistics(Long userId, Long profileId, String metricType, String period) {
        // 验证档案权限
        profileService.validateProfileAccess(userId, profileId);
        
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = calculateStartTime(endTime, period);
        
        List<HealthMetricRecord> records = recordRepository.findByProfileIdAndMetricTypeAndRecordedAtBetweenOrderByRecordedAt(
            profileId, metricType, startTime, endTime);
        
        if (records.isEmpty()) {
            return new HealthStatisticsVO(metricType, period, null, Collections.emptyList());
        }
        
        // 计算统计数据
        DoubleSummaryStatistics stats = records.stream()
            .mapToDouble(r -> r.getMetricValue().doubleValue())
            .summaryStatistics();
        
        HealthStatisticsVO.StatisticsData statisticsData = new HealthStatisticsVO.StatisticsData();
        statisticsData.setAverage(BigDecimal.valueOf(stats.getAverage()).setScale(2, RoundingMode.HALF_UP));
        statisticsData.setMax(BigDecimal.valueOf(stats.getMax()));
        statisticsData.setMin(BigDecimal.valueOf(stats.getMin()));
        statisticsData.setTrend(calculateTrend(records));
        
        // 构建图表数据
        List<HealthStatisticsVO.ChartDataPoint> chartData = records.stream()
            .map(r -> new HealthStatisticsVO.ChartDataPoint(r.getRecordedAt().toLocalDate(), r.getMetricValue()))
            .collect(Collectors.toList());
        
        return new HealthStatisticsVO(metricType, period, statisticsData, chartData);
    }
    
    /**
     * 更新健康数据记录
     */
    public void updateRecord(Long userId, Long recordId, HealthRecordCreateDTO dto) {
        HealthMetricRecord record = recordRepository.findById(recordId)
            .orElseThrow(() -> new RuntimeException("健康数据记录不存在"));
        
        // 验证权限：通过档案验证
        profileService.validateProfileAccess(userId, record.getProfileId());
        
        // 更新字段
        if (dto.getMetricValue() != null) {
            record.setMetricValue(dto.getMetricValue());
        }
        if (dto.getSystolicPressure() != null) {
            record.setSystolicPressure(dto.getSystolicPressure());
        }
        if (dto.getDiastolicPressure() != null) {
            record.setDiastolicPressure(dto.getDiastolicPressure());
        }
        if (dto.getUnit() != null) {
            record.setUnit(dto.getUnit());
        }
        if (dto.getNotes() != null) {
            record.setNotes(dto.getNotes());
        }
        if (dto.getRecordedAt() != null) {
            record.setRecordedAt(dto.getRecordedAt());
        }
        
        recordRepository.save(record);
    }
    
    /**
     * 删除健康数据记录
     */
    public void deleteRecord(Long userId, Long recordId) {
        HealthMetricRecord record = recordRepository.findById(recordId)
            .orElseThrow(() -> new RuntimeException("健康数据记录不存在"));
        
        // 验证权限
        profileService.validateProfileAccess(userId, record.getProfileId());
        
        recordRepository.delete(record);
    }
    
    /**
     * 构建查询条件
     */
    private Specification<HealthMetricRecord> buildSpecification(HealthRecordQueryDTO query) {
        Specification<HealthMetricRecord> spec = Specification.where(null);
        
        spec = spec.and((root, criteriaQuery, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("profileId"), query.getProfileId()));
        
        if (query.getMetricType() != null) {
            spec = spec.and((root, criteriaQuery, criteriaBuilder) -> 
                criteriaBuilder.equal(root.get("metricType"), query.getMetricType()));
        }
        
        if (query.getStartDate() != null) {
            spec = spec.and((root, criteriaQuery, criteriaBuilder) -> 
                criteriaBuilder.greaterThanOrEqualTo(root.get("recordedAt"), query.getStartDate().atStartOfDay()));
        }
        
        if (query.getEndDate() != null) {
            spec = spec.and((root, criteriaQuery, criteriaBuilder) -> 
                criteriaBuilder.lessThanOrEqualTo(root.get("recordedAt"), query.getEndDate().atTime(23, 59, 59)));
        }
        
        return spec;
    }
    
    /**
     * 计算开始时间
     */
    private LocalDateTime calculateStartTime(LocalDateTime endTime, String period) {
        switch (period) {
            case "week":
                return endTime.minusWeeks(1);
            case "month":
                return endTime.minusMonths(1);
            case "year":
                return endTime.minusYears(1);
            default:
                return endTime.minusMonths(1);
        }
    }
    
    /**
     * 计算趋势
     */
    private String calculateTrend(List<HealthMetricRecord> records) {
        if (records.size() < 2) return "insufficient_data";
        
        // 简单的趋势计算：比较前半段和后半段的平均值
        int midPoint = records.size() / 2;
        double firstHalfAvg = records.subList(0, midPoint).stream()
            .mapToDouble(r -> r.getMetricValue().doubleValue())
            .average().orElse(0);
        double secondHalfAvg = records.subList(midPoint, records.size()).stream()
            .mapToDouble(r -> r.getMetricValue().doubleValue())
            .average().orElse(0);
        
        double changePercent = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100;
        
        if (Math.abs(changePercent) < 5) return "stable";
        return changePercent > 0 ? "increasing" : "decreasing";
    }
}
