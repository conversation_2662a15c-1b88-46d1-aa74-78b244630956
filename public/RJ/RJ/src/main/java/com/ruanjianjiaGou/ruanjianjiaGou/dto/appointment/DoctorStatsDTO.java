package com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 医生统计DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DoctorStatsDTO {
    
    private Long doctorId;
    private String doctorName;
    private String departmentName;
    private Long totalSchedules;         // 总排班数
    private Long totalAppointments;      // 总预约数
    private Long completedAppointments;  // 已完成预约数
    private Long cancelledAppointments;  // 已取消预约数
    private Long todaySchedules;         // 今日排班数
    private Long todayAppointments;      // 今日预约数
    private Long todayAvailableSlots;    // 今日可预约号源
    private Long todayBookedSlots;       // 今日已预约号源
    private Double averageRating;        // 平均评分
    private Double appointmentRate;      // 预约率（已预约/总号源）
}
