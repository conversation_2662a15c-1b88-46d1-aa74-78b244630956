package com.ruanjianjiaGou.ruanjianjiaGou.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
public class HealthStatisticsVO {
    private String metricType;
    private String period;
    private StatisticsData statistics;
    private List<ChartDataPoint> chartData;
    
    public HealthStatisticsVO(String metricType, String period, StatisticsData statistics, List<ChartDataPoint> chartData) {
        this.metricType = metricType;
        this.period = period;
        this.statistics = statistics;
        this.chartData = chartData;
    }
    
    @Data
    public static class StatisticsData {
        private BigDecimal average;
        private BigDecimal max;
        private BigDecimal min;
        private String trend; // "increasing", "decreasing", "stable", "insufficient_data"
    }
    
    @Data
    public static class ChartDataPoint {
        private LocalDate date;
        private BigDecimal value;
        
        public ChartDataPoint(LocalDate date, BigDecimal value) {
            this.date = date;
            this.value = value;
        }
    }
}
