package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.HealthReminderCreateDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.HealthReminder;
import com.ruanjianjiaGou.ruanjianjiaGou.service.HealthReminderService;
import com.ruanjianjiaGou.ruanjianjiaGou.utils.SecurityUtils;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.HealthReminderVO;
import com.ruanjianjiaGou.ruanjianjiaGou.vo.TodayReminderVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/health/reminders")
public class HealthReminderController {
    
    @Autowired
    private HealthReminderService reminderService;
    
    @Autowired
    private SecurityUtils securityUtils;
    
    /**
     * 创建健康提醒
     */
    @PostMapping
    public Result<Map<String, Object>> createReminder(@Valid @RequestBody HealthReminderCreateDTO dto,
                                                     HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            HealthReminder reminder = reminderService.createReminder(userId, dto);
            
            Map<String, Object> result = new HashMap<>();
            result.put("id", reminder.getId());
            result.put("title", reminder.getTitle());
            result.put("nextReminderTime", reminder.getNextReminderTime());
            
            return Result.success("健康提醒创建成功", result);
        } catch (RuntimeException e) {
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            return Result.error("创建失败，请稍后重试");
        }
    }
    
    /**
     * 获取健康提醒列表
     */
    @GetMapping
    public Result<Map<String, Object>> getReminders(@RequestParam(required = false) String status,
                                                   @RequestParam(required = false) String type,
                                                   @RequestParam(defaultValue = "1") int page,
                                                   @RequestParam(defaultValue = "10") int size,
                                                   HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            Page<HealthReminder> reminderPage = reminderService.getUserReminders(userId, status, type, page, size);

            List<HealthReminderVO> reminders = reminderPage.getContent().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

            Map<String, Object> result = new HashMap<>();
            result.put("reminders", reminders);

            Map<String, Object> pagination = new HashMap<>();
            pagination.put("currentPage", reminderPage.getNumber() + 1);
            pagination.put("totalPages", reminderPage.getTotalPages());
            pagination.put("totalRecords", reminderPage.getTotalElements());
            pagination.put("pageSize", reminderPage.getSize());
            result.put("pagination", pagination);

            return Result.success("获取成功", result);
        } catch (Exception e) {
            return Result.error("获取失败，请稍后重试");
        }
    }
    
    /**
     * 获取今日提醒
     */
    @GetMapping("/today")
    public Result<List<TodayReminderVO>> getTodayReminders(HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            List<TodayReminderVO> reminders = reminderService.getTodayReminders(userId);
            return Result.success("获取成功", reminders);
        } catch (Exception e) {
            return Result.error("获取失败，请稍后重试");
        }
    }
    
    /**
     * 标记提醒完成
     */
    @PostMapping("/{id}/complete")
    public Result<Map<String, Object>> completeReminder(@PathVariable Long id,
                                                       HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            HealthReminder reminder = reminderService.completeReminder(userId, id);
            
            Map<String, Object> result = new HashMap<>();
            result.put("nextReminderTime", reminder.getNextReminderTime());
            
            return Result.success("提醒已标记为完成", result);
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            return Result.error("操作失败，请稍后重试");
        }
    }
    
    /**
     * 更新健康提醒
     */
    @PutMapping("/{id}")
    public Result<Void> updateReminder(@PathVariable Long id,
                                      @Valid @RequestBody HealthReminderCreateDTO dto,
                                      HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            reminderService.updateReminder(userId, id, dto);
            return Result.success("健康提醒更新成功");
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            return Result.error("更新失败，请稍后重试");
        }
    }
    
    /**
     * 删除健康提醒
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteReminder(@PathVariable Long id,
                                      HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            reminderService.deleteReminder(userId, id);
            return Result.success("健康提醒删除成功");
        } catch (RuntimeException e) {
            return Result.error(403, e.getMessage());
        } catch (Exception e) {
            return Result.error("删除失败，请稍后重试");
        }
    }
    
    /**
     * 转换为VO对象
     */
    private HealthReminderVO convertToVO(HealthReminder reminder) {
        HealthReminderVO vo = new HealthReminderVO();
        BeanUtils.copyProperties(reminder, vo);
        return vo;
    }
}
