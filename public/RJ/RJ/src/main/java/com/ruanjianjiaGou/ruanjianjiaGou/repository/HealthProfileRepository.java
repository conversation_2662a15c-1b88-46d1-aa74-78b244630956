package com.ruanjianjiaGou.ruanjianjiaGou.repository;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.HealthProfile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface HealthProfileRepository extends JpaRepository<HealthProfile, Long> {
    
    /**
     * 根据管理用户ID查找健康档案列表
     */
    List<HealthProfile> findByManagingUserId(Long managingUserId);

    /**
     * 根据管理用户ID查找健康档案列表（分页）
     */
    Page<HealthProfile> findByManagingUserId(Long managingUserId, Pageable pageable);

    /**
     * 根据ID和管理用户ID查找健康档案（用于权限验证）
     */
    Optional<HealthProfile> findByIdAndManagingUserId(Long id, Long managingUserId);

    /**
     * 统计用户管理的档案数量
     */
    long countByManagingUserId(Long managingUserId);
}
