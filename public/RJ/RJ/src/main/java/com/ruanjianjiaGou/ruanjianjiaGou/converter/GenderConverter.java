package com.ruanjianjiaGou.ruanjianjiaGou.converter;

import com.ruanjianjiaGou.ruanjianjiaGou.enums.Gender;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * 性别枚举转换器
 * 将数据库中的字符串转换为Java枚举
 */
@Converter(autoApply = true)
public class GenderConverter implements AttributeConverter<Gender, String> {

    @Override
    public String convertToDatabaseColumn(Gender attribute) {
        if (attribute == null) {
            return null;
        }
        // 将枚举转换为小写字符串存储到数据库
        return attribute.name().toLowerCase();
    }

    @Override
    public Gender convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.trim().isEmpty()) {
            return null;
        }
        
        // 将数据库中的字符串转换为枚举
        String normalizedData = dbData.trim().toLowerCase();
        
        switch (normalizedData) {
            case "male":
                return Gender.MALE;
            case "female":
                return Gender.FEMALE;
            case "other":
                return Gender.OTHER;
            default:
                // 如果是大写的枚举名称，也要处理
                try {
                    return Gender.valueOf(dbData.toUpperCase());
                } catch (IllegalArgumentException e) {
                    throw new IllegalArgumentException("Unknown gender: " + dbData);
                }
        }
    }
}
