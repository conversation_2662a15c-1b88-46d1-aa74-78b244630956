package com.ruanjianjiaGou.ruanjianjiaGou.entity;

import com.ruanjianjiaGou.ruanjianjiaGou.converter.UserRoleConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 用户实体类
 * 对应数据库表：users
 */
@Data
@Entity
@Table(name = "users", indexes = {
    @Index(name = "idx_phone_number", columnList = "phone_number"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@EqualsAndHashCode(of = "id")
@ToString(exclude = "password")
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Column(name = "phone_number", unique = true, nullable = false, length = 20)
    private String phoneNumber;

    @NotBlank(message = "密码不能为空")
    @Size(max = 50, message = "密码长度不能超过50个字符")
    @Column(name = "password", nullable = false, length = 50)
    private String password;

    @Size(max = 50, message = "昵称长度不能超过50个字符")
    @Column(name = "nickname", length = 50)
    private String nickname;

    @Convert(converter = UserRoleConverter.class)
    @Column(name = "role", nullable = false)
    private UserRole role = UserRole.RESIDENT;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 用户角色枚举
     */
    public enum UserRole {
        RESIDENT("resident", "居民"),
        DOCTOR("doctor", "医生"),
        ADMIN("admin", "管理员");

        private final String code;
        private final String description;

        UserRole(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查是否为医生
     */
    public boolean isDoctor() {
        return UserRole.DOCTOR.equals(this.role);
    }

    /**
     * 检查是否为管理员
     */
    public boolean isAdmin() {
        return UserRole.ADMIN.equals(this.role);
    }

    /**
     * 检查是否为居民
     */
    public boolean isResident() {
        return UserRole.RESIDENT.equals(this.role);
    }
}
