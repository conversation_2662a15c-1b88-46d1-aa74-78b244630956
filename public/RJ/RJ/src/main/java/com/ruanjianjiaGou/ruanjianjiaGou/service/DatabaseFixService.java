package com.ruanjianjiaGou.ruanjianjiaGou.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据库修复服务
 * 用于修复数据库中的数据不一致问题
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Order(1) // 确保在DataInitService之前执行
public class DatabaseFixService implements CommandLineRunner {
    
    private final JdbcTemplate jdbcTemplate;
    
    @Override
    @Transactional
    public void run(String... args) throws Exception {
        fixDoctorStatus();
    }
    
    /**
     * 修复医生状态数据
     */
    @Transactional
    private void fixDoctorStatus() {
        try {
            log.info("开始修复医生状态数据...");

            // 查看当前状态分布
            String querySQL = "SELECT status, COUNT(*) as count FROM doctors GROUP BY status";
            jdbcTemplate.query(querySQL, (rs) -> {
                log.info("当前医生状态: {} = {}", rs.getString("status"), rs.getInt("count"));
            });

            // 更新所有可能的小写状态为大写
            int approvedCount = jdbcTemplate.update("UPDATE doctors SET status = 'APPROVED' WHERE status = 'approved'");
            int pendingCount = jdbcTemplate.update("UPDATE doctors SET status = 'PENDING' WHERE status = 'pending'");
            int rejectedCount = jdbcTemplate.update("UPDATE doctors SET status = 'REJECTED' WHERE status = 'rejected'");

            // 对于任何其他状态，默认设为APPROVED
            int otherCount = jdbcTemplate.update("UPDATE doctors SET status = 'APPROVED' WHERE status NOT IN ('APPROVED', 'PENDING', 'REJECTED')");

            log.info("医生状态数据修复完成: APPROVED={}, PENDING={}, REJECTED={}, OTHER->APPROVED={}",
                    approvedCount, pendingCount, rejectedCount, otherCount);

            // 再次查看修复后的状态分布
            log.info("修复后的医生状态分布:");
            jdbcTemplate.query(querySQL, (rs) -> {
                log.info("修复后状态: {} = {}", rs.getString("status"), rs.getInt("count"));
            });

        } catch (Exception e) {
            log.error("修复医生状态数据失败", e);
        }
    }
}
