package com.ruanjianjiaGou.ruanjianjiaGou.dto;

import com.ruanjianjiaGou.ruanjianjiaGou.enums.Gender;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDate;

@Data
public class UserUpdateDTO {

    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;

    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;

    private Gender gender;

    private LocalDate birthDate;

    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$",
             message = "身份证号格式不正确")
    private String idCardNumber;

    @Size(max = 255, message = "头像URL长度不能超过255个字符")
    private String avatarUrl;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phoneNumber;
}
