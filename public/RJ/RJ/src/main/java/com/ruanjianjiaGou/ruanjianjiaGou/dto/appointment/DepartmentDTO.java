package com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 科室DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentDTO {
    
    private Long id;
    private String name;
    private String description;
    private Long doctorCount; // 科室医生数量
    
    public DepartmentDTO(Long id, String name, String description) {
        this.id = id;
        this.name = name;
        this.description = description;
    }
}
