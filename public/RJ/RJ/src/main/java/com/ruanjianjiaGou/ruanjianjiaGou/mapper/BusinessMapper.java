package com.ruanjianjiaGou.ruanjianjiaGou.mapper;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.Business;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface BusinessMapper {
    
    @Select("SELECT * FROM business")
    List<Business> findAll();
    
    @Select("SELECT * FROM business WHERE business_id = #{businessId}")
    Business findById(@Param("businessId") Integer businessId);
    
    @Insert("INSERT INTO business(password, business_name, business_address, x_address, " +
            "y_address, business_img, star_price, delivery_price, business_explain) " +
            "VALUES(#{password}, #{businessName}, #{businessAddress}, #{XAddress}, " +
            "#{YAddress}, #{businessImg}, #{starPrice}, #{deliveryPrice}, #{businessExplain})")
    @Options(useGeneratedKeys = true, keyProperty = "businessId")
    int insert(Business business);
    
    @Update("UPDATE business SET password=#{password}, business_name=#{businessName}, " +
            "business_address=#{businessAddress}, x_address=#{XAddress}, y_address=#{YAddress}, " +
            "business_img=#{businessImg}, star_price=#{starPrice}, delivery_price=#{deliveryPrice}, " +
            "business_explain=#{businessExplain} WHERE business_id=#{businessId}")
    int update(Business business);
    
    @Delete("DELETE FROM business WHERE business_id = #{businessId}")
    int deleteById(@Param("businessId") Integer businessId);
    
    @Select("SELECT business_id, business_name, business_explain, business_img " +
            "FROM business")
    List<Business> findBusinessBriefList();
}
