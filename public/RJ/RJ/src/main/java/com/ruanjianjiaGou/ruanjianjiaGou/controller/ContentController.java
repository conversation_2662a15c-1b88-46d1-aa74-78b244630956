package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.content.*;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.Content;
import com.ruanjianjiaGou.ruanjianjiaGou.service.ContentService;
import com.ruanjianjiaGou.ruanjianjiaGou.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Min;

/**
 * 内容管理控制器
 */
@RestController
@RequestMapping("/api")
@Slf4j
@Validated
public class ContentController {
    
    @Autowired
    private ContentService contentService;
    
    @Autowired
    private SecurityUtils securityUtils;
    
    /**
     * 发布/创建内容
     * 功能: 管理员或医生创建内容（文章、活动、健康指导）
     * 权限: 管理员 (Admin) 或 医生 (Doctor)。医生只能发布"健康指导"类型
     */
    @PostMapping("/contents")
    public Result<ContentDetailDTO> createContent(@Valid @RequestBody ContentCreateDTO createDTO, HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            String userRole = securityUtils.getCurrentUser(request).getRole().name();
            
            ContentDetailDTO result = contentService.createContent(userId, userRole, createDTO);
            return Result.success("内容创建成功", result);
        } catch (Exception e) {
            log.error("创建内容失败", e);
            return Result.error("创建内容失败: " + e.getMessage());
        }
    }
    
    /**
     * 编辑/更新内容
     * 功能: 管理员或内容的原始作者可以更新已发布的内容
     * 权限: 管理员 (Admin)，或该内容的创建者
     */
    @PutMapping("/contents/{contentId}")
    public Result<ContentDetailDTO> updateContent(
            @PathVariable Long contentId,
            @Valid @RequestBody ContentUpdateDTO updateDTO,
            HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            String userRole = securityUtils.getCurrentUser(request).getRole().name();
            
            ContentDetailDTO result = contentService.updateContent(contentId, userId, userRole, updateDTO);
            return Result.success("内容更新成功", result);
        } catch (Exception e) {
            log.error("更新内容失败", e);
            return Result.error("更新内容失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除内容
     * 功能: 管理员或内容的原始作者可以删除已发布的内容
     * 权限: 管理员 (Admin)，或该内容的创建者
     */
    @DeleteMapping("/contents/{contentId}")
    public Result<String> deleteContent(@PathVariable Long contentId, HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            String userRole = securityUtils.getCurrentUser(request).getRole().name();
            
            contentService.deleteContent(contentId, userId, userRole);
            return Result.success("内容删除成功");
        } catch (Exception e) {
            log.error("删除内容失败", e);
            return Result.error("删除内容失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取内容列表
     * 功能: 公开浏览资讯、活动或健康指导的列表
     * 权限: 任何人（公开接口）
     */
    @GetMapping("/contents")
    public Result<Page<ContentListDTO>> getContentList(
            @RequestParam(required = false) Content.ContentType contentType,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "1") @Min(1) int page,
            @RequestParam(defaultValue = "10") @Min(1) int size,
            HttpServletRequest request) {
        try {
            // 获取当前用户ID（如果已登录）
            Long currentUserId = null;
            try {
                currentUserId = securityUtils.getCurrentUserId(request);
            } catch (Exception e) {
                // 未登录用户，currentUserId保持为null
            }
            
            Page<ContentListDTO> result = contentService.getContentList(contentType, keyword, currentUserId, page, size);
            return Result.success("获取内容列表成功", result);
        } catch (Exception e) {
            log.error("获取内容列表失败", e);
            return Result.error("获取内容列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取内容详情
     * 功能: 查看某篇文章或活动的详细信息
     * 权限: 任何人（公开接口）
     */
    @GetMapping("/contents/{contentId}")
    public Result<ContentDetailDTO> getContentDetail(@PathVariable Long contentId, HttpServletRequest request) {
        try {
            // 获取当前用户信息（如果已登录）
            Long currentUserId = null;
            String userRole = null;
            try {
                currentUserId = securityUtils.getCurrentUserId(request);
                userRole = securityUtils.getCurrentUser(request).getRole().name();
            } catch (Exception e) {
                // 未登录用户，保持为null
            }
            
            ContentDetailDTO result = contentService.getContentDetail(contentId, currentUserId, userRole);
            return Result.success("获取内容详情成功", result);
        } catch (Exception e) {
            log.error("获取内容详情失败", e);
            return Result.error("获取内容详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 报名参加活动
     * 功能: 居民报名参加一个社区活动
     * 权限: 居民 (Resident)
     */
    @PostMapping("/activities/{activityId}/register")
    public Result<String> registerActivity(@PathVariable Long activityId, HttpServletRequest request) {
        try {
            Long userId = securityUtils.getCurrentUserId(request);
            String userRole = securityUtils.getCurrentUser(request).getRole().name();
            
            // 只有居民可以报名活动
            if (!"RESIDENT".equals(userRole)) {
                return Result.error("只有居民可以报名参加活动");
            }
            
            contentService.registerActivity(activityId, userId);
            return Result.success("活动报名成功");
        } catch (Exception e) {
            log.error("活动报名失败", e);
            return Result.error("活动报名失败: " + e.getMessage());
        }
    }
}
