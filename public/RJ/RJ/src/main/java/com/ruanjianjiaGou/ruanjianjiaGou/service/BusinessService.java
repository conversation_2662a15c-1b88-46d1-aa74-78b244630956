package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.dto.BusinessBriefDTO;
import com.ruanjianjiaGou.ruanjianjiaGou.mapper.BusinessMapper;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.Business;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class BusinessService {
    
    @Autowired
    private BusinessMapper businessMapper;
    
    public List<BusinessBriefDTO> getBusinessBriefList() {
        List<Business> businesses = businessMapper.findBusinessBriefList();
        return businesses.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    private BusinessBriefDTO convertToDTO(Business business) {
        BusinessBriefDTO dto = new BusinessBriefDTO();
        BeanUtils.copyProperties(business, dto);
        return dto;
    }
}
