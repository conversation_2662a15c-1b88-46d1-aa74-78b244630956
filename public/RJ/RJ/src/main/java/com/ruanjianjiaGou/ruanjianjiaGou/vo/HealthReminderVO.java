package com.ruanjianjiaGou.ruanjianjiaGou.vo;

import com.ruanjianjiaGou.ruanjianjiaGou.enums.FrequencyType;
import com.ruanjianjiaGou.ruanjianjiaGou.enums.ReminderType;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class HealthReminderVO {
    private Long id;
    private String title;
    private String content;
    private ReminderType reminderType;
    private FrequencyType frequencyType;
    private String frequencyValue;
    private LocalDate startDate;
    private LocalDate endDate;
    private LocalDateTime nextReminderTime;
    private Boolean isActive;
    private LocalDateTime createdAt;
}
