package com.ruanjianjiaGou.ruanjianjiaGou.dto.content;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 更新内容请求DTO
 */
@Data
public class ContentUpdateDTO {
    
    @NotBlank(message = "标题不能为空")
    private String title;
    
    @NotBlank(message = "内容不能为空")
    private String body;
    
    private LocalDateTime activityTime; // 活动时间（仅对活动类型有效）
    
    private String activityLocation; // 活动地点（仅对活动类型有效）
}
