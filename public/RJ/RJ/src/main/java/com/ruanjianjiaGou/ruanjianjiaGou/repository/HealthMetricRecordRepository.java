package com.ruanjianjiaGou.ruanjianjiaGou.repository;

import com.ruanjianjiaGou.ruanjianjiaGou.entity.HealthMetricRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface HealthMetricRecordRepository extends JpaRepository<HealthMetricRecord, Long>, JpaSpecificationExecutor<HealthMetricRecord> {
    
    /**
     * 根据档案ID和指标类型查找记录
     */
    List<HealthMetricRecord> findByProfileIdAndMetricTypeOrderByRecordedAtDesc(Long profileId, String metricType);
    
    /**
     * 根据档案ID、指标类型和时间范围查找记录
     */
    List<HealthMetricRecord> findByProfileIdAndMetricTypeAndRecordedAtBetweenOrderByRecordedAt(
        Long profileId, String metricType, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据档案ID查找最近的记录
     */
    Page<HealthMetricRecord> findByProfileIdOrderByRecordedAtDesc(Long profileId, Pageable pageable);
    
    /**
     * 统计档案的记录数量
     */
    long countByProfileId(Long profileId);
    
    /**
     * 获取档案的最新记录（按指标类型分组）
     */
    @Query("SELECT r FROM HealthMetricRecord r WHERE r.profileId = :profileId " +
           "AND r.recordedAt = (SELECT MAX(r2.recordedAt) FROM HealthMetricRecord r2 " +
           "WHERE r2.profileId = :profileId AND r2.metricType = r.metricType)")
    List<HealthMetricRecord> findLatestRecordsByProfileId(@Param("profileId") Long profileId);

    /**
     * 按指标类型统计记录数量
     */
    @Query("SELECT r.metricType, COUNT(r) FROM HealthMetricRecord r GROUP BY r.metricType")
    List<Object[]> countByMetricType();

    /**
     * 统计指定时间范围内的记录数
     */
    long countByRecordedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
}
