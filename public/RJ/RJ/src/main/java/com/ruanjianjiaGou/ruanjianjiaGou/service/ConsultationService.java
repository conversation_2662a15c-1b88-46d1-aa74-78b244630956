package com.ruanjianjiaGou.ruanjianjiaGou.service;

import com.ruanjianjiaGou.ruanjianjiaGou.dto.consultation.*;
import com.ruanjianjiaGou.ruanjianjiaGou.entity.*;
import com.ruanjianjiaGou.ruanjianjiaGou.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 在线问诊服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ConsultationService {
    
    private final OnlineConsultationRepository consultationRepository;
    private final MessageRepository messageRepository;
    private final UserRepository userRepository;
    private final DoctorRepository doctorRepository;
    
    /**
     * 创建问诊会话
     */
    public ConsultationListDTO createConsultation(Long userId, ConsultationCreateDTO createDTO) {
        log.info("用户 {} 向医生 {} 发起问诊", userId, createDTO.getDoctorId());
        
        // 验证医生是否存在
        Doctor doctor = doctorRepository.findById(createDTO.getDoctorId())
                .orElseThrow(() -> new RuntimeException("医生不存在"));
        
        if (doctor.getStatus() != Doctor.DoctorStatus.APPROVED) {
            throw new RuntimeException("该医生尚未通过审核，无法接受问诊");
        }
        
        // 创建问诊会话
        OnlineConsultation consultation = new OnlineConsultation();
        consultation.setUserId(userId);
        consultation.setDoctorId(createDTO.getDoctorId());
        consultation.setStatus(OnlineConsultation.ConsultationStatus.IN_PROGRESS);
        
        consultation = consultationRepository.save(consultation);
        log.info("问诊会话创建成功，ID: {}", consultation.getId());
        
        // 如果有初始消息，发送第一条消息
        if (createDTO.getInitialMessage() != null && !createDTO.getInitialMessage().trim().isEmpty()) {
            MessageCreateDTO messageDTO = new MessageCreateDTO();
            messageDTO.setContent(createDTO.getInitialMessage());
            sendMessage(consultation.getId(), userId, messageDTO);
        }
        
        return convertToConsultationListDTO(consultation);
    }
    
    /**
     * 获取问诊列表
     */
    @Transactional(readOnly = true)
    public Page<ConsultationListDTO> getConsultations(Long userId, String userRole, int page, int size) {
        log.info("获取用户 {} (角色: {}) 的问诊列表，页码: {}, 大小: {}", userId, userRole, page, size);
        
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<OnlineConsultation> consultations;
        
        if ("DOCTOR".equals(userRole)) {
            consultations = consultationRepository.findByDoctorId(userId, pageable);
        } else {
            consultations = consultationRepository.findByUserId(userId, pageable);
        }
        
        return consultations.map(this::convertToConsultationListDTO);
    }
    
    /**
     * 发送消息
     */
    public MessageListDTO sendMessage(Long consultationId, Long senderId, MessageCreateDTO messageDTO) {
        log.info("用户 {} 在问诊 {} 中发送消息", senderId, consultationId);
        
        // 验证问诊会话是否存在且用户有权限
        OnlineConsultation consultation = consultationRepository.findById(consultationId)
                .orElseThrow(() -> new RuntimeException("问诊会话不存在"));
        
        if (!consultation.getUserId().equals(senderId) && !consultation.getDoctorId().equals(senderId)) {
            throw new RuntimeException("无权限在此问诊会话中发送消息");
        }
        
        if (consultation.getStatus() == OnlineConsultation.ConsultationStatus.COMPLETED) {
            throw new RuntimeException("问诊会话已结束，无法发送消息");
        }
        
        // 创建消息
        Message message = new Message();
        message.setConsultationId(consultationId);
        message.setSenderId(senderId);
        message.setContent(messageDTO.getContent());
        
        message = messageRepository.save(message);
        log.info("消息发送成功，ID: {}", message.getId());
        
        return convertToMessageListDTO(message);
    }
    
    /**
     * 获取消息历史记录
     */
    @Transactional(readOnly = true)
    public Page<MessageListDTO> getMessages(Long consultationId, Long userId, int page, int size) {
        log.info("获取问诊 {} 的消息历史，用户: {}, 页码: {}, 大小: {}", consultationId, userId, page, size);
        
        // 验证用户是否有权限查看此问诊的消息
        OnlineConsultation consultation = consultationRepository.findById(consultationId)
                .orElseThrow(() -> new RuntimeException("问诊会话不存在"));
        
        if (!consultation.getUserId().equals(userId) && !consultation.getDoctorId().equals(userId)) {
            throw new RuntimeException("无权限查看此问诊会话的消息");
        }
        
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.ASC, "sentAt"));
        Page<Message> messages = messageRepository.findByConsultationIdOrderBySentAt(consultationId, pageable);
        
        return messages.map(this::convertToMessageListDTO);
    }
    
    /**
     * 完成问诊（仅医生可操作）
     */
    public void completeConsultation(Long consultationId, Long doctorId) {
        log.info("医生 {} 完成问诊 {}", doctorId, consultationId);
        
        OnlineConsultation consultation = consultationRepository.findById(consultationId)
                .orElseThrow(() -> new RuntimeException("问诊会话不存在"));
        
        if (!consultation.getDoctorId().equals(doctorId)) {
            throw new RuntimeException("只有负责的医生才能完成问诊");
        }
        
        consultation.setStatus(OnlineConsultation.ConsultationStatus.COMPLETED);
        consultationRepository.save(consultation);
        
        log.info("问诊 {} 已完成", consultationId);
    }
    
    /**
     * 转换为问诊列表DTO
     */
    private ConsultationListDTO convertToConsultationListDTO(OnlineConsultation consultation) {
        ConsultationListDTO dto = new ConsultationListDTO();
        dto.setId(consultation.getId());
        dto.setUserId(consultation.getUserId());
        dto.setDoctorId(consultation.getDoctorId());
        dto.setStatus(consultation.getStatus());
        dto.setStatusDescription(consultation.getStatus().getDescription());
        dto.setCreatedAt(consultation.getCreatedAt());
        
        // 获取用户信息
        userRepository.findById(consultation.getUserId()).ifPresent(user -> {
            dto.setUserNickname(user.getNickname());
            dto.setUserPhoneNumber(user.getPhoneNumber());
        });
        
        // 获取医生信息
        doctorRepository.findById(consultation.getDoctorId()).ifPresent(doctor -> {
            dto.setDoctorName(doctor.getRealName());
            if (doctor.getDepartment() != null) {
                dto.setDepartmentName(doctor.getDepartment().getName());
            }
        });
        
        // 获取最后一条消息
        List<Message> lastMessages = messageRepository.findLatestMessagesByConsultationId(
                consultation.getId(), PageRequest.of(0, 1));
        if (!lastMessages.isEmpty()) {
            Message lastMessage = lastMessages.get(0);
            dto.setLastMessage(lastMessage.getContent());
            dto.setLastMessageTime(lastMessage.getSentAt());
        }
        
        // 获取消息总数
        dto.setMessageCount(messageRepository.countByConsultationId(consultation.getId()));
        
        return dto;
    }
    
    /**
     * 转换为消息列表DTO
     */
    private MessageListDTO convertToMessageListDTO(Message message) {
        MessageListDTO dto = new MessageListDTO();
        dto.setId(message.getId());
        dto.setConsultationId(message.getConsultationId());
        dto.setSenderId(message.getSenderId());
        dto.setContent(message.getContent());
        dto.setSentAt(message.getSentAt());
        
        // 获取发送者信息
        userRepository.findById(message.getSenderId()).ifPresent(user -> {
            dto.setSenderNickname(user.getNickname());
            dto.setSenderRole(user.getRole().name());
        });
        
        return dto;
    }
}
