package com.ruanjianjiaGou.ruanjianjiaGou.config;

import com.ruanjianjiaGou.ruanjianjiaGou.security.CustomUserDetailsService;
import com.ruanjianjiaGou.ruanjianjiaGou.security.JwtAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.password.NoOpPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter {
    
    @Autowired
    private CustomUserDetailsService userDetailsService;
    
    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;
    
    @Bean
    @SuppressWarnings("deprecation")
    public PasswordEncoder passwordEncoder() {
        // 使用明文密码编码器（仅用于开发/测试环境）
        return NoOpPasswordEncoder.getInstance();
    }
    
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }
    
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(userDetailsService).passwordEncoder(passwordEncoder());
    }
    
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.cors().and() // 启用CORS
            .csrf().disable() // 禁用CSRF
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeRequests()
                // 允许所有OPTIONS请求（CORS预检请求）
                .antMatchers(HttpMethod.OPTIONS, "/**").permitAll()
                // 允许注册和登录接口
                .antMatchers(HttpMethod.POST, "/api/user/register", "/api/user/login", "/api/auth/login").permitAll()
                .antMatchers(HttpMethod.GET, "/api/user/login", "/api/auth/login").permitAll() // 允许GET请求用于测试
                // 允许测试接口
                .antMatchers("/api/test/**").permitAll()
                // 允许静态资源
                .antMatchers("/", "/index.html", "/static/**", "/**/*.html", "/**/*.css", "/**/*.js").permitAll()
                // 允许Swagger文档（如果需要）
                .antMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
                // 允许健康检查接口
                .antMatchers("/actuator/health").permitAll()
                // 允许内容管理的公开接口（GET请求）
                .antMatchers(HttpMethod.GET, "/api/contents", "/api/contents/**").permitAll()
                // 内容管理的认证接口
                .antMatchers(HttpMethod.POST, "/api/contents").hasAnyRole("ADMIN", "DOCTOR")
                .antMatchers(HttpMethod.PUT, "/api/contents/**").hasAnyRole("ADMIN", "DOCTOR")
                .antMatchers(HttpMethod.DELETE, "/api/contents/**").hasAnyRole("ADMIN", "DOCTOR")
                .antMatchers(HttpMethod.POST, "/api/activities/*/register").hasRole("RESIDENT")
                // 其他请求需要认证
                .anyRequest().authenticated()
            .and()
            .headers().frameOptions().sameOrigin() // 允许同源iframe
            .and()
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
    }
}
