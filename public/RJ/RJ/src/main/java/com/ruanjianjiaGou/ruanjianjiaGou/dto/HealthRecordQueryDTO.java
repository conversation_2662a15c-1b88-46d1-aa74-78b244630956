package com.ruanjianjiaGou.ruanjianjiaGou.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
public class HealthRecordQueryDTO {
    
    @NotNull(message = "档案ID不能为空")
    private Long profileId;
    
    private String metricType; // 指标类型筛选
    
    private LocalDate startDate; // 开始日期
    
    private LocalDate endDate; // 结束日期
    
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1; // 页码，默认第1页
    
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10; // 每页大小，默认10条
}
