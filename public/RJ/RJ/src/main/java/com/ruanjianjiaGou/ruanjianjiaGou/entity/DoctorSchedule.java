package com.ruanjianjiaGou.ruanjianjiaGou.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 医生排班实体类
 */
@Entity
@Table(name = "doctor_schedules", indexes = {
    @Index(name = "idx_doctor_date", columnList = "doctor_id, schedule_date"),
    @Index(name = "idx_schedule_date", columnList = "schedule_date")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DoctorSchedule {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "doctor_id", nullable = false)
    private Long doctorId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "doctor_id", insertable = false, updatable = false)
    private Doctor doctor;
    
    @Column(name = "schedule_date", nullable = false)
    private LocalDate scheduleDate;
    
    @Column(name = "start_time", nullable = false)
    private LocalTime startTime;
    
    @Column(name = "end_time", nullable = false)
    private LocalTime endTime;
    
    @Column(name = "total_slots", nullable = false)
    private Integer totalSlots;
    
    @Column(name = "booked_slots", nullable = false)
    private Integer bookedSlots = 0;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    // 一对多关系：一个排班可以有多个预约
    @OneToMany(mappedBy = "schedule", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Appointment> appointments;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        if (bookedSlots == null) {
            bookedSlots = 0;
        }
    }
    
    /**
     * 获取剩余可预约数量
     */
    public Integer getAvailableSlots() {
        return totalSlots - bookedSlots;
    }
    
    /**
     * 检查是否还有可预约的号源
     */
    public boolean hasAvailableSlots() {
        return getAvailableSlots() > 0;
    }
    
    /**
     * 预约一个号源
     */
    public boolean bookSlot() {
        if (hasAvailableSlots()) {
            bookedSlots++;
            return true;
        }
        return false;
    }
    
    /**
     * 取消一个号源
     */
    public void cancelSlot() {
        if (bookedSlots > 0) {
            bookedSlots--;
        }
    }
}
