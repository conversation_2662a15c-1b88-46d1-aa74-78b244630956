package com.ruanjianjiaGou.ruanjianjiaGou.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 电子处方实体类
 */
@Entity
@Table(name = "e_prescriptions", indexes = {
    @Index(name = "fk_prescriptions_doctors_idx", columnList = "doctor_id"),
    @Index(name = "fk_prescriptions_profiles_idx", columnList = "profile_id"),
    @Index(name = "fk_prescriptions_consultations_idx", columnList = "consultation_id")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EPrescription {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "doctor_id", nullable = false)
    private Long doctorId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "doctor_id", insertable = false, updatable = false)
    private User doctor;
    
    @Column(name = "profile_id", nullable = false)
    private Long profileId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "profile_id", insertable = false, updatable = false)
    private HealthProfile healthProfile;
    
    @Column(name = "consultation_id")
    private Long consultationId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "consultation_id", insertable = false, updatable = false)
    private OnlineConsultation consultation;
    
    @Column(name = "diagnosis", nullable = false, columnDefinition = "TEXT")
    private String diagnosis;
    
    @Column(name = "medication_details", nullable = false, columnDefinition = "JSON")
    private String medicationDetails;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}
