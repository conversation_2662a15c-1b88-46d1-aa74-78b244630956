package com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 创建预约DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateAppointmentDTO {
    
    @NotNull(message = "排班ID不能为空")
    private Long scheduleId;
    
    @NotNull(message = "健康档案ID不能为空")
    private Long profileId;
    
    @Size(max = 500, message = "备注不能超过500个字符")
    private String notes;
}
