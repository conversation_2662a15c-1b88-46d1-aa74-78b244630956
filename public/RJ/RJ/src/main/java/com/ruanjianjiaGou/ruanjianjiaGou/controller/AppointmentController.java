package com.ruanjianjiaGou.ruanjianjiaGou.controller;

import com.ruanjianjiaGou.ruanjianjiaGou.common.Result;
import com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment.*;
import com.ruanjianjiaGou.ruanjianjiaGou.service.AppointmentService;
import com.ruanjianjiaGou.ruanjianjiaGou.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 预约挂号控制器
 */
@RestController
@RequestMapping("/api/appointments")
@RequiredArgsConstructor
@Slf4j
public class AppointmentController {
    
    private final AppointmentService appointmentService;
    private final SecurityUtils securityUtils;
    
    /**
     * 获取所有科室
     */
    @GetMapping("/departments")
    public Result<List<DepartmentDTO>> getAllDepartments() {
        List<DepartmentDTO> departments = appointmentService.getAllDepartments();
        return Result.success("获取成功", departments);
    }
    
    /**
     * 搜索医生
     */
    @GetMapping("/doctors")
    public Result<Page<DoctorDTO>> searchDoctors(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Long departmentId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {

        Page<DoctorDTO> doctors = appointmentService.searchDoctors(name, departmentId, page, size);
        return Result.success("获取成功", doctors);
    }
    
    /**
     * 获取科室下的医生列表
     */
    @GetMapping("/departments/{departmentId}/doctors")
    public Result<Page<DoctorDTO>> getDoctorsByDepartment(
            @PathVariable Long departmentId,
            @RequestParam(required = false) String name,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {

        Page<DoctorDTO> doctors = appointmentService.getDoctorsByDepartment(departmentId, page, size, name);
        return Result.success("获取成功", doctors);
    }
    
    /**
     * 获取医生详情
     */
    @GetMapping("/doctors/{doctorId}")
    public Result<DoctorDTO> getDoctorDetail(@PathVariable Long doctorId) {
        DoctorDTO doctor = appointmentService.getDoctorDetail(doctorId);
        return Result.success("获取成功", doctor);
    }
    
    /**
     * 获取医生的可预约排班
     */
    @GetMapping("/doctors/{doctorId}/schedules")
    public Result<List<DoctorScheduleDTO>> getDoctorSchedules(
            @PathVariable Long doctorId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        List<DoctorScheduleDTO> schedules = appointmentService.getDoctorSchedules(doctorId, startDate, endDate);
        return Result.success("获取成功", schedules);
    }

    /**
     * 获取指定日期的可预约排班
     */
    @GetMapping("/schedules/available")
    public Result<List<DoctorScheduleDTO>> getAvailableSchedules(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(required = false) Long departmentId) {

        List<DoctorScheduleDTO> schedules = appointmentService.getAvailableSchedules(date, departmentId);
        return Result.success("获取成功", schedules);
    }
    
    /**
     * 创建预约
     */
    @PostMapping
    public Result<AppointmentDTO> createAppointment(
            @Valid @RequestBody CreateAppointmentDTO createDTO,
            HttpServletRequest request) {

        Long userId = securityUtils.getCurrentUserId(request);
        AppointmentDTO appointment = appointmentService.createAppointment(userId, createDTO);
        return Result.success("预约成功", appointment);
    }
    
    /**
     * 取消预约
     */
    @PostMapping("/{appointmentId}/cancel")
    public Result<Void> cancelAppointment(
            @PathVariable Long appointmentId,
            HttpServletRequest request) {

        Long userId = securityUtils.getCurrentUserId(request);
        appointmentService.cancelAppointment(userId, appointmentId);
        return Result.success("预约已取消", null);
    }
    
    /**
     * 获取用户的预约列表
     */
    @GetMapping("/my")
    public Result<Page<AppointmentDTO>> getUserAppointments(
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            HttpServletRequest request) {

        Long userId = securityUtils.getCurrentUserId(request);
        Page<AppointmentDTO> appointments = appointmentService.getUserAppointments(userId, status, page, size);
        return Result.success("获取成功", appointments);
    }

    /**
     * 获取预约详情
     */
    @GetMapping("/{appointmentId}")
    public Result<AppointmentDTO> getAppointmentDetail(
            @PathVariable Long appointmentId,
            HttpServletRequest request) {

        Long userId = securityUtils.getCurrentUserId(request);
        AppointmentDTO appointment = appointmentService.getAppointmentDetail(userId, appointmentId);
        return Result.success("获取成功", appointment);
    }

    /**
     * 获取即将到来的预约
     */
    @GetMapping("/upcoming")
    public Result<List<AppointmentDTO>> getUpcomingAppointments(HttpServletRequest request) {
        Long userId = securityUtils.getCurrentUserId(request);
        List<AppointmentDTO> appointments = appointmentService.getUpcomingAppointments(userId);
        return Result.success("获取成功", appointments);
    }

    /**
     * 获取科室统计信息
     */
    @GetMapping("/departments/{departmentId}/stats")
    public Result<DepartmentStatsDTO> getDepartmentStats(@PathVariable Long departmentId) {
        DepartmentStatsDTO stats = appointmentService.getDepartmentStats(departmentId);
        return Result.success("获取成功", stats);
    }

    /**
     * 获取医生统计信息
     */
    @GetMapping("/doctors/{doctorId}/stats")
    public Result<DoctorStatsDTO> getDoctorStats(@PathVariable Long doctorId) {
        DoctorStatsDTO stats = appointmentService.getDoctorStats(doctorId);
        return Result.success("获取成功", stats);
    }
}
