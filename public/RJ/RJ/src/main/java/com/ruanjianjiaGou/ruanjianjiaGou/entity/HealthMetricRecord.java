package com.ruanjianjiaGou.ruanjianjiaGou.entity;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "health_metric_records")
public class HealthMetricRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "profile_id", nullable = false)
    private Long profileId;
    
    @Column(name = "metric_type", nullable = false, length = 50)
    private String metricType; // blood_pressure, blood_sugar, weight, heart_rate, temperature, etc.
    
    @Column(name = "metric_value", nullable = false, precision = 10, scale = 2)
    private BigDecimal metricValue;
    
    @Column(name = "systolic_pressure") // 收缩压（血压专用）
    private Integer systolicPressure;
    
    @Column(name = "diastolic_pressure") // 舒张压（血压专用）
    private Integer diastolicPressure;
    
    @Column(length = 20)
    private String unit;
    
    @Column(columnDefinition = "TEXT")
    private String notes;
    
    @Column(name = "recorded_at", nullable = false)
    private LocalDateTime recordedAt;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        if (recordedAt == null) {
            recordedAt = LocalDateTime.now();
        }
    }
}
