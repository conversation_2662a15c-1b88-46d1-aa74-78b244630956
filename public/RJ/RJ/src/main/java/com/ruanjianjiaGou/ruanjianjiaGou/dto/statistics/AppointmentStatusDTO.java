package com.ruanjianjiaGou.ruanjianjiaGou.dto.statistics;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 预约状态分配DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppointmentStatusDTO {
    
    /**
     * 状态分配数据列表
     */
    private List<StatusData> statusData;
    
    /**
     * 状态数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusData {
        
        /**
         * 状态名称
         */
        private String status;
        
        /**
         * 状态描述
         */
        private String statusDescription;
        
        /**
         * 数量
         */
        private Long count;
        
        /**
         * 百分比
         */
        private Double percentage;
    }
}
