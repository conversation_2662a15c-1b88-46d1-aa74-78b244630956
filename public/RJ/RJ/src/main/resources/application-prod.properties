# ==================== ?????? ====================
# MySQL???????
spring.datasource.url=***************************************************************************************************************************************************************
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:123456}

# ?????????
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.leak-detection-threshold=60000

# JPA????
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.connection.characterEncoding=UTF-8
spring.jpa.properties.hibernate.connection.useUnicode=true
spring.jpa.properties.hibernate.jdbc.time_zone=Asia/Shanghai
spring.jpa.open-in-view=false

# MyBatis??
mybatis.mapper-locations=classpath:mappers/*.xml
mybatis.type-aliases-package=com.ruanjianjiaGou.ruanjianjiaGou.entity
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.cache-enabled=true
mybatis.configuration.lazy-loading-enabled=true

# ???????
server.port=${SERVER_PORT:8080}
server.servlet.context-path=/
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# ????????
logging.level.com.ruanjianjiaGou.ruanjianjiaGou=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN
logging.file.name=logs/community-health.log
logging.logback.rollingpolicy.max-file-size=10MB
logging.logback.rollingpolicy.max-history=30

# JWT??
jwt.secret=${JWT_SECRET:mySecretKeyForHealthManagementSystemThatIsLongEnough}
jwt.expiration=${JWT_EXPIRATION:604800000}

# ????
server.ssl.enabled=false
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=when-authorized
