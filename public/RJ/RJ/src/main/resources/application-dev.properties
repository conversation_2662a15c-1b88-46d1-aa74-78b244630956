# ==================== ?????? ====================
# MySQL???????
spring.datasource.url=************************************************************************************************************************************************************************************************
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=123456

# ?????????????
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=2

# JPA????
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.connection.characterEncoding=UTF-8
spring.jpa.properties.hibernate.connection.useUnicode=true

# MyBatis??
mybatis.mapper-locations=classpath:mappers/*.xml
mybatis.type-aliases-package=com.ruanjianjiaGou.ruanjianjiaGou.entity
mybatis.configuration.map-underscore-to-camel-case=true

# ???????
server.port=8080

# ????????
logging.level.com.ruanjianjiaGou.ruanjianjiaGou=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
