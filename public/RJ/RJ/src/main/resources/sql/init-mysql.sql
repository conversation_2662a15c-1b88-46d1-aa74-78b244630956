-- 数据库: community_health_db
-- -----------------------------------------------------
CREATE DATABASE IF NOT EXISTS `community_health_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `community_health_db`;

-- -----------------------------------------------------
-- A. 核心用户与身份模块
-- -----------------------------------------------------

-- 表 1: users (用户表) - 【已按要求修改】
-- 表 1: users (用户表) - 【已按角色方案优化】
CREATE TABLE IF NOT EXISTS `users` (
                                       `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
                                       `phone_number` VARCHAR(20) NOT NULL,
                                       `password` VARCHAR(50) NOT NULL COMMENT '明文密码',
                                       `nickname` VARCHAR(50) NULL,

    -- 核心修改点：添加 role 字段 --
                                       `role` ENUM('RESIDENT', 'DOCTOR', 'ADMIN') NOT NULL DEFAULT 'RESIDENT' COMMENT '用户角色: RESIDENT-居民, DOCTOR-医生, ADMIN-管理员',

                                       `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,

                                       PRIMARY KEY (`id`),
                                       UNIQUE INDEX `uq_phone_number` (`phone_number` ASC)
) ENGINE = InnoDB COMMENT = '基础用户账户表 (包含角色信息)';

-- 表 2: departments (科室表)
CREATE TABLE IF NOT EXISTS `departments` (
                                             `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
                                             `name` VARCHAR(50) NOT NULL,
                                             PRIMARY KEY (`id`),
                                             UNIQUE INDEX `uq_name` (`name` ASC)
) ENGINE = InnoDB COMMENT = '医院科室表';

-- 表 3: doctors (医生信息表)
CREATE TABLE IF NOT EXISTS `doctors` (
                                         `user_id` INT UNSIGNED NOT NULL,
                                         `real_name` VARCHAR(50) NOT NULL,
                                         `gender` ENUM('male', 'female', 'other') NULL COMMENT '性别',
                                         `birth_date` DATE NULL COMMENT '出生日期 (用于计算年龄)',
                                         `id_card_number` VARCHAR(25) NULL COMMENT '身份证号',
                                         `avatar_url` VARCHAR(255) NULL COMMENT '头像链接',
                                         `department_id` INT UNSIGNED NOT NULL,
                                         `title` VARCHAR(50) NULL COMMENT '职称, 如：主治医师',
                                         `specialty` TEXT NULL COMMENT '擅长领域',
                                         `bio` TEXT NULL COMMENT '个人简介',
                                         `status` ENUM('PENDING', 'APPROVED', 'REJECTED') NOT NULL DEFAULT 'PENDING' COMMENT '审核状态',
                                         PRIMARY KEY (`user_id`),
                                         UNIQUE INDEX `uq_id_card_number` (`id_card_number` ASC),
                                         INDEX `fk_doctors_departments_idx` (`department_id` ASC),
                                         CONSTRAINT `fk_doctors_users`
                                             FOREIGN KEY (`user_id`)
                                                 REFERENCES `users` (`id`)
                                                 ON DELETE CASCADE ON UPDATE CASCADE,
                                         CONSTRAINT `fk_doctors_departments`
                                             FOREIGN KEY (`department_id`)
                                                 REFERENCES `departments` (`id`)
                                                 ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '医生专业信息表';


-- -----------------------------------------------------
-- B. 健康管理核心模块
-- -----------------------------------------------------

-- 表 4: health_profiles (健康档案表)
CREATE TABLE IF NOT EXISTS `health_profiles` (
                                                 `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
                                                 `managing_user_id` INT UNSIGNED NOT NULL COMMENT '档案管理人ID',
                                                 `profile_owner_name` VARCHAR(50) NOT NULL COMMENT '档案归属人姓名',
                                                 `medical_history` TEXT NULL COMMENT '既往病史',
                                                 PRIMARY KEY (`id`),
                                                 INDEX `fk_health_profiles_users_idx` (`managing_user_id` ASC),
                                                 CONSTRAINT `fk_health_profiles_users`
                                                     FOREIGN KEY (`managing_user_id`)
                                                         REFERENCES `users` (`id`)
                                                         ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '个人电子健康档案表';

-- 表 5: health_metric_records (健康指标记录表)
CREATE TABLE IF NOT EXISTS `health_metric_records` (
                                                       `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
                                                       `profile_id` INT UNSIGNED NOT NULL,
                                                       `metric_type` VARCHAR(50) NOT NULL COMMENT '指标类型, 如: blood_sugar, blood_pressure',
                                                       `metric_value` DECIMAL(10,2) NOT NULL COMMENT '测量数值',
                                                       `recorded_at` DATETIME NOT NULL COMMENT '测量时间',
                                                       PRIMARY KEY (`id`),
                                                       INDEX `fk_health_records_profiles_idx` (`profile_id` ASC),
                                                       INDEX `idx_type_time` (`metric_type` ASC, `recorded_at` DESC),
                                                       CONSTRAINT `fk_health_records_profiles`
                                                           FOREIGN KEY (`profile_id`)
                                                               REFERENCES `health_profiles` (`id`)
                                                               ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '健康数据自测记录表';

-- 表 6: health_reminders (健康提醒表)
CREATE TABLE IF NOT EXISTS `health_reminders` (
                                                  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
                                                  `user_id` INT UNSIGNED NOT NULL,
                                                  `content` TEXT NOT NULL COMMENT '提醒内容',
                                                  `frequency_rule` VARCHAR(100) NULL COMMENT '频率规则, 如 daily@08:00 或 cron 表达式',
                                                  `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
                                                  PRIMARY KEY (`id`),
                                                  INDEX `fk_health_reminders_users_idx` (`user_id` ASC),
                                                  CONSTRAINT `fk_health_reminders_users`
                                                      FOREIGN KEY (`user_id`)
                                                          REFERENCES `users` (`id`)
                                                          ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '用户自定义健康提醒表';


-- -----------------------------------------------------
-- C. 医疗服务与预约模块
-- -----------------------------------------------------

-- 表 7: doctor_schedules (医生排班表)
CREATE TABLE IF NOT EXISTS `doctor_schedules` (
                                                  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
                                                  `doctor_id` INT UNSIGNED NOT NULL,
                                                  `schedule_date` DATE NOT NULL,
                                                  `start_time` TIME NOT NULL,
                                                  `total_slots` INT UNSIGNED NOT NULL COMMENT '总号源数',
                                                  `booked_slots` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '已预约数',
                                                  PRIMARY KEY (`id`),
                                                  UNIQUE INDEX `uq_doctor_schedule` (`doctor_id` ASC, `schedule_date` ASC, `start_time` ASC),
                                                  CONSTRAINT `fk_doctor_schedules_doctors`
                                                      FOREIGN KEY (`doctor_id`)
                                                          REFERENCES `doctors` (`user_id`)
                                                          ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '医生可预约排班表';

-- 表 8: appointments (预约记录表)
CREATE TABLE IF NOT EXISTS `appointments` (
                                              `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
                                              `user_id` INT UNSIGNED NOT NULL,
                                              `schedule_id` INT UNSIGNED NOT NULL,
                                              `profile_id` INT UNSIGNED NOT NULL COMMENT '就诊人档案ID',
                                              `status` ENUM('booked', 'completed', 'cancelled') NOT NULL DEFAULT 'booked',
                                              `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                              PRIMARY KEY (`id`),
                                              INDEX `fk_appointments_users_idx` (`user_id` ASC),
                                              INDEX `fk_appointments_schedules_idx` (`schedule_id` ASC),
                                              INDEX `fk_appointments_profiles_idx` (`profile_id` ASC),
                                              CONSTRAINT `fk_appointments_users`
                                                  FOREIGN KEY (`user_id`)
                                                      REFERENCES `users` (`id`)
                                                      ON DELETE CASCADE ON UPDATE CASCADE,
                                              CONSTRAINT `fk_appointments_schedules`
                                                  FOREIGN KEY (`schedule_id`)
                                                      REFERENCES `doctor_schedules` (`id`)
                                                      ON DELETE RESTRICT ON UPDATE CASCADE,
                                              CONSTRAINT `fk_appointments_profiles`
                                                  FOREIGN KEY (`profile_id`)
                                                      REFERENCES `health_profiles` (`id`)
                                                      ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '预约挂号记录表';

-- 表 9: online_consultations (在线问诊会话表)
CREATE TABLE IF NOT EXISTS `online_consultations` (
                                                      `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
                                                      `user_id` INT UNSIGNED NOT NULL,
                                                      `doctor_id` INT UNSIGNED NOT NULL,
                                                      `status` ENUM('in_progress', 'completed') NOT NULL DEFAULT 'in_progress',
                                                      `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                      PRIMARY KEY (`id`),
                                                      INDEX `fk_consultations_users_idx` (`user_id` ASC),
                                                      INDEX `fk_consultations_doctors_idx` (`doctor_id` ASC),
                                                      CONSTRAINT `fk_consultations_users`
                                                          FOREIGN KEY (`user_id`)
                                                              REFERENCES `users` (`id`)
                                                              ON DELETE CASCADE ON UPDATE CASCADE,
                                                      CONSTRAINT `fk_consultations_doctors`
                                                          FOREIGN KEY (`doctor_id`)
                                                              REFERENCES `doctors` (`user_id`)
                                                              ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '在线图文问诊会话表';

-- 表 10: messages (即时消息表)
CREATE TABLE IF NOT EXISTS `messages` (
                                          `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
                                          `consultation_id` INT UNSIGNED NOT NULL,
                                          `sender_id` INT UNSIGNED NOT NULL,
                                          `content` TEXT NOT NULL,
                                          `sent_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                          PRIMARY KEY (`id`),
                                          INDEX `fk_messages_consultations_idx` (`consultation_id` ASC),
                                          INDEX `fk_messages_users_idx` (`sender_id` ASC),
                                          CONSTRAINT `fk_messages_consultations`
                                              FOREIGN KEY (`consultation_id`)
                                                  REFERENCES `online_consultations` (`id`)
                                                  ON DELETE CASCADE ON UPDATE CASCADE,
                                          CONSTRAINT `fk_messages_users`
                                              FOREIGN KEY (`sender_id`)
                                                  REFERENCES `users` (`id`)
                                                  ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '问诊聊天消息记录表';

-- 表 11: e_prescriptions (电子处方表)
CREATE TABLE IF NOT EXISTS `e_prescriptions` (
                                                 `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
                                                 `doctor_id` INT UNSIGNED NOT NULL,
                                                 `profile_id` INT UNSIGNED NOT NULL,
                                                 `consultation_id` INT UNSIGNED NULL COMMENT '关联的线上问诊ID',
                                                 `diagnosis` TEXT NOT NULL COMMENT '临床诊断',
                                                 `medication_details` JSON NOT NULL COMMENT '药品详情列表',
                                                 `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                 PRIMARY KEY (`id`),
                                                 INDEX `fk_prescriptions_doctors_idx` (`doctor_id` ASC),
                                                 INDEX `fk_prescriptions_profiles_idx` (`profile_id` ASC),
                                                 INDEX `fk_prescriptions_consultations_idx` (`consultation_id` ASC),
                                                 CONSTRAINT `fk_prescriptions_doctors`
                                                     FOREIGN KEY (`doctor_id`)
                                                         REFERENCES `doctors` (`user_id`)
                                                         ON DELETE RESTRICT ON UPDATE CASCADE,
                                                 CONSTRAINT `fk_prescriptions_profiles`
                                                     FOREIGN KEY (`profile_id`)
                                                         REFERENCES `health_profiles` (`id`)
                                                         ON DELETE CASCADE ON UPDATE CASCADE,
                                                 CONSTRAINT `fk_prescriptions_consultations`
                                                     FOREIGN KEY (`consultation_id`)
                                                         REFERENCES `online_consultations` (`id`)
                                                         ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '电子处方表';


-- -----------------------------------------------------
-- D. 内容与社区互动模块
-- -----------------------------------------------------

-- 表 12: contents (内容表)
CREATE TABLE IF NOT EXISTS `contents` (
                                          `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
                                          `author_id` INT UNSIGNED NOT NULL,
                                          `content_type` ENUM('news', 'activity', 'guidance') NOT NULL COMMENT '内容类型: 新闻, 活动, 健康指导',
                                          `title` VARCHAR(255) NOT NULL,
                                          `body` TEXT NULL,
                                          `published_at` DATETIME NULL,
                                          `activity_time` DATETIME NULL COMMENT '活动时间 (仅对活动类型有效)',
                                          `activity_location` VARCHAR(255) NULL COMMENT '活动地点 (仅对活动类型有效)',
                                          PRIMARY KEY (`id`),
                                          INDEX `fk_contents_users_idx` (`author_id` ASC),
                                          INDEX `idx_content_type` (`content_type` ASC),
                                          CONSTRAINT `fk_contents_users`
                                              FOREIGN KEY (`author_id`)
                                                  REFERENCES `users` (`id`)
                                                  ON DELETE NO ACTION ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '统一内容管理表 (资讯, 活动, 指导)';

-- 表 13: activity_registrations (活动报名表)
CREATE TABLE IF NOT EXISTS `activity_registrations` (
                                                        `activity_id` INT UNSIGNED NOT NULL,
                                                        `user_id` INT UNSIGNED NOT NULL,
                                                        `registered_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                        PRIMARY KEY (`activity_id`, `user_id`),
                                                        INDEX `fk_registrations_users_idx` (`user_id` ASC),
                                                        CONSTRAINT `fk_registrations_activities`
                                                            FOREIGN KEY (`activity_id`)
                                                                REFERENCES `contents` (`id`)
                                                                ON DELETE CASCADE ON UPDATE CASCADE,
                                                        CONSTRAINT `fk_registrations_users`
                                                            FOREIGN KEY (`user_id`)
                                                                REFERENCES `users` (`id`)
                                                                ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '社区活动报名记录表';
-- -----------------------------------------------------
-- 使用目标数据库
-- -----------------------------------------------------
USE `community_health_db`;
SET NAMES utf8mb4;

-- -----------------------------------------------------
-- 为了方便批量插入和清空，暂时禁用外键检查
-- -----------------------------------------------------
SET FOREIGN_KEY_CHECKS=0;

-- -----------------------------------------------------
-- 清空所有相关表，以便重复执行此脚本
-- -----------------------------------------------------
TRUNCATE TABLE `activity_registrations`;
TRUNCATE TABLE `contents`;
TRUNCATE TABLE `e_prescriptions`;
TRUNCATE TABLE `messages`;
TRUNCATE TABLE `online_consultations`;
TRUNCATE TABLE `appointments`;
TRUNCATE TABLE `doctor_schedules`;
TRUNCATE TABLE `health_reminders`;
TRUNCATE TABLE `health_metric_records`;
TRUNCATE TABLE `health_profiles`;
TRUNCATE TABLE `doctors`;
TRUNCATE TABLE `users`;
TRUNCATE TABLE `departments`;

-- -----------------------------------------------------
-- 重新开启外键检查
-- -----------------------------------------------------
SET FOREIGN_KEY_CHECKS=1;

-- -----------------------------------------------------
-- 1. 填充 `departments` (科室表) - 作为基础数据
-- -----------------------------------------------------
INSERT INTO `departments` (`id`, `name`) VALUES
                                             (1, '内科'),
                                             (2, '外科'),
                                             (3, '儿科'),
                                             (4, '皮肤科'),
                                             (5, '中医科');

-- -----------------------------------------------------
-- 2. 填充 `users` (用户表) - 直接指定角色
-- 居民用户ID: 1-5
-- 医生用户ID: 6-9
-- 管理员用户ID: 10
-- -----------------------------------------------------
INSERT INTO `users` (`id`, `phone_number`, `password`, `nickname`, `role`) VALUES
-- 5个居民用户
(1, '13800000001', '123456', '张三', 'RESIDENT'),
(2, '13800000002', '123456', '李四', 'RESIDENT'),
(3, '13800000003', '123456', '王五', 'RESIDENT'),
(4, '13800000004', '123456', '赵六', 'RESIDENT'),
(5, '13800000005', '123456', '孙七', 'RESIDENT'),
(6, '13800000006', '123456', '张六', 'RESIDENT'), -- 新增测试用户

-- 4个医生用户
(7, '18610001001', 'doctor666', '王医生', 'DOCTOR'),
(8, '18610001002', 'doctor666', '刘医生', 'DOCTOR'),
(9, '18610001003', 'doctor666', '陈医生', 'DOCTOR'),
(10, '18610001004', 'doctor666', '林医生', 'DOCTOR'),
(11, '18610001005', '123456', '五医生', 'DOCTOR'), -- 新增测试医生

-- 1个管理员用户
(12, '19999999999', 'admin888', '系统管理员', 'ADMIN');


-- -----------------------------------------------------
-- 3. 填充 `doctors` (医生信息表) - 真正赋予医生身份
-- 这一步是关键！为 role='doctor' 的用户创建专业档案。
-- -----------------------------------------------------
INSERT INTO `doctors` (`user_id`, `real_name`, `department_id`, `title`, `specialty`, `bio`, `status`) VALUES
                                                                                                           (7, '王健康', 1, '主任医师', '心脑血管疾病、内科杂症', '医术精湛，擅长各种内科疑难杂症的诊断与治疗。', 'APPROVED'),
                                                                                                           (8, '刘平安', 2, '副主任医师', '普外科、创伤急救', '拥有丰富的外科手术经验，尤其擅长微创手术。', 'APPROVED'),
                                                                                                           (9, '陈爱婴', 3, '主治医师', '小儿常见病、新生儿护理', '对儿科呼吸系统、消化系统疾病有深入研究，深受家长信赖。', 'APPROVED'),
                                                                                                           (10, '林之善', 4, '医师', '各种皮肤病、医学美容', '致力于将传统疗法与现代皮肤科治疗技术相结合。', 'PENDING'), -- 假设林医生是新入驻，待审核
                                                                                                           (11, '五医生', 5, '主治医师', '中医内科、针灸推拿', '专业的中医医师，擅长中医诊疗和针灸治疗。', 'APPROVED'); -- 新增测试医生


-- -----------------------------------------------------
-- 4. 填充其他关联数据 (可选，但推荐，使系统更真实)
-- -----------------------------------------------------

-- 为居民用户创建健康档案
INSERT INTO `health_profiles` (`id`, `managing_user_id`, `profile_owner_name`, `medical_history`) VALUES
(1, 1, '张三', '高血压病史，对青霉素过敏'),
(2, 2, '李四', '2型糖尿病'),
(3, 3, '王五', '无特殊病史'),
(4, 1, '张小宝', '（张三的孩子）过敏性鼻炎'); -- 张三管理孩子的档案

-- 为医生创建排班
INSERT INTO `doctor_schedules` (`doctor_id`, `schedule_date`, `start_time`, `total_slots`) VALUES
(7, CURDATE() + INTERVAL 1 DAY, '09:00:00', 10),
(7, CURDATE() + INTERVAL 1 DAY, '14:00:00', 10),
(8, CURDATE() + INTERVAL 2 DAY, '09:00:00', 8),
(9, CURDATE() + INTERVAL 2 DAY, '10:00:00', 12),
(11, CURDATE() + INTERVAL 3 DAY, '09:00:00', 15); -- 新增测试医生排班

-- 创建一些内容
INSERT INTO `contents` (`author_id`, `content_type`, `title`, `body`, `published_at`) VALUES
                                                                                          (12, 'news', '关于系统升级维护的通知', '本周末晚22:00将进行系统升级，届时服务可能中断。', NOW()),
                                                                                          (7, 'guidance', '高血压患者如何安全度夏？', '夏季天气炎热，高血压患者尤其需要注意...', NOW() - INTERVAL 1 DAY);


-- -----------------------------------------------------
-- 数据填充完成
-- -----------------------------------------------------
SELECT '所有数据已按角色逻辑成功填充！' AS 'Status';