# ==================== ????? ====================
server.port=8080
server.servlet.context-path=/

# ==================== ????? ====================
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.url=*********************************************************************************************************************************************************************
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# ==================== JPA/Hibernate?? ====================
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.hbm2ddl.auto=update

# ==================== ???? ====================
spring.jpa.properties.hibernate.current_session_context_class=thread
spring.transaction.default-timeout=30

# ==================== MyBatis?? ====================
mybatis.mapper-locations=classpath:mappers/*.xml
mybatis.type-aliases-package=com.ruanjianjiaGou.ruanjianjiaGou.entity
mybatis.configuration.map-underscore-to-camel-case=true

# ==================== ???? ====================
logging.level.com.ruanjianjiaGou.ruanjianjiaGou=DEBUG
logging.level.org.springframework.transaction=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# ==================== JWT?? ====================
jwt.secret=mySecretKeyForHealthManagementSystemThatIsLongEnough
jwt.expiration=604800000
