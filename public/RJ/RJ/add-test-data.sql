-- 为医生统计分析添加更多测试数据

USE community_health_db;

-- 1. 添加一些在线问诊数据（医生ID=6对应用户ID=6）
INSERT INTO online_consultations (user_id, doctor_id, status, created_at) VALUES
(1, 6, 'completed', '2025-06-10 09:00:00'),
(2, 6, 'completed', '2025-06-11 14:30:00'),
(3, 6, 'completed', '2025-06-12 16:00:00'),
(4, 6, 'completed', '2025-06-13 10:30:00'),
(1, 6, 'in_progress', '2025-06-14 10:00:00'),
(5, 6, 'completed', '2025-06-14 15:00:00');

-- 2. 添加一些健康指导内容（作者ID=6）
INSERT INTO contents (author_id, content_type, title, body, published_at) VALUES
(6, 'guidance', '夏季高血压患者注意事项', '夏季天气炎热，高血压患者需要特别注意以下几点：1.避免长时间暴露在高温环境中...', '2025-06-10 08:00:00'),
(6, 'guidance', '糖尿病患者的饮食建议', '糖尿病患者在日常饮食中应该注意控制血糖，建议：1.少食多餐...', '2025-06-12 09:00:00'),
(6, 'guidance', '心血管疾病预防指南', '心血管疾病的预防非常重要，建议患者：1.定期体检...', '2025-06-13 14:00:00'),
(6, 'news', '医院最新通知', '关于门诊时间调整的通知...', '2025-06-13 14:00:00');

-- 3. 更新一些预约状态为已完成
UPDATE appointments SET status = 'completed', updated_at = NOW() WHERE id IN (1, 2);
UPDATE appointments SET status = 'cancelled', updated_at = NOW() WHERE id = 3;

-- 4. 查看更新后的数据统计
SELECT '=== 医生6的预约统计 ===' as info;
SELECT 
    s.doctor_id,
    a.status,
    COUNT(*) as count
FROM appointments a 
JOIN doctor_schedules s ON a.schedule_id = s.id 
WHERE s.doctor_id = 6 
GROUP BY s.doctor_id, a.status;

SELECT '=== 医生6的在线问诊统计 ===' as info;
SELECT 
    doctor_id,
    status,
    COUNT(*) as count
FROM online_consultations 
WHERE doctor_id = 6 
GROUP BY doctor_id, status;

SELECT '=== 医生6的内容统计 ===' as info;
SELECT 
    author_id,
    content_type,
    COUNT(*) as count
FROM contents 
WHERE author_id = 6 
GROUP BY author_id, content_type;

SELECT '=== 医生6的时间段预约统计 ===' as info;
SELECT 
    HOUR(s.start_time) as hour,
    COUNT(a.id) as appointment_count
FROM appointments a 
JOIN doctor_schedules s ON a.schedule_id = s.id 
WHERE s.doctor_id = 6 
GROUP BY HOUR(s.start_time) 
ORDER BY HOUR(s.start_time);
