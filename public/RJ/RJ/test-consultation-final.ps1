# 在线问诊功能完整测试脚本
$baseUrl = "http://localhost:8080"

Write-Host "=== 在线问诊功能测试 ===" -ForegroundColor Cyan

# 1. 测试居民登录
Write-Host "`n1. 测试居民登录..." -ForegroundColor Yellow
$residentBody = '{"phoneNumber":"13800000001","password":"123456"}'
try {
    $residentResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $residentBody -ContentType 'application/json'
    if ($residentResponse.success) {
        $residentToken = $residentResponse.data.token
        Write-Host "✅ 居民登录成功" -ForegroundColor Green
        Write-Host "   Token: $($residentToken.Substring(0,20))..." -ForegroundColor Cyan
    } else {
        Write-Host "❌ 居民登录失败" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ 居民登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 测试医生登录
Write-Host "`n2. 测试医生登录..." -ForegroundColor Yellow
$doctorBody = '{"phoneNumber":"18610001001","password":"doctor666"}'
try {
    $doctorResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method POST -Body $doctorBody -ContentType 'application/json'
    if ($doctorResponse.success) {
        $doctorToken = $doctorResponse.data.token
        Write-Host "✅ 医生登录成功" -ForegroundColor Green
        Write-Host "   Token: $($doctorToken.Substring(0,20))..." -ForegroundColor Cyan
    } else {
        Write-Host "❌ 医生登录失败" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ 医生登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 3. 创建问诊会话
Write-Host "`n3. 创建问诊会话..." -ForegroundColor Yellow
$consultationBody = '{"doctorId":7,"initialMessage":"医生您好，我最近感觉头痛，想咨询一下可能的原因和治疗建议。"}'
$headers = @{
    'Content-Type' = 'application/json'
    'Authorization' = "Bearer $residentToken"
}

try {
    $consultationResponse = Invoke-RestMethod -Uri "$baseUrl/api/consultations" -Method POST -Body $consultationBody -Headers $headers
    if ($consultationResponse.success) {
        $consultationId = $consultationResponse.data.id
        Write-Host "✅ 问诊会话创建成功" -ForegroundColor Green
        Write-Host "   会话ID: $consultationId" -ForegroundColor Cyan
        Write-Host "   医生: $($consultationResponse.data.doctorName)" -ForegroundColor Cyan
        Write-Host "   状态: $($consultationResponse.data.statusDescription)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ 创建问诊会话失败: $($consultationResponse.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ 创建问诊会话请求失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 4. 居民发送消息
Write-Host "`n4. 居民发送消息..." -ForegroundColor Yellow
$messageBody = '{"content":"补充一下，头痛主要是在太阳穴附近，持续了大概3天了，有时候还会感到恶心。"}'
try {
    $messageResponse = Invoke-RestMethod -Uri "$baseUrl/api/consultations/$consultationId/messages" -Method POST -Body $messageBody -Headers $headers
    if ($messageResponse.success) {
        Write-Host "✅ 居民消息发送成功" -ForegroundColor Green
        Write-Host "   消息ID: $($messageResponse.data.id)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ 居民发送消息失败: $($messageResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 居民发送消息请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 医生发送回复
Write-Host "`n5. 医生发送回复..." -ForegroundColor Yellow
$doctorMessageBody = '{"content":"根据您描述的症状，太阳穴疼痛伴随恶心可能是偏头痛的表现。建议您：1. 保证充足睡眠；2. 避免强光刺激；3. 可以适当按摩太阳穴。如果症状持续或加重，建议到医院进一步检查。"}'
$doctorHeaders = @{
    'Content-Type' = 'application/json'
    'Authorization' = "Bearer $doctorToken"
}

try {
    $doctorMessageResponse = Invoke-RestMethod -Uri "$baseUrl/api/consultations/$consultationId/messages" -Method POST -Body $doctorMessageBody -Headers $doctorHeaders
    if ($doctorMessageResponse.success) {
        Write-Host "✅ 医生回复发送成功" -ForegroundColor Green
        Write-Host "   消息ID: $($doctorMessageResponse.data.id)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ 医生发送回复失败: $($doctorMessageResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 医生发送回复请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 获取消息历史
Write-Host "`n6. 获取消息历史..." -ForegroundColor Yellow
try {
    $messagesResponse = Invoke-RestMethod -Uri "$baseUrl/api/consultations/$consultationId/messages?page=1&size=10" -Method GET -Headers $headers
    if ($messagesResponse.success) {
        Write-Host "✅ 获取消息历史成功" -ForegroundColor Green
        Write-Host "   总消息数: $($messagesResponse.data.totalElements)" -ForegroundColor Cyan
        
        foreach ($message in $messagesResponse.data.content) {
            $roleColor = if ($message.senderRole -eq "RESIDENT") { "Blue" } else { "Magenta" }
            Write-Host "   [$($message.senderRole)] $($message.senderNickname): $($message.content)" -ForegroundColor $roleColor
        }
    } else {
        Write-Host "❌ 获取消息历史失败: $($messagesResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 获取消息历史请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. 获取问诊列表
Write-Host "`n7. 获取问诊列表..." -ForegroundColor Yellow
try {
    $consultationsResponse = Invoke-RestMethod -Uri "$baseUrl/api/consultations?page=1&size=5" -Method GET -Headers $headers
    if ($consultationsResponse.success) {
        Write-Host "✅ 获取问诊列表成功" -ForegroundColor Green
        Write-Host "   总问诊数: $($consultationsResponse.data.totalElements)" -ForegroundColor Cyan
        
        foreach ($consultation in $consultationsResponse.data.content) {
            Write-Host "   问诊ID: $($consultation.id) | 医生: $($consultation.doctorName) | 状态: $($consultation.statusDescription)" -ForegroundColor Cyan
        }
    } else {
        Write-Host "❌ 获取问诊列表失败: $($consultationsResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 获取问诊列表请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 8. 医生完成问诊
Write-Host "`n8. 医生完成问诊..." -ForegroundColor Yellow
try {
    $completeResponse = Invoke-RestMethod -Uri "$baseUrl/api/consultations/$consultationId/complete" -Method PUT -Headers $doctorHeaders
    if ($completeResponse.success) {
        Write-Host "✅ 问诊完成成功" -ForegroundColor Green
    } else {
        Write-Host "❌ 完成问诊失败: $($completeResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 完成问诊请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Cyan
Write-Host "✅ 在线问诊功能基本测试通过" -ForegroundColor Green

Write-Host "`n📋 测试总结:" -ForegroundColor White
Write-Host "✅ 1. 创建问诊会话 - POST /api/consultations" -ForegroundColor Green
Write-Host "✅ 2. 获取问诊列表 - GET /api/consultations" -ForegroundColor Green
Write-Host "✅ 3. 发送消息 - POST /api/consultations/{id}/messages" -ForegroundColor Green
Write-Host "✅ 4. 获取消息历史 - GET /api/consultations/{id}/messages" -ForegroundColor Green
Write-Host "✅ 5. 完成问诊 - PUT /api/consultations/{id}/complete" -ForegroundColor Green

Write-Host "`n🔍 数据库验证建议:" -ForegroundColor Yellow
Write-Host "请检查以下数据表中的数据是否正确保存：" -ForegroundColor Yellow
Write-Host "- online_consultations 表：问诊会话记录" -ForegroundColor Gray
Write-Host "- messages 表：消息记录" -ForegroundColor Gray
Write-Host "- 验证外键关联和时间戳是否正确" -ForegroundColor Gray
