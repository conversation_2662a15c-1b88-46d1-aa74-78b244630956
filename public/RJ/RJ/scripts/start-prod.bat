@echo off
echo ==================== 社区健康管理系统 - 生产环境启动 ====================

echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请确保已安装Java 8或更高版本
    pause
    exit /b 1
)

echo 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo 错误：未找到Maven环境，请确保已安装Maven
    pause
    exit /b 1
)

echo 清理并打包项目...
mvn clean package -DskipTests

echo 启动生产环境...
java -jar -Dspring.profiles.active=prod target/RuanJianJiaGou-0.0.1-SNAPSHOT.jar

pause
