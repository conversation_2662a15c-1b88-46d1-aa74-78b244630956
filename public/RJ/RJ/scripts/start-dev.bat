@echo off
echo ==================== 社区健康管理系统 - 开发环境启动 ====================

echo 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请确保已安装Java 8或更高版本
    pause
    exit /b 1
)

echo 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo 错误：未找到Maven环境，请确保已安装Maven
    pause
    exit /b 1
)

echo 清理并编译项目...
mvn clean compile

echo 启动开发环境...
mvn spring-boot:run -Dspring-boot.run.profiles=dev

pause
