# 简单的医生统计分析接口测试

$baseUrl = "http://localhost:8080"

# 1. 医生登录
Write-Host "=== 医生登录 ===" -ForegroundColor Green
$loginBody = '{"phoneNumber":"18610001001","password":"doctor666"}'

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/user/login" -Method Post -ContentType "application/json" -Body $loginBody
    $token = $loginResponse.data.token
    Write-Host "登录成功" -ForegroundColor Green
    
    # 2. 测试KPI接口
    Write-Host "`n=== 测试KPI接口 ===" -ForegroundColor Green
    $kpiResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/statistics/kpi?range=month" -Method Get -Headers @{Authorization="Bearer $token"}
    Write-Host "KPI接口调用成功:" -ForegroundColor Green
    $kpiResponse | ConvertTo-Json -Depth 3
    
    # 3. 测试服务量趋势接口
    Write-Host "`n=== 测试服务量趋势接口 ===" -ForegroundColor Green
    $trendResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/statistics/service-trend?range=month" -Method Get -Headers @{Authorization="Bearer $token"}
    Write-Host "服务量趋势接口调用成功:" -ForegroundColor Green
    $trendResponse | ConvertTo-Json -Depth 3
    
    # 4. 测试预约状态分配接口
    Write-Host "`n=== 测试预约状态分配接口 ===" -ForegroundColor Green
    $statusResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/statistics/appointment-status?range=month" -Method Get -Headers @{Authorization="Bearer $token"}
    Write-Host "预约状态分配接口调用成功:" -ForegroundColor Green
    $statusResponse | ConvertTo-Json -Depth 3

    # 5. 测试高频服务患者排行接口
    Write-Host "`n=== 测试高频服务患者排行接口 ===" -ForegroundColor Green
    $patientsResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/statistics/top-patients?range=month" -Method Get -Headers @{Authorization="Bearer $token"}
    Write-Host "高频服务患者排行接口调用成功:" -ForegroundColor Green
    $patientsResponse | ConvertTo-Json -Depth 3

    # 6. 测试预约时间段分析接口
    Write-Host "`n=== 测试预约时间段分析接口 ===" -ForegroundColor Green
    $hotnessResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/statistics/schedule-hotness?range=month" -Method Get -Headers @{Authorization="Bearer $token"}
    Write-Host "预约时间段分析接口调用成功:" -ForegroundColor Green
    $hotnessResponse | ConvertTo-Json -Depth 3
    
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Yellow
