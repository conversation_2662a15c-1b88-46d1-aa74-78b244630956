# 医生统计分析接口测试脚本

$baseUrl = "http://localhost:8080"

# 1. 医生登录获取Token
Write-Host "=== 1. 医生登录 ===" -ForegroundColor Green
$loginData = @{
    phoneNumber = "18610001001"
    password = "doctor666"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/login" -Method Post -ContentType "application/json" -Body $loginData
    $token = $loginResponse.data.token
    Write-Host "登录成功，Token: $($token.Substring(0, 20))..." -ForegroundColor Green
} catch {
    Write-Host "登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 设置请求头
$headers = @{
    Authorization = "Bearer $token"
    "Content-Type" = "application/json"
}

# 2. 测试核心指标KPI接口
Write-Host "`n=== 2. 测试核心指标KPI接口 ===" -ForegroundColor Green
try {
    $kpiResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/statistics/kpi?range=month" -Method Get -Headers $headers
    Write-Host "KPI接口调用成功:" -ForegroundColor Green
    Write-Host ($kpiResponse | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "KPI接口调用失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 测试服务量趋势接口
Write-Host "`n=== 3. 测试服务量趋势接口 ===" -ForegroundColor Green
try {
    $trendResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/statistics/service-trend?range=month" -Method Get -Headers $headers
    Write-Host "服务量趋势接口调用成功:" -ForegroundColor Green
    Write-Host ($trendResponse | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "服务量趋势接口调用失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 测试预约状态分配接口
Write-Host "`n=== 4. 测试预约状态分配接口 ===" -ForegroundColor Green
try {
    $statusResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/statistics/appointment-status?range=month" -Method Get -Headers $headers
    Write-Host "预约状态分配接口调用成功:" -ForegroundColor Green
    Write-Host ($statusResponse | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "预约状态分配接口调用失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 测试高频服务患者排行接口
Write-Host "`n=== 5. 测试高频服务患者排行接口 ===" -ForegroundColor Green
try {
    $patientsResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/statistics/top-patients?range=month" -Method Get -Headers $headers
    Write-Host "高频服务患者排行接口调用成功:" -ForegroundColor Green
    Write-Host ($patientsResponse | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "高频服务患者排行接口调用失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 测试预约时间段分析接口
Write-Host "`n=== 6. 测试预约时间段分析接口 ===" -ForegroundColor Green
try {
    $hotnessResponse = Invoke-RestMethod -Uri "$baseUrl/api/doctor/statistics/schedule-hotness?range=month" -Method Get -Headers $headers
    Write-Host "预约时间段分析接口调用成功:" -ForegroundColor Green
    Write-Host ($hotnessResponse | ConvertTo-Json -Depth 3)
} catch {
    Write-Host "预约时间段分析接口调用失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Yellow
