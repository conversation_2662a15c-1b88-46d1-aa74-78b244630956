# Community Health Management System - API Test Script

$baseUrl = "http://localhost:8888/api/user"

Write-Host "============================================" -ForegroundColor Green
Write-Host "Community Health Management System - API Test" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green

# Test Login Function
function Test-Login {
    param(
        [string]$phoneNumber,
        [string]$password,
        [string]$roleName
    )
    
    Write-Host "`nTesting $roleName login: $phoneNumber" -ForegroundColor Yellow
    
    $loginData = @{
        phoneNumber = $phoneNumber
        password = $password
    } | ConvertTo-Json
    
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/login" -Method POST -ContentType "application/json" -Body $loginData
        
        Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Cyan
        
        if ($response.StatusCode -eq 200) {
            Write-Host "Login Success!" -ForegroundColor Green
            $result = $response.Content | ConvertFrom-Json
            Write-Host "User: $($result.data.userInfo.nickname)" -ForegroundColor White
            
            if ($result.data -and $result.data.token) {
                return $result.data.token
            }
        } else {
            Write-Host "Login Failed!" -ForegroundColor Red
            Write-Host "Error: $($response.Content)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "Request Error: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    return $null
}

# Test Register Function
function Test-Register {
    param(
        [string]$phoneNumber,
        [string]$password,
        [string]$nickname,
        [string]$role
    )
    
    Write-Host "`nTesting register: $phoneNumber ($nickname)" -ForegroundColor Yellow
    
    $registerData = @{
        phoneNumber = $phoneNumber
        password = $password
        nickname = $nickname
        role = $role
    } | ConvertTo-Json
    
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/register" -Method POST -ContentType "application/json" -Body $registerData
        
        Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Cyan
        
        if ($response.StatusCode -eq 200) {
            Write-Host "Register Success!" -ForegroundColor Green
            $result = $response.Content | ConvertFrom-Json
            Write-Host "Result: $($result.message)" -ForegroundColor White
            return $true
        } else {
            Write-Host "Register Failed!" -ForegroundColor Red
            Write-Host "Error: $($response.Content)" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "Request Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Test Get Profile Function
function Test-GetProfile {
    param(
        [string]$token,
        [string]$roleName
    )
    
    Write-Host "`nTesting get $roleName profile" -ForegroundColor Yellow
    
    try {
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        $response = Invoke-WebRequest -Uri "$baseUrl/profile" -Method GET -Headers $headers
        
        Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Cyan
        
        if ($response.StatusCode -eq 200) {
            Write-Host "Get Profile Success!" -ForegroundColor Green
            $result = $response.Content | ConvertFrom-Json
            Write-Host "Profile: $($result.data.nickname) - $($result.data.phoneNumber)" -ForegroundColor White
        } else {
            Write-Host "Get Profile Failed!" -ForegroundColor Red
            Write-Host "Error: $($response.Content)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "Request Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Main Test Process
Write-Host "`nPart 1: Test Existing User Login" -ForegroundColor Magenta
Write-Host "========================================" -ForegroundColor Magenta

$testUsers = @(
    @{ phone = "***********"; password = "123456"; role = "Resident" },
    @{ phone = "***********"; password = "doctor666"; role = "Doctor" },
    @{ phone = "***********"; password = "admin888"; role = "Admin" }
)

$tokens = @{}

foreach ($user in $testUsers) {
    $token = Test-Login -phoneNumber $user.phone -password $user.password -roleName $user.role
    if ($token) {
        $tokens[$user.phone] = $token
    }
}

Write-Host "`nPart 2: Test Get User Profile" -ForegroundColor Magenta
Write-Host "========================================" -ForegroundColor Magenta

foreach ($phoneToken in $tokens.GetEnumerator()) {
    $phone = $phoneToken.Key
    $token = $phoneToken.Value
    
    $roleName = if ($phone.StartsWith("138")) { "Resident" } 
                elseif ($phone.StartsWith("186")) { "Doctor" } 
                else { "Admin" }
    
    Test-GetProfile -token $token -roleName $roleName
}

Write-Host "`nPart 3: Test New User Registration" -ForegroundColor Magenta
Write-Host "========================================" -ForegroundColor Magenta

$newUsers = @(
    @{ phone = "13800000011"; password = "123456"; nickname = "Test Resident"; role = "RESIDENT" },
    @{ phone = "13900000002"; password = "123456"; nickname = "Test Doctor"; role = "DOCTOR" }
)

foreach ($newUser in $newUsers) {
    $success = Test-Register -phoneNumber $newUser.phone -password $newUser.password -nickname $newUser.nickname -role $newUser.role
    
    if ($success) {
        $roleName = if ($newUser.role -eq "RESIDENT") { "Resident" } else { "Doctor" }
        $token = Test-Login -phoneNumber $newUser.phone -password $newUser.password -roleName $roleName
        if ($token) {
            Test-GetProfile -token $token -roleName $roleName
        }
    }
}

Write-Host "`n============================================" -ForegroundColor Green
Write-Host "Test Complete" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green
Write-Host "API Summary:" -ForegroundColor Cyan
Write-Host "1. Login API: POST $baseUrl/login" -ForegroundColor White
Write-Host "2. Register API: POST $baseUrl/register" -ForegroundColor White
Write-Host "3. Profile API: GET $baseUrl/profile" -ForegroundColor White
Write-Host ""
Write-Host "Test Accounts:" -ForegroundColor Yellow
Write-Host "Resident: ***********/123456" -ForegroundColor White
Write-Host "Doctor: ***********/doctor666" -ForegroundColor White
Write-Host "Admin: ***********/admin888" -ForegroundColor White
