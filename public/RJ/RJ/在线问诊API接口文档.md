# 在线问诊API接口文档

## 📋 接口概述

本文档描述了社区健康管理系统阶段4在线问诊功能的4个核心API接口。

### 🎯 功能特性

- ✅ **居民发起问诊**: 居民可以向指定医生发起图文问诊
- ✅ **双向消息通信**: 居民和医生可以在问诊会话中发送消息
- ✅ **消息历史记录**: 完整保存问诊会话的消息历史
- ✅ **问诊列表管理**: 按角色显示相关的问诊会话列表
- ✅ **问诊状态管理**: 医生可以完成问诊会话
- ✅ **权限控制**: 严格的角色权限验证

---

## 🔐 认证说明

所有API接口都需要JWT Token认证，在请求头中添加：
```
Authorization: Bearer <token>
```

---

## 📝 API接口详情

### 1. 创建问诊会话

**功能**: 居民向指定医生发起一次新的图文问诊  
**权限**: 居民 (Resident)

```http
POST /api/consultations
Content-Type: application/json
Authorization: Bearer <resident_token>

{
  "doctorId": 7,
  "initialMessage": "医生您好，我最近感觉头痛，想咨询一下可能的原因和治疗建议。"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "问诊会话创建成功",
  "data": {
    "id": 1,
    "userId": 1,
    "userNickname": "张三",
    "userPhoneNumber": "13800000001",
    "doctorId": 7,
    "doctorName": "王健康",
    "departmentName": "内科",
    "status": "IN_PROGRESS",
    "statusDescription": "进行中",
    "createdAt": "2025-06-15T14:30:00",
    "lastMessage": "医生您好，我最近感觉头痛，想咨询一下可能的原因和治疗建议。",
    "lastMessageTime": "2025-06-15T14:30:00",
    "messageCount": 1
  }
}
```

---

### 2. 获取问诊列表

**功能**: 获取当前用户（居民或医生）的所有问诊会话列表  
**权限**: 居民 (Resident) 或 医生 (Doctor)

```http
GET /api/consultations?page=1&size=10
Authorization: Bearer <token>
```

**参数说明**:
- `page`: 页码，从1开始，默认1
- `size`: 每页大小，默认10

**响应示例**:
```json
{
  "code": 200,
  "message": "获取问诊列表成功",
  "data": {
    "content": [
      {
        "id": 1,
        "userId": 1,
        "userNickname": "张三",
        "userPhoneNumber": "13800000001",
        "doctorId": 7,
        "doctorName": "王健康",
        "departmentName": "内科",
        "status": "IN_PROGRESS",
        "statusDescription": "进行中",
        "createdAt": "2025-06-15T14:30:00",
        "lastMessage": "根据您描述的症状...",
        "lastMessageTime": "2025-06-15T14:35:00",
        "messageCount": 3
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 0
  }
}
```

---

### 3. 发送消息

**功能**: 在指定的问诊会话中发送一条新消息  
**权限**: 参与该会话的居民或医生

```http
POST /api/consultations/{consultationId}/messages
Content-Type: application/json
Authorization: Bearer <token>

{
  "content": "补充一下，头痛主要是在太阳穴附近，持续了大概3天了，有时候还会感到恶心。"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "消息发送成功",
  "data": {
    "id": 2,
    "consultationId": 1,
    "senderId": 1,
    "senderNickname": "张三",
    "senderRole": "RESIDENT",
    "content": "补充一下，头痛主要是在太阳穴附近，持续了大概3天了，有时候还会感到恶心。",
    "sentAt": "2025-06-15T14:32:00"
  }
}
```

---

### 4. 获取消息历史记录

**功能**: 获取指定问诊会话的历史消息  
**权限**: 参与该会话的居民或医生

```http
GET /api/consultations/{consultationId}/messages?page=1&size=20
Authorization: Bearer <token>
```

**参数说明**:
- `page`: 页码，从1开始，默认1
- `size`: 每页大小，默认20

**响应示例**:
```json
{
  "code": 200,
  "message": "获取消息历史成功",
  "data": {
    "content": [
      {
        "id": 1,
        "consultationId": 1,
        "senderId": 1,
        "senderNickname": "张三",
        "senderRole": "RESIDENT",
        "content": "医生您好，我最近感觉头痛，想咨询一下可能的原因和治疗建议。",
        "sentAt": "2025-06-15T14:30:00"
      },
      {
        "id": 2,
        "consultationId": 1,
        "senderId": 1,
        "senderNickname": "张三",
        "senderRole": "RESIDENT",
        "content": "补充一下，头痛主要是在太阳穴附近，持续了大概3天了，有时候还会感到恶心。",
        "sentAt": "2025-06-15T14:32:00"
      },
      {
        "id": 3,
        "consultationId": 1,
        "senderId": 7,
        "senderNickname": "王健康",
        "senderRole": "DOCTOR",
        "content": "根据您描述的症状，太阳穴疼痛伴随恶心可能是偏头痛的表现。建议您：1. 保证充足睡眠；2. 避免强光刺激；3. 可以适当按摩太阳穴。如果症状持续或加重，建议到医院进一步检查。",
        "sentAt": "2025-06-15T14:35:00"
      }
    ],
    "totalElements": 3,
    "totalPages": 1,
    "size": 20,
    "number": 0
  }
}
```

---

### 5. 完成问诊（额外功能）

**功能**: 医生标记问诊会话为已完成  
**权限**: 负责该问诊的医生

```http
PUT /api/consultations/{consultationId}/complete
Authorization: Bearer <doctor_token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "问诊已完成",
  "data": "问诊已完成"
}
```

---

## 🔍 错误处理

### 常见错误码

- `401`: 未授权 - Token无效或过期
- `403`: 权限不足 - 角色权限不匹配
- `404`: 资源不存在 - 问诊会话或医生不存在
- `500`: 服务器内部错误

### 错误响应格式

```json
{
  "code": 500,
  "message": "创建问诊会话失败: 该医生尚未通过审核，无法接受问诊",
  "data": null
}
```

---

## 📊 数据库表结构

### online_consultations (问诊会话表)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | INT | 主键 |
| user_id | INT | 居民用户ID |
| doctor_id | INT | 医生用户ID |
| status | ENUM | 状态：IN_PROGRESS, COMPLETED |
| created_at | DATETIME | 创建时间 |

### messages (消息表)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | BIGINT | 主键 |
| consultation_id | INT | 问诊会话ID |
| sender_id | INT | 发送者用户ID |
| content | TEXT | 消息内容 |
| sent_at | DATETIME | 发送时间 |

---

## 🎯 测试建议

1. **功能测试**: 验证所有API接口的基本功能
2. **权限测试**: 验证不同角色的访问权限
3. **数据一致性**: 检查数据库中的数据完整性
4. **并发测试**: 测试多用户同时使用的情况
5. **边界测试**: 测试异常输入和边界条件

---

## 📝 使用流程示例

1. **居民登录** → 获取Token
2. **医生登录** → 获取Token  
3. **居民创建问诊** → 向指定医生发起问诊
4. **居民发送消息** → 描述症状
5. **医生查看问诊列表** → 看到新的问诊请求
6. **医生发送回复** → 提供医疗建议
7. **双方查看消息历史** → 回顾对话内容
8. **医生完成问诊** → 标记问诊结束

---

## ✅ 实现状态

- ✅ 创建问诊会话 - POST /api/consultations
- ✅ 获取问诊列表 - GET /api/consultations  
- ✅ 发送消息 - POST /api/consultations/{id}/messages
- ✅ 获取消息历史 - GET /api/consultations/{id}/messages
- ✅ 完成问诊 - PUT /api/consultations/{id}/complete

**所有接口已完成开发并可正常使用！**
