# Test Create Consultation API

Write-Host "=== Test Create Consultation ===" -ForegroundColor Cyan

# 1. Resident Login
Write-Host "1. Resident Login..." -ForegroundColor Yellow
$residentResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/user/login" -Method POST -ContentType "application/json" -Body (Get-Content login-resident.json -Raw)
$residentToken = $residentResponse.data.token
Write-Host "Resident login success" -ForegroundColor Green

# 2. Create Consultation
Write-Host "2. Create Consultation..." -ForegroundColor Yellow
$headers = @{
    'Authorization' = "Bearer $residentToken"
    'Content-Type' = 'application/json'
}

try {
    $consultationResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/consultations" -Method POST -Headers $headers -Body (Get-Content create-consultation.json -Raw)
    Write-Host "Consultation Response:" -ForegroundColor Green
    $consultationResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Error creating consultation:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}
