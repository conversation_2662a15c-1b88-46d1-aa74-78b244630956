-- =====================================================
-- 在线问诊枚举值修复脚本
-- 修复数据库枚举值与后端代码不匹配的问题
-- =====================================================

USE `community_health_db`;

-- 1. 首先查看当前的枚举值定义
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'community_health_db' 
  AND TABLE_NAME = 'online_consultations' 
  AND COLUMN_NAME = 'status';

-- 2. 查看当前数据中的状态值
SELECT DISTINCT status FROM online_consultations;

-- 3. 修复方案：更新枚举值定义
-- 将小写下划线格式改为大写下划线格式，与Java后端保持一致

-- 步骤1: 添加新的枚举值（临时兼容）
ALTER TABLE `online_consultations` 
MODIFY COLUMN `status` ENUM('in_progress', 'completed', 'IN_PROGRESS', 'COMPLETED') 
NOT NULL DEFAULT 'IN_PROGRESS';

-- 步骤2: 更新现有数据
UPDATE `online_consultations` 
SET `status` = 'IN_PROGRESS' 
WHERE `status` = 'in_progress';

UPDATE `online_consultations` 
SET `status` = 'COMPLETED' 
WHERE `status` = 'completed';

-- 步骤3: 移除旧的枚举值，只保留大写格式
ALTER TABLE `online_consultations` 
MODIFY COLUMN `status` ENUM('IN_PROGRESS', 'COMPLETED') 
NOT NULL DEFAULT 'IN_PROGRESS';

-- 4. 验证修复结果
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'community_health_db' 
  AND TABLE_NAME = 'online_consultations' 
  AND COLUMN_NAME = 'status';

-- 5. 查看修复后的数据
SELECT id, user_id, doctor_id, status, created_at 
FROM online_consultations 
ORDER BY created_at DESC;

-- 6. 同时检查预约表的状态枚举值（如果存在类似问题）
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'community_health_db' 
  AND TABLE_NAME = 'appointments' 
  AND COLUMN_NAME = 'status';

-- 7. 如果预约表也有问题，执行类似修复
-- 注意：根据测试报告，预约表可能也需要修复
-- 但需要先确认当前的枚举值定义

SELECT DISTINCT status FROM appointments;

-- 如果预约表状态是小写，也需要修复：
-- ALTER TABLE `appointments` 
-- MODIFY COLUMN `status` ENUM('booked', 'completed', 'cancelled', 'BOOKED', 'COMPLETED', 'CANCELLED') 
-- NOT NULL DEFAULT 'BOOKED';

-- UPDATE `appointments` SET `status` = 'BOOKED' WHERE `status` = 'booked';
-- UPDATE `appointments` SET `status` = 'COMPLETED' WHERE `status` = 'completed';  
-- UPDATE `appointments` SET `status` = 'CANCELLED' WHERE `status` = 'cancelled';

-- ALTER TABLE `appointments` 
-- MODIFY COLUMN `status` ENUM('BOOKED', 'COMPLETED', 'CANCELLED') 
-- NOT NULL DEFAULT 'BOOKED';

-- 8. 最终验证
SELECT '在线问诊枚举值修复完成！' AS 'Status';
SELECT 'API接口现在应该可以正常工作了' AS 'Message';
