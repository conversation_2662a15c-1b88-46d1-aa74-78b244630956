<template>
  <div class="schedule-form-overlay">
    <div class="schedule-form-container">
      <div class="form-header">
        <h3>{{ isEdit ? '📝 编辑排班' : '➕ 新增排班' }}</h3>
        <button @click="$emit('close')" class="close-btn">✕</button>
      </div>

      <div class="form-content">
        <form @submit.prevent="handleSubmit" class="schedule-form">
          <!-- 排班日期 -->
          <div class="form-group">
            <label for="scheduleDate">排班日期 *</label>
            <input
              id="scheduleDate"
              v-model="form.scheduleDate"
              type="date"
              :min="minDate"
              required
              class="form-input"
              :class="{ 'error': errors.scheduleDate }"
            />
            <span v-if="errors.scheduleDate" class="error-text">{{ errors.scheduleDate }}</span>
            <span v-else class="help-text">{{ getDateDescription(form.scheduleDate) }}</span>
          </div>

          <!-- 时间设置 -->
          <div class="time-section">
            <h4>⏰ 出诊时间</h4>
            
            <div class="time-inputs">
              <!-- 开始时间 -->
              <div class="form-group">
                <label for="startTime">开始时间 *</label>
                <input
                  id="startTime"
                  v-model="form.startTime"
                  type="time"
                  required
                  class="form-input"
                  :class="{ 'error': errors.startTime }"
                />
                <span v-if="errors.startTime" class="error-text">{{ errors.startTime }}</span>
              </div>

              <!-- 结束时间 -->
              <div class="form-group">
                <label for="endTime">结束时间 *</label>
                <input
                  id="endTime"
                  v-model="form.endTime"
                  type="time"
                  required
                  class="form-input"
                  :class="{ 'error': errors.endTime }"
                />
                <span v-if="errors.endTime" class="error-text">{{ errors.endTime }}</span>
              </div>
            </div>

            <!-- 时长显示 -->
            <div v-if="form.startTime && form.endTime" class="duration-display">
              <span class="duration-label">出诊时长:</span>
              <span class="duration-value">{{ calculateDuration() }}</span>
            </div>
          </div>

          <!-- 号源设置 -->
          <div class="form-group">
            <label for="totalSlots">总号源数量 *</label>
            <input
              id="totalSlots"
              v-model.number="form.totalSlots"
              type="number"
              min="1"
              max="100"
              required
              class="form-input"
              :class="{ 'error': errors.totalSlots }"
            />
            <span v-if="errors.totalSlots" class="error-text">{{ errors.totalSlots }}</span>
            <span v-else class="help-text">建议根据出诊时长合理设置号源数量</span>
          </div>

          <!-- 快捷设置 -->
          <div class="quick-settings">
            <h4>⚡ 快捷设置</h4>
            <div class="preset-buttons">
              <button
                type="button"
                v-for="preset in timePresets"
                :key="preset.name"
                @click="applyPreset(preset)"
                class="preset-btn"
              >
                {{ preset.name }}
                <small>{{ preset.startTime }}-{{ preset.endTime }}</small>
              </button>
            </div>
          </div>

          <!-- 冲突检查结果 -->
          <div v-if="conflictMessage" class="conflict-warning">
            <div class="warning-icon">⚠️</div>
            <div class="warning-content">
              <strong>时间冲突提醒</strong>
              <p>{{ conflictMessage }}</p>
            </div>
          </div>

          <!-- 表单按钮 -->
          <div class="form-actions">
            <button type="button" @click="resetForm" class="btn-reset">
              重置
            </button>
            <button type="submit" :disabled="!isFormValid || loading" class="btn-submit">
              <span v-if="loading">{{ isEdit ? '更新中...' : '创建中...' }}</span>
              <span v-else>{{ isEdit ? '更新排班' : '创建排班' }}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { createSchedule, updateSchedule, checkScheduleConflict } from '@/api/schedule'
import { formatDate, formatTime, formatTimeDisplay, getToday, getDateDisplayName, compareTime, isValidTime } from '@/utils/dateUtils'

// Props & Emits
const props = defineProps({
  schedule: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'success'])

// 响应式数据
const loading = ref(false)
const conflictMessage = ref('')

const form = ref({
  scheduleDate: '',
  startTime: '',
  endTime: '',
  totalSlots: 10
})

const errors = ref({
  scheduleDate: '',
  startTime: '',
  endTime: '',
  totalSlots: ''
})

// 时间预设
const timePresets = ref([
  { name: '上午班', startTime: '08:00', endTime: '12:00', slots: 16 },
  { name: '下午班', startTime: '14:00', endTime: '18:00', slots: 16 },
  { name: '夜班', startTime: '18:00', endTime: '22:00', slots: 12 },
  { name: '全天班', startTime: '08:00', endTime: '18:00', slots: 32 }
])

// 计算属性
const minDate = computed(() => getToday())

const isFormValid = computed(() => {
  return form.value.scheduleDate &&
         form.value.startTime &&
         form.value.endTime &&
         form.value.totalSlots > 0 &&
         !Object.values(errors.value).some(error => error)
})

// 方法
const getDateDescription = (date) => {
  if (!date) return ''
  return getDateDisplayName(date)
}

const calculateDuration = () => {
  if (!form.value.startTime || !form.value.endTime) return ''
  
  const start = new Date(`2000-01-01 ${form.value.startTime}`)
  const end = new Date(`2000-01-01 ${form.value.endTime}`)
  
  if (end <= start) return '时间设置有误'
  
  const diffMs = end.getTime() - start.getTime()
  const hours = Math.floor(diffMs / (1000 * 60 * 60))
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours === 0) {
    return `${minutes}分钟`
  } else if (minutes === 0) {
    return `${hours}小时`
  } else {
    return `${hours}小时${minutes}分钟`
  }
}

const applyPreset = (preset) => {
  form.value.startTime = preset.startTime
  form.value.endTime = preset.endTime
  form.value.totalSlots = preset.slots
  validateForm()
}

const validateForm = () => {
  errors.value = {
    scheduleDate: '',
    startTime: '',
    endTime: '',
    totalSlots: ''
  }

  // 验证日期
  if (!form.value.scheduleDate) {
    errors.value.scheduleDate = '请选择排班日期'
  } else if (form.value.scheduleDate < getToday()) {
    errors.value.scheduleDate = '不能选择过去的日期'
  }

  // 验证开始时间
  if (!form.value.startTime) {
    errors.value.startTime = '请选择开始时间'
  } else if (!isValidTime(form.value.startTime)) {
    errors.value.startTime = '时间格式不正确'
  }

  // 验证结束时间
  if (!form.value.endTime) {
    errors.value.endTime = '请选择结束时间'
  } else if (!isValidTime(form.value.endTime)) {
    errors.value.endTime = '时间格式不正确'
  } else if (form.value.startTime && compareTime(form.value.endTime, form.value.startTime) <= 0) {
    errors.value.endTime = '结束时间必须晚于开始时间'
  }

  // 验证号源数量
  if (!form.value.totalSlots || form.value.totalSlots <= 0) {
    errors.value.totalSlots = '号源数量必须大于0'
  } else if (form.value.totalSlots > 100) {
    errors.value.totalSlots = '号源数量不能超过100'
  }

  return !Object.values(errors.value).some(error => error)
}

const checkConflict = async () => {
  if (!validateForm()) return

  try {
    const scheduleData = {
      scheduleDate: form.value.scheduleDate,
      startTime: formatTime(form.value.startTime),
      endTime: formatTime(form.value.endTime)
    }

    const excludeId = props.isEdit ? props.schedule?.id : null
    const response = await checkScheduleConflict(scheduleData, excludeId)
    
    if (response.data.code === 200 && response.data.data.hasConflict) {
      conflictMessage.value = response.data.data.message
    } else {
      conflictMessage.value = ''
    }
  } catch (error) {
    console.error('检查排班冲突失败:', error)
    conflictMessage.value = ''
  }
}

const resetForm = () => {
  if (props.isEdit && props.schedule) {
    // 编辑模式：重置为原始数据
    form.value = {
      scheduleDate: props.schedule.scheduleDate,
      startTime: formatTimeDisplay(props.schedule.startTime),
      endTime: formatTimeDisplay(props.schedule.endTime),
      totalSlots: props.schedule.totalSlots
    }
  } else {
    // 新增模式：重置为空
    form.value = {
      scheduleDate: '',
      startTime: '',
      endTime: '',
      totalSlots: 10
    }
  }
  
  errors.value = {
    scheduleDate: '',
    startTime: '',
    endTime: '',
    totalSlots: ''
  }
  
  conflictMessage.value = ''
}

const handleSubmit = async () => {
  if (!validateForm()) return

  loading.value = true
  try {
    const scheduleData = {
      scheduleDate: form.value.scheduleDate,
      startTime: formatTime(form.value.startTime),
      endTime: formatTime(form.value.endTime),
      totalSlots: form.value.totalSlots
    }

    let response
    if (props.isEdit) {
      response = await updateSchedule(props.schedule.id, scheduleData)
    } else {
      response = await createSchedule(scheduleData)
    }

    if (response.data.code === 200) {
      emit('success', props.isEdit ? '排班更新成功！' : '排班创建成功！')
    } else {
      alert(response.data.message || '操作失败')
    }
  } catch (error) {
    console.error('排班操作失败:', error)
    if (error.response?.data?.message) {
      alert('操作失败：' + error.response.data.message)
    } else {
      alert('操作失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 监听表单变化，进行冲突检查
watch([() => form.value.scheduleDate, () => form.value.startTime, () => form.value.endTime], () => {
  if (form.value.scheduleDate && form.value.startTime && form.value.endTime) {
    // 延迟检查，避免频繁请求
    setTimeout(checkConflict, 500)
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  if (props.isEdit && props.schedule) {
    // 编辑模式：填充现有数据
    form.value = {
      scheduleDate: props.schedule.scheduleDate,
      startTime: formatTimeDisplay(props.schedule.startTime),
      endTime: formatTimeDisplay(props.schedule.endTime),
      totalSlots: props.schedule.totalSlots
    }
  } else {
    // 新增模式：设置默认日期为明天
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    form.value.scheduleDate = formatDate(tomorrow)
  }
})
</script>

<style scoped>
.schedule-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.schedule-form-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 12px 12px 0 0;
}

.form-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.form-content {
  padding: 24px;
}

.schedule-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-input {
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
  border-color: #ef4444;
}

.help-text {
  font-size: 12px;
  color: #6b7280;
}

.error-text {
  font-size: 12px;
  color: #ef4444;
}

.time-section {
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.time-section h4 {
  margin: 0 0 16px 0;
  color: #1e40af;
  font-size: 16px;
  font-weight: 600;
}

.time-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.duration-display {
  margin-top: 12px;
  padding: 8px 12px;
  background: #e0f2fe;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.duration-label {
  font-size: 14px;
  color: #0369a1;
  font-weight: 500;
}

.duration-value {
  font-size: 14px;
  color: #0c4a6e;
  font-weight: 600;
}

.quick-settings {
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bae6fd;
}

.quick-settings h4 {
  margin: 0 0 12px 0;
  color: #1e40af;
  font-size: 16px;
  font-weight: 600;
}

.preset-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.preset-btn {
  padding: 8px 12px;
  background: white;
  border: 1px solid #3b82f6;
  border-radius: 6px;
  color: #3b82f6;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  font-size: 13px;
}

.preset-btn:hover {
  background: #3b82f6;
  color: white;
}

.preset-btn small {
  display: block;
  font-size: 11px;
  opacity: 0.8;
  margin-top: 2px;
}

.conflict-warning {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  color: #92400e;
}

.warning-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.warning-content strong {
  display: block;
  margin-bottom: 4px;
  font-weight: 600;
}

.warning-content p {
  margin: 0;
  font-size: 14px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 8px;
}

.btn-reset,
.btn-submit {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-reset {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-reset:hover {
  background: #e5e7eb;
}

.btn-submit {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
}

.btn-submit:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-submit:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .schedule-form-container {
    width: 95%;
    margin: 20px;
  }
  
  .form-content {
    padding: 16px;
  }
  
  .time-inputs {
    grid-template-columns: 1fr;
  }
  
  .preset-buttons {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
