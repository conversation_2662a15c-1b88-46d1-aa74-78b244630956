<template>
  <div class="appointment-booking">
    <!-- 步骤指示器 -->
    <div class="step-indicator">
      <div class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
        <div class="step-number">1</div>
        <div class="step-label">选择科室</div>
      </div>
      <div class="step-line" :class="{ active: currentStep > 1 }"></div>
      <div class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
        <div class="step-number">2</div>
        <div class="step-label">选择医生</div>
      </div>
      <div class="step-line" :class="{ active: currentStep > 2 }"></div>
      <div class="step" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
        <div class="step-number">3</div>
        <div class="step-label">选择时间</div>
      </div>
      <div class="step-line" :class="{ active: currentStep > 3 }"></div>
      <div class="step" :class="{ active: currentStep >= 4 }">
        <div class="step-number">4</div>
        <div class="step-label">确认预约</div>
      </div>
    </div>

    <!-- 步骤1: 选择科室 -->
    <div v-if="currentStep === 1" class="step-content">
      <h3>选择科室</h3>
      <div v-if="loadingDepartments" class="loading">
        <div class="loading-spinner"></div>
        <p>正在加载科室信息...</p>
      </div>
      <div v-else class="departments-grid">
        <div
          v-for="department in departments"
          :key="department.id"
          class="department-card"
          :class="{ selected: selectedDepartment?.id === department.id }"
          @click="selectDepartment(department)"
        >
          <div class="department-icon">🏥</div>
          <div class="department-name">{{ department.name }}</div>
          <div class="department-info">
            <span class="doctor-count">{{ department.doctorCount || 0 }}位医生</span>
          </div>
        </div>
      </div>
      <div class="step-actions">
        <button @click="$emit('cancel')" class="btn-secondary">取消</button>
        <button
          @click="nextStep"
          :disabled="!selectedDepartment"
          class="btn-primary"
        >
          下一步
        </button>
      </div>
    </div>

    <!-- 步骤2: 选择医生 -->
    <div v-if="currentStep === 2" class="step-content">
      <h3>选择医生</h3>

      <!-- 医生搜索 -->
      <div class="filter-section">
        <select
          v-model="doctorFilter"
          @change="filterDoctors"
          class="filter-select"
        >
          <option value="">全部医生</option>
          <option value="主任医师">主任医师</option>
          <option value="副主任医师">副主任医师</option>
          <option value="主治医师">主治医师</option>
          <option value="住院医师">住院医师</option>
        </select>
      </div>

      <div v-if="loadingDoctors" class="loading">
        <div class="loading-spinner"></div>
        <p>正在加载医生信息...</p>
      </div>
      <div v-else-if="doctors.length === 0" class="empty-state">
        <p>该科室暂无可预约医生</p>
      </div>
      <div v-else class="doctors-list">
        <div
          v-for="doctor in doctors"
          :key="doctor.userId"
          class="doctor-card"
          :class="{ selected: selectedDoctor?.userId === doctor.userId }"
          @click="selectDoctor(doctor)"
        >
          <div class="doctor-avatar">
            <img v-if="doctor.avatarUrl" :src="doctor.avatarUrl" :alt="doctor.realName">
            <div v-else class="avatar-placeholder">{{ getInitials(doctor.realName) }}</div>
          </div>
          <div class="doctor-info">
            <h4>{{ doctor.realName }}</h4>
            <p class="doctor-title">{{ doctor.title }}</p>
            <p class="doctor-specialty">{{ doctor.specialty }}</p>
          </div>
        </div>
      </div>

      <div class="step-actions">
        <button @click="prevStep" class="btn-secondary">上一步</button>
        <button
          @click="nextStep"
          :disabled="!selectedDoctor"
          class="btn-primary"
        >
          下一步
        </button>
      </div>
    </div>

    <!-- 步骤3: 选择时间 -->
    <div v-if="currentStep === 3" class="step-content">
      <h3>选择预约时间</h3>

      <div class="selected-doctor-info">
        <div class="doctor-summary">
          <div class="doctor-avatar-small">
            <img v-if="selectedDoctor.avatarUrl" :src="selectedDoctor.avatarUrl" :alt="selectedDoctor.realName">
            <div v-else class="avatar-placeholder-small">{{ getInitials(selectedDoctor.realName) }}</div>
          </div>
          <div>
            <h4>{{ selectedDoctor.realName }}</h4>
            <p>{{ selectedDepartment.name }} - {{ selectedDoctor.title }}</p>
          </div>
        </div>
      </div>

      <!-- 日期选择 -->
      <div class="date-selector">
        <h4>选择日期</h4>
        <div class="date-tabs">
          <button
            v-for="date in availableDates"
            :key="date.value"
            class="date-tab"
            :class="{ active: selectedDate === date.value }"
            @click="selectDate(date.value)"
          >
            <div class="date-day">{{ date.day }}</div>
            <div class="date-date">{{ date.date }}</div>
            <div class="date-slots">{{ date.availableSlots }}个时段</div>
          </button>
        </div>
      </div>

      <!-- 时间段选择 -->
      <div v-if="selectedDate" class="time-selector">
        <h4>选择时间段</h4>
        <div v-if="loadingSchedules" class="loading">
          <div class="loading-spinner"></div>
          <p>正在加载时间段...</p>
        </div>
        <div v-else-if="timeSlots.length === 0" class="empty-state">
          <p>该日期暂无可预约时间段</p>
        </div>
        <div v-else class="time-slots">
          <button
            v-for="slot in timeSlots"
            :key="slot.id"
            class="time-slot"
            :class="{
              selected: selectedSchedule?.id === slot.id,
              disabled: slot.availableSlots <= 0
            }"
            :disabled="slot.availableSlots <= 0"
            @click="selectSchedule(slot)"
          >
            <div class="slot-header">
              <div class="slot-doctor">{{ slot.doctorName || slot.doctor?.realName }}</div>
              <div class="slot-time">{{ slot.startTime }} - {{ slot.endTime }}</div>
            </div>
            <div class="slot-info">
              <span class="available-count">剩余{{ slot.availableSlots }}个号源</span>
              <span v-if="slot.department" class="department">{{ slot.department }}</span>
            </div>
          </button>
        </div>
      </div>

      <div class="step-actions">
        <button @click="prevStep" class="btn-secondary">上一步</button>
        <button
          @click="nextStep"
          :disabled="!selectedSchedule"
          class="btn-primary"
        >
          下一步
        </button>
      </div>
    </div>

    <!-- 步骤4: 确认预约 -->
    <div v-if="currentStep === 4" class="step-content">
      <h3>确认预约信息</h3>

      <!-- 预约信息确认 -->
      <div class="booking-summary">
        <div class="summary-section">
          <h4>医生信息</h4>
          <div class="doctor-summary-card">
            <div class="doctor-avatar-small">
              <img v-if="selectedDoctor.avatarUrl" :src="selectedDoctor.avatarUrl" :alt="selectedDoctor.realName">
              <div v-else class="avatar-placeholder-small">{{ getInitials(selectedDoctor.realName) }}</div>
            </div>
            <div class="doctor-details">
              <h5>{{ selectedDoctor.realName }}</h5>
              <p>{{ selectedDepartment.name }} - {{ selectedDoctor.title }}</p>
              <p>{{ selectedDoctor.specialty }}</p>
            </div>
          </div>
        </div>

        <div class="summary-section">
          <h4>预约时间</h4>
          <div class="time-summary">
            <div class="time-info">
              <span class="date">{{ formatDate(selectedDate) }}</span>
              <span class="time">{{ selectedSchedule.startTime }}</span>
            </div>
          </div>
        </div>

        <div class="summary-section">
          <h4>就诊人信息</h4>
          <div class="profile-selector">
            <select v-model="selectedProfileId" class="profile-select" v-if="healthProfiles.length > 0">
              <option value="">请选择就诊人</option>
              <option
                v-for="profile in healthProfiles"
                :key="profile.id"
                :value="profile.id"
              >
                {{ profile.profileOwnerName }}
              </option>
            </select>
            <div v-else class="no-profiles">
              <p>您还没有健康档案</p>
              <button @click="createHealthProfile" class="create-profile-btn">
                创建健康档案
              </button>
            </div>
          </div>
        </div>

        <div class="summary-section">
          <h4>预约备注</h4>
          <textarea
            v-model="appointmentNotes"
            placeholder="请简要描述症状或预约原因（可选）"
            class="notes-textarea"
            rows="3"
          ></textarea>
        </div>
      </div>

      <div class="step-actions">
        <button @click="prevStep" class="btn-secondary">上一步</button>
        <button
          @click="confirmBooking"
          :disabled="!selectedProfileId || submitting"
          class="btn-primary"
        >
          {{ submitting ? '预约中...' : '确认预约' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import * as appointmentsApi from '@/api/appointments'
import * as healthApi from '@/api/health'

export default {
  name: 'AppointmentBooking',
  emits: ['success', 'cancel'],
  setup(props, { emit }) {
    const router = useRouter()

    // 响应式数据
    const currentStep = ref(1)
    const submitting = ref(false)

    // 加载状态
    const loadingDepartments = ref(false)
    const loadingDoctors = ref(false)
    const loadingSchedules = ref(false)

    // 数据
    const departments = ref([])
    const doctors = ref([])
    const timeSlots = ref([])
    const healthProfiles = ref([])

    // 选择的数据
    const selectedDepartment = ref(null)
    const selectedDoctor = ref(null)
    const selectedDate = ref('')
    const selectedSchedule = ref(null)
    const selectedProfileId = ref('')
    const appointmentNotes = ref('')

    // 筛选
    const doctorFilter = ref('')
    const allDoctors = ref([]) // 存储所有医生数据

    // 计算属性
    const availableDates = computed(() => {
      const dates = []
      const today = new Date()

      for (let i = 0; i < 7; i++) {
        const date = new Date(today)
        date.setDate(today.getDate() + i)

        dates.push({
          value: date.toISOString().split('T')[0],
          day: date.toLocaleDateString('zh-CN', { weekday: 'short' }),
          date: date.getDate(),
          availableSlots: 0 // 将通过API获取真实数据
        })
      }

      return dates
    })

    // 方法
    const loadDepartments = async () => {
      try {
        loadingDepartments.value = true
        const response = await appointmentsApi.getDepartments()

        if (response.data.code === 200) {
          departments.value = response.data.data || []
        } else {
          console.error('获取科室列表失败:', response.data.message)
          alert('获取科室列表失败，请稍后重试')
        }
      } catch (error) {
        console.error('加载科室失败:', error)
        alert('加载科室失败，请稍后重试')
      } finally {
        loadingDepartments.value = false
      }
    }

    const loadDoctors = async () => {
      if (!selectedDepartment.value) return

      try {
        loadingDoctors.value = true
        const params = {
          page: 1,
          size: 20
        }

        const response = await appointmentsApi.getDepartmentDoctors(selectedDepartment.value.id, params)

        if (response.data.code === 200) {
          allDoctors.value = response.data.data.content || []
          doctors.value = allDoctors.value // 初始显示所有医生
        } else {
          console.error('获取医生列表失败:', response.data.message)
          alert('获取医生列表失败，请稍后重试')
        }
      } catch (error) {
        console.error('加载医生失败:', error)
        alert('加载医生失败，请稍后重试')
      } finally {
        loadingDoctors.value = false
      }
    }

    const loadSchedules = async () => {
      if (!selectedDate.value) {
        console.log('loadSchedules: 缺少日期参数', {
          selectedDate: selectedDate.value
        })
        return
      }

      try {
        loadingSchedules.value = true
        const params = {
          date: selectedDate.value
        }

        // 如果选择了特定医生，添加医生ID过滤
        if (selectedDoctor.value) {
          params.doctorId = selectedDoctor.value.userId
        }

        // 如果选择了科室，添加科室ID过滤
        if (selectedDepartment.value) {
          params.departmentId = selectedDepartment.value.id
        }

        console.log('开始加载可预约时段:', {
          params: params,
          selectedDoctor: selectedDoctor.value,
          selectedDepartment: selectedDepartment.value
        })

        // 使用可预约时段API而不是医生排班API
        const response = await appointmentsApi.getAvailableSchedules(params)
        console.log('可预约时段API响应:', response)
        console.log('API返回的原始数据:', response.data)

        if (response.data.code === 200) {
          const rawData = response.data.data || []
          timeSlots.value = rawData
          console.log('可预约时段加载成功:', timeSlots.value)
          console.log('时段数量:', timeSlots.value.length)

          // 详细打印每个时段的信息
          timeSlots.value.forEach((slot, index) => {
            console.log(`时段${index + 1}:`, {
              id: slot.id,
              doctorName: slot.doctorName,
              startTime: slot.startTime,
              endTime: slot.endTime,
              availableSlots: slot.availableSlots
            })
          })

          // 如果没有可预约时段，给用户友好提示
          if (timeSlots.value.length === 0) {
            console.warn('该日期暂无可预约时段，参数:', params)
          }
        } else {
          console.error('获取可预约时段失败:', response.data.message)
          alert('获取可预约时段失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('加载可预约时段失败:', error)
        console.error('错误详情:', error.response?.data)
        alert('加载可预约时段失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loadingSchedules.value = false
      }
    }

    const loadHealthProfiles = async () => {
      try {
        console.log('开始加载健康档案...')
        const response = await healthApi.getHealthProfiles()
        console.log('健康档案API响应:', response)

        if (response.data.code === 200) {
          // 检查数据结构，兼容不同的返回格式
          const data = response.data.data
          if (data && data.profiles) {
            // 分页格式：{ profiles: [], page: 1, total: 10 }
            healthProfiles.value = data.profiles || []
          } else if (Array.isArray(data)) {
            // 数组格式：[{}, {}, {}]
            healthProfiles.value = data
          } else {
            // 其他格式
            healthProfiles.value = []
          }

          console.log('健康档案加载成功:', healthProfiles.value)

          if (healthProfiles.value.length === 0) {
            console.warn('当前用户没有健康档案')
            // 不再弹出警告，让用户在确认页面看到提示即可
          }
        } else {
          console.error('获取健康档案失败:', response.data.message)
          alert('获取健康档案失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('加载健康档案失败:', error)
        console.error('错误详情:', error.response?.data)
        alert('加载健康档案失败: ' + (error.response?.data?.message || error.message))
      }
    }

    const selectDepartment = (department) => {
      selectedDepartment.value = department
    }

    const selectDoctor = (doctor) => {
      selectedDoctor.value = doctor
    }

    const selectDate = (date) => {
      selectedDate.value = date
      selectedSchedule.value = null
      // 选择日期后立即加载可预约时段
      loadSchedules()
    }

    const selectSchedule = (schedule) => {
      selectedSchedule.value = schedule
    }

    const filterDoctors = () => {
      if (!doctorFilter.value) {
        // 显示所有医生
        doctors.value = allDoctors.value
      } else {
        // 按职称筛选
        doctors.value = allDoctors.value.filter(doctor =>
          doctor.title && doctor.title.includes(doctorFilter.value)
        )
      }
      console.log('筛选结果:', {
        filter: doctorFilter.value,
        total: allDoctors.value.length,
        filtered: doctors.value.length
      })
    }

    const nextStep = () => {
      if (currentStep.value < 4) {
        currentStep.value++

        if (currentStep.value === 2) {
          loadDoctors()
        } else if (currentStep.value === 4) {
          loadHealthProfiles()
        }
      }
    }

    const prevStep = () => {
      if (currentStep.value > 1) {
        currentStep.value--
      }
    }

    const confirmBooking = async () => {
      if (!selectedSchedule.value || !selectedProfileId.value) {
        alert('请完善预约信息')
        return
      }

      try {
        submitting.value = true

        const bookingData = {
          scheduleId: selectedSchedule.value.id,
          profileId: selectedProfileId.value,
          notes: appointmentNotes.value
        }

        const response = await appointmentsApi.createAppointment(bookingData)

        if (response.data.code === 200) {
          alert('预约成功！')
          emit('success')
        } else {
          alert(response.data.message || '预约失败')
        }
      } catch (error) {
        console.error('预约失败:', error)
        alert('预约失败，请稍后重试')
      } finally {
        submitting.value = false
      }
    }

    const createHealthProfile = () => {
      router.push('/profile/create')
    }



    // 工具函数
    const getInitials = (name) => {
      if (!name) return '?'
      return name.charAt(0).toUpperCase()
    }

    const formatDate = (dateStr) => {
      const date = new Date(dateStr)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        weekday: 'long'
      })
    }

    // 监听器
    watch(selectedDepartment, () => {
      selectedDoctor.value = null
      doctors.value = []
      allDoctors.value = []
      doctorFilter.value = '' // 重置筛选
    })

    watch(selectedDoctor, () => {
      selectedSchedule.value = null
      // 如果已经选择了日期，重新加载该日期的可预约时段
      if (selectedDate.value) {
        loadSchedules()
      } else {
        timeSlots.value = []
      }
    })

    // 组件挂载
    onMounted(() => {
      loadDepartments()
    })

    return {
      currentStep,
      submitting,
      loadingDepartments,
      loadingDoctors,
      loadingSchedules,
      departments,
      doctors,
      timeSlots,
      healthProfiles,
      selectedDepartment,
      selectedDoctor,
      selectedDate,
      selectedSchedule,
      selectedProfileId,
      appointmentNotes,
      doctorFilter,
      availableDates,
      selectDepartment,
      selectDoctor,
      selectDate,
      selectSchedule,
      filterDoctors,
      nextStep,
      prevStep,
      confirmBooking,
      createHealthProfile,
      getInitials,
      formatDate
    }
  }
}
</script>

<style scoped>
.appointment-booking {
  max-width: 800px;
  margin: 0 auto;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
  padding: 20px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.step.active .step-number {
  background: #4A90E2;
  color: white;
}

.step.completed .step-number {
  background: #28a745;
  color: white;
}

.step-label {
  font-size: 12px;
  color: #6c757d;
  text-align: center;
}

.step.active .step-label {
  color: #4A90E2;
  font-weight: 500;
}

.step-line {
  width: 60px;
  height: 2px;
  background: #e9ecef;
  margin: 0 10px;
  margin-bottom: 20px;
  transition: all 0.3s;
}

.step-line.active {
  background: #4A90E2;
}

/* 步骤内容 */
.step-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.step-content h3 {
  margin: 0 0 30px 0;
  color: #2c3e50;
  font-size: 24px;
  text-align: center;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4A90E2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 科室网格 */
.departments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.department-card {
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.department-card:hover {
  border-color: #4A90E2;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.department-card.selected {
  border-color: #4A90E2;
  background: #f8f9ff;
}

.department-icon {
  font-size: 40px;
  margin-bottom: 15px;
}

.department-name {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.department-info {
  color: #6c757d;
  font-size: 14px;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

.filter-select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  background: white;
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.filter-select option {
  padding: 8px;
}

/* 医生列表 */
.doctors-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 30px;
}

.doctor-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.doctor-card:hover {
  border-color: #4A90E2;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.doctor-card.selected {
  border-color: #4A90E2;
  background: #f8f9ff;
}

.doctor-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.doctor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  font-size: 24px;
  font-weight: bold;
  color: #6c757d;
}

.doctor-info {
  flex: 1;
}

.doctor-info h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 20px;
}

.doctor-title {
  margin: 0 0 8px 0;
  color: #4A90E2;
  font-weight: 500;
}

.doctor-specialty {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

/* 选中的医生信息 */
.selected-doctor-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.doctor-summary {
  display: flex;
  align-items: center;
  gap: 15px;
}

.doctor-avatar-small {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
}

.doctor-avatar-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder-small {
  font-size: 24px;
  font-weight: bold;
  color: #6c757d;
}

.doctor-summary h4 {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 18px;
}

.doctor-summary p {
  margin: 2px 0;
  color: #6c757d;
  font-size: 14px;
}

/* 日期选择器 */
.date-selector {
  margin-bottom: 30px;
}

.date-selector h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
}

.date-tabs {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  padding-bottom: 10px;
}

.date-tab {
  min-width: 100px;
  padding: 15px 10px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s;
}

.date-tab:hover {
  border-color: #4A90E2;
}

.date-tab.active {
  border-color: #4A90E2;
  background: #4A90E2;
  color: white;
}

.date-day {
  font-size: 12px;
  margin-bottom: 4px;
}

.date-date {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
}

.date-slots {
  font-size: 10px;
  opacity: 0.8;
}

/* 时间段选择器 */
.time-selector h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
}

.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.time-slot {
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  text-align: left;
  transition: all 0.3s;
  min-height: 90px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.time-slot:hover:not(:disabled) {
  border-color: #4A90E2;
  background: #f8f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.15);
}

.time-slot.selected {
  border-color: #4A90E2;
  background: #4A90E2;
  color: white;
}

.time-slot:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f8f9fa;
}

.slot-header {
  margin-bottom: 8px;
}

.slot-doctor {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #2c3e50;
}

.time-slot.selected .slot-doctor {
  color: white;
}

.slot-time {
  font-size: 14px;
  color: #6c757d;
}

.time-slot.selected .slot-time {
  color: rgba(255, 255, 255, 0.9);
}

.slot-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.available-count {
  font-weight: 500;
  color: #28a745;
}

.time-slot.selected .available-count {
  color: rgba(255, 255, 255, 0.9);
}

.department {
  color: #6c757d;
}

.time-slot.selected .department {
  color: rgba(255, 255, 255, 0.7);
}

/* 预约摘要 */
.booking-summary {
  margin-bottom: 30px;
}

.summary-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.summary-section:last-child {
  border-bottom: none;
}

.summary-section h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.doctor-summary-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.doctor-details h5 {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 16px;
}

.doctor-details p {
  margin: 2px 0;
  color: #6c757d;
  font-size: 14px;
}

.time-summary {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.time-info .date {
  font-size: 16px;
  color: #2c3e50;
  font-weight: 500;
}

.time-info .time {
  font-size: 18px;
  color: #4A90E2;
  font-weight: bold;
}

.profile-select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
}

.no-profiles {
  text-align: center;
  padding: 20px;
  border: 2px dashed #ddd;
  border-radius: 6px;
  background: #f8f9fa;
}

.no-profiles p {
  margin: 0 0 15px 0;
  color: #6c757d;
  font-size: 16px;
}

.create-profile-btn {
  padding: 10px 20px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.3s;
}

.create-profile-btn:hover {
  background: #218838;
}

.notes-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
  font-family: inherit;
}

.notes-textarea:focus {
  outline: none;
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

/* 步骤操作按钮 */
.step-actions {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  margin-top: 30px;
}

.btn-primary, .btn-secondary {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background: #4A90E2;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #357abd;
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .step-indicator {
    flex-direction: column;
    gap: 20px;
  }

  .step-line {
    width: 2px;
    height: 30px;
    margin: 10px 0;
  }

  .departments-grid {
    grid-template-columns: 1fr;
  }

  .doctor-card {
    flex-direction: column;
    text-align: center;
  }

  .doctor-summary {
    flex-direction: column;
    text-align: center;
  }

  .date-tabs {
    justify-content: center;
  }

  .time-slots {
    grid-template-columns: repeat(2, 1fr);
  }

  .time-info {
    flex-direction: column;
    gap: 8px;
  }

  .step-actions {
    flex-direction: column;
  }
}
</style>