<template>
  <div class="metric-chart">
    <div class="chart-header">
      <div class="chart-title">
        <span class="chart-icon">{{ getMetricConfig(metricType).icon }}</span>
        <h3>{{ getMetricConfig(metricType).label }}趋势图</h3>
      </div>
      <div class="chart-controls">
        <select v-model="selectedPeriod" @change="loadStatistics" class="period-selector">
          <option value="week">最近7天</option>
          <option value="month">最近30天</option>
          <option value="year">最近一年</option>
        </select>
      </div>
    </div>

    <div class="chart-content">
      <!-- 统计卡片 -->
      <div v-if="statistics" class="statistics-cards">
        <div class="stat-card">
          <div class="stat-label">平均值</div>
          <div class="stat-value">{{ formatStatValue(statistics.average) }}</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">最高值</div>
          <div class="stat-value high">{{ formatStatValue(statistics.max) }}</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">最低值</div>
          <div class="stat-value low">{{ formatStatValue(statistics.min) }}</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">趋势</div>
          <div class="stat-value trend" :class="getTrendClass()">
            {{ getTrendText() }}
          </div>
        </div>
      </div>

      <!-- 图表容器 -->
      <div ref="chartContainer" class="chart-container" v-show="!loading && chartData.length > 0"></div>

      <!-- 加载状态 -->
      <div v-if="loading" class="chart-loading">
        <div class="loading-spinner">📊 加载图表数据...</div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="chartData.length === 0" class="chart-empty">
        <div class="empty-icon">📈</div>
        <h4>暂无数据</h4>
        <p>该时间段内没有{{ getMetricConfig(metricType).label }}记录</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { getHealthStatistics, getMetricConfig } from '@/api/healthRecords'

// Props
const props = defineProps({
  profileId: {
    type: Number,
    required: true
  },
  metricType: {
    type: String,
    required: true
  }
})

// 响应式数据
const loading = ref(false)
const selectedPeriod = ref('month')
const statistics = ref(null)
const chartData = ref([])
const chartContainer = ref(null)
let chartInstance = null

// 方法
const loadStatistics = async () => {
  if (!props.profileId || !props.metricType) return

  loading.value = true
  try {
    const response = await getHealthStatistics({
      profileId: props.profileId,
      metricType: props.metricType,
      period: selectedPeriod.value
    })

    if (response.data.code === 200) {
      const data = response.data.data
      statistics.value = data.statistics
      chartData.value = data.chartData || []
      
      await nextTick()
      renderChart()
    } else {
      console.error('获取统计数据失败:', response.data.message)
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

const renderChart = () => {
  if (!chartContainer.value || chartData.value.length === 0) return

  // 销毁现有图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建新的图表实例
  chartInstance = echarts.init(chartContainer.value)

  const config = getMetricConfig(props.metricType)
  const dates = chartData.value.map(item => item.date)
  const values = chartData.value.map(item => item.value)

  let option = {}

  if (props.metricType === 'blood_pressure') {
    // 血压特殊处理 - 显示收缩压和舒张压
    const systolicValues = chartData.value.map(item => item.systolicPressure || item.value)
    const diastolicValues = chartData.value.map(item => item.diastolicPressure || item.value * 0.67) // 估算舒张压

    option = {
      title: {
        text: `${config.label}变化趋势`,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
          color: '#262626'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          let result = `${params[0].axisValue}<br/>`
          params.forEach(param => {
            result += `${param.seriesName}: ${param.value} ${config.unit}<br/>`
          })
          return result
        }
      },
      legend: {
        data: ['收缩压', '舒张压'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLine: {
          lineStyle: { color: '#e8e8e8' }
        },
        axisLabel: {
          color: '#8c8c8c'
        }
      },
      yAxis: {
        type: 'value',
        name: config.unit,
        axisLine: {
          lineStyle: { color: '#e8e8e8' }
        },
        axisLabel: {
          color: '#8c8c8c'
        },
        splitLine: {
          lineStyle: { color: '#f0f0f0' }
        }
      },
      series: [
        {
          name: '收缩压',
          type: 'line',
          data: systolicValues,
          smooth: true,
          lineStyle: { color: '#ff4d4f', width: 3 },
          itemStyle: { color: '#ff4d4f' },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(255, 77, 79, 0.3)' },
              { offset: 1, color: 'rgba(255, 77, 79, 0.1)' }
            ])
          }
        },
        {
          name: '舒张压',
          type: 'line',
          data: diastolicValues,
          smooth: true,
          lineStyle: { color: '#1890ff', width: 3 },
          itemStyle: { color: '#1890ff' },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
            ])
          }
        }
      ]
    }
  } else {
    // 其他指标的通用图表
    option = {
      title: {
        text: `${config.label}变化趋势`,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
          color: '#262626'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          return `${params[0].axisValue}<br/>${config.label}: ${params[0].value} ${config.unit}`
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLine: {
          lineStyle: { color: '#e8e8e8' }
        },
        axisLabel: {
          color: '#8c8c8c'
        }
      },
      yAxis: {
        type: 'value',
        name: config.unit,
        axisLine: {
          lineStyle: { color: '#e8e8e8' }
        },
        axisLabel: {
          color: '#8c8c8c'
        },
        splitLine: {
          lineStyle: { color: '#f0f0f0' }
        }
      },
      series: [{
        name: config.label,
        type: 'line',
        data: values,
        smooth: true,
        lineStyle: { color: '#1890ff', width: 3 },
        itemStyle: { color: '#1890ff' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
            { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
          ])
        }
      }]
    }
  }

  chartInstance.setOption(option)

  // 响应式调整
  const resizeChart = () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }
  window.addEventListener('resize', resizeChart)
}

const formatStatValue = (value) => {
  if (value === null || value === undefined) return '--'
  const config = getMetricConfig(props.metricType)
  return `${value} ${config.unit}`
}

const getTrendClass = () => {
  if (!statistics.value) return ''
  const trend = statistics.value.trend
  if (trend === 'increasing') return 'up'
  if (trend === 'decreasing') return 'down'
  return 'stable'
}

const getTrendText = () => {
  if (!statistics.value) return '--'
  const trendMap = {
    increasing: '上升 ↗',
    decreasing: '下降 ↘',
    stable: '稳定 →',
    insufficient_data: '数据不足'
  }
  return trendMap[statistics.value.trend] || '--'
}

// 监听props变化
watch([() => props.profileId, () => props.metricType], () => {
  loadStatistics()
}, { immediate: true })

// 生命周期
onMounted(() => {
  loadStatistics()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', () => {})
})
</script>

<style scoped>
.metric-chart {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-icon {
  font-size: 24px;
}

.chart-title h3 {
  color: #262626;
  font-size: 18px;
  font-weight: 700;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 12px;
}

.period-selector {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 13px;
  color: #595959;
  cursor: pointer;
  transition: all 0.2s ease;
}

.period-selector:hover {
  border-color: #1890ff;
}

.period-selector:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.chart-content {
  padding: 24px 28px;
}

/* 统计卡片 */
.statistics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-label {
  color: #8c8c8c;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  color: #262626;
  font-size: 18px;
  font-weight: 700;
}

.stat-value.high {
  color: #ff4d4f;
}

.stat-value.low {
  color: #52c41a;
}

.stat-value.trend.up {
  color: #ff4d4f;
}

.stat-value.trend.down {
  color: #52c41a;
}

.stat-value.trend.stable {
  color: #faad14;
}

/* 图表容器 */
.chart-container {
  width: 100%;
  height: 400px;
  margin-top: 16px;
}

/* 加载状态 */
.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #8c8c8c;
}

.loading-spinner {
  font-size: 16px;
  font-weight: 600;
}

/* 空状态 */
.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.chart-empty h4 {
  color: #262626;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.chart-empty p {
  color: #8c8c8c;
  font-size: 14px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .chart-content {
    padding: 20px;
  }

  .statistics-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .chart-container {
    height: 300px;
  }
}

@media (max-width: 480px) {
  .statistics-cards {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 250px;
  }
}
</style>
