<template>
  <div class="appointment-detail-overlay">
    <div class="appointment-detail-container">
      <div class="detail-header">
        <h3>👥 预约详情</h3>
        <button @click="$emit('close')" class="close-btn">✕</button>
      </div>

      <div class="detail-content">
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner">⏳</div>
          <p>加载中...</p>
        </div>

        <div v-else-if="appointment" class="appointment-info">
          <!-- 患者信息卡片 -->
          <div class="info-card patient-card">
            <div class="card-header">
              <h4>👤 患者信息</h4>
              <span class="status-badge" :class="getAppointmentStatusClass(appointment.status)">
                {{ getAppointmentStatusText(appointment.status) }}
              </span>
            </div>
            
            <div class="patient-info">
              <div class="patient-avatar">
                <span class="avatar-text">
                  {{ getPatientInitial(appointment.patient) }}
                </span>
              </div>
              <div class="patient-details">
                <div class="patient-name">{{ getPatientName(appointment.patient) }}</div>
                <div class="patient-meta">
                  <span class="age">{{ getPatientAge(appointment.patient?.birthDate) }}岁</span>
                  <span class="gender">{{ getPatientGender(appointment.patient?.gender) }}</span>
                </div>
                <div class="patient-contact">
                  <span class="phone">📞 {{ appointment.patient?.phoneNumber || '未提供' }}</span>
                </div>
                <div v-if="appointment.patient?.idCardNumber" class="patient-id">
                  <span class="id-card">🆔 {{ maskIdCard(appointment.patient.idCardNumber) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 预约信息卡片 -->
          <div class="info-card appointment-card">
            <div class="card-header">
              <h4>📅 预约信息</h4>
              <div class="priority-indicator" :class="getPriorityClass(getAppointmentPriority(appointment))">
                {{ getPriorityText(getAppointmentPriority(appointment)) }}
              </div>
            </div>
            
            <div class="appointment-details">
              <div class="detail-item">
                <span class="label">预约日期:</span>
                <span class="value">{{ formatAppointmentDate(appointment.appointmentDate) }}</span>
              </div>
              <div class="detail-item">
                <span class="label">预约时间:</span>
                <span class="value">{{ formatTimeDisplay(appointment.appointmentTime) }}</span>
              </div>
              <div class="detail-item">
                <span class="label">相对时间:</span>
                <span class="value">{{ getRelativeTime(appointment.appointmentDate) }}</span>
              </div>
              <div class="detail-item">
                <span class="label">预约原因:</span>
                <span class="value">{{ appointment.reason || '常规检查' }}</span>
              </div>
              <div v-if="appointment.notes" class="detail-item">
                <span class="label">医生备注:</span>
                <span class="value">{{ appointment.notes }}</span>
              </div>
              <div class="detail-item">
                <span class="label">预约时间:</span>
                <span class="value">{{ formatDateTime(appointment.createdAt) }}</span>
              </div>
            </div>
          </div>

          <!-- 医生信息卡片 -->
          <div class="info-card doctor-card">
            <div class="card-header">
              <h4>👨‍⚕️ 医生信息</h4>
            </div>
            
            <div class="doctor-info">
              <div class="doctor-avatar">
                <span class="avatar-text">医</span>
              </div>
              <div class="doctor-details">
                <div class="doctor-name">{{ appointment.doctor?.realName || '王健康' }}</div>
                <div class="doctor-title">{{ appointment.doctor?.title || '主任医师' }}</div>
                <div class="doctor-department">{{ appointment.doctor?.departmentName || '内科' }}</div>
                <div v-if="appointment.doctor?.specialty" class="doctor-specialty">
                  专长: {{ appointment.doctor.specialty }}
                </div>
              </div>
            </div>
          </div>

          <!-- 诊疗记录卡片 -->
          <div v-if="medicalRecords.length > 0" class="info-card records-card">
            <div class="card-header">
              <h4>📋 诊疗记录</h4>
              <span class="record-count">{{ medicalRecords.length }} 条记录</span>
            </div>
            
            <div class="medical-records">
              <div
                v-for="record in medicalRecords"
                :key="record.id"
                class="record-item"
              >
                <div class="record-header">
                  <span class="record-date">{{ formatDate(record.recordDate) }}</span>
                  <span class="record-doctor">{{ record.doctorName }}</span>
                </div>
                <div class="record-content">
                  <div class="record-field">
                    <strong>诊断:</strong> {{ record.diagnosis }}
                  </div>
                  <div class="record-field">
                    <strong>治疗:</strong> {{ record.treatment }}
                  </div>
                  <div v-if="record.prescription" class="record-field">
                    <strong>处方:</strong> {{ record.prescription }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-section">
            <div class="action-buttons">
              <!-- 确认预约 -->
              <button
                v-if="canOperateAppointment(appointment, 'confirm')"
                @click="handleConfirm"
                :disabled="actionLoading"
                class="action-btn confirm-btn"
              >
                <span v-if="actionLoading">确认中...</span>
                <span v-else>✅ 确认预约</span>
              </button>

              <!-- 完成诊疗 -->
              <button
                v-if="canOperateAppointment(appointment, 'complete')"
                @click="handleComplete"
                :disabled="actionLoading"
                class="action-btn complete-btn"
              >
                <span v-if="actionLoading">完成中...</span>
                <span v-else">✔️ 完成诊疗</span>
              </button>

              <!-- 添加诊疗记录 -->
              <button
                v-if="canOperateAppointment(appointment, 'addRecord')"
                @click="$emit('addRecord', appointment)"
                class="action-btn record-btn"
              >
                📝 添加诊疗记录
              </button>

              <!-- 查看患者病历 -->
              <button
                @click="$emit('viewHistory', appointment.patient)"
                class="action-btn history-btn"
              >
                📋 查看完整病历
              </button>

              <!-- 取消预约 -->
              <button
                v-if="canOperateAppointment(appointment, 'cancel')"
                @click="handleCancel"
                :disabled="actionLoading"
                class="action-btn cancel-btn"
              >
                <span v-if="actionLoading">取消中...</span>
                <span v-else>❌ 取消预约</span>
              </button>
            </div>
          </div>
        </div>

        <div v-else class="error-state">
          <div class="error-icon">❌</div>
          <h4>加载失败</h4>
          <p>无法获取预约详情，请重试</p>
          <button @click="loadAppointmentDetail" class="retry-btn">
            重新加载
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import {
  getPatientAppointmentDetail,
  confirmAppointment,
  completeAppointment,
  cancelPatientAppointment,
  getPatientRecords
} from '@/api/appointments'
import {
  getAppointmentStatusText,
  getAppointmentStatusClass,
  getAppointmentPriority,
  getPriorityClass,
  canOperateAppointment,
  getPatientAge,
  formatAppointmentDate
} from '@/utils/appointmentUtils'
import {
  formatTimeDisplay,
  getRelativeDateDescription,
  formatDate
} from '@/utils/dateUtils'

// Props & Emits
const props = defineProps({
  appointmentId: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['close', 'success', 'addRecord', 'viewHistory'])

// 响应式数据
const loading = ref(false)
const actionLoading = ref(false)
const appointment = ref(null)
const medicalRecords = ref([])

// 方法
const getPatientName = (patient) => {
  return patient?.name || patient?.profileOwnerName || '未知患者'
}

const getPatientInitial = (patient) => {
  const name = getPatientName(patient)
  return name.charAt(0).toUpperCase()
}

const getPatientGender = (gender) => {
  const genderMap = {
    'male': '男',
    'female': '女',
    'MALE': '男',
    'FEMALE': '女',
    'OTHER': '其他'
  }
  return genderMap[gender] || ''
}

const getRelativeTime = (date) => {
  return getRelativeDateDescription(date)
}

const getPriorityText = (priority) => {
  const priorityMap = {
    'urgent': '紧急',
    'high': '高',
    'normal': '普通',
    'low': '低'
  }
  return priorityMap[priority] || '普通'
}

const maskIdCard = (idCard) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

const loadAppointmentDetail = async () => {
  loading.value = true
  try {
    const response = await getPatientAppointmentDetail(props.appointmentId)
    if (response.data.code === 200) {
      appointment.value = response.data.data

      // 加载患者病历记录
      if (appointment.value.patient?.id) {
        await loadPatientRecords(appointment.value.patient.id)
      }
    } else {
      console.error('获取预约详情失败:', response.data.message)
    }
  } catch (error) {
    console.error('获取预约详情失败:', error)
  } finally {
    loading.value = false
  }
}

const loadPatientRecords = async (patientId) => {
  try {
    const response = await getPatientRecords(patientId)
    if (response.data.code === 200) {
      medicalRecords.value = response.data.data || []
    }
  } catch (error) {
    console.error('获取患者病历失败:', error)
    medicalRecords.value = []
  }
}

const handleConfirm = async () => {
  actionLoading.value = true
  try {
    const response = await confirmAppointment(props.appointmentId)
    if (response.data.code === 200) {
      emit('success', '预约确认成功！')
      await loadAppointmentDetail() // 重新加载数据
    } else {
      alert('确认失败：' + response.data.message)
    }
  } catch (error) {
    console.error('确认预约失败:', error)
    alert('确认失败，请重试')
  } finally {
    actionLoading.value = false
  }
}

const handleComplete = async () => {
  const notes = prompt('请输入诊疗备注（可选）:')
  if (notes === null) return // 用户取消
  
  actionLoading.value = true
  try {
    const response = await completeAppointment(props.appointmentId, { notes })
    if (response.data.code === 200) {
      emit('success', '诊疗完成！')
      await loadAppointmentDetail() // 重新加载数据
    } else {
      alert('完成失败：' + response.data.message)
    }
  } catch (error) {
    console.error('完成诊疗失败:', error)
    alert('完成失败，请重试')
  } finally {
    actionLoading.value = false
  }
}

const handleCancel = async () => {
  const reason = prompt('请输入取消原因:')
  if (!reason) return
  
  actionLoading.value = true
  try {
    const response = await cancelPatientAppointment(props.appointmentId)
    if (response.data.code === 200) {
      emit('success', '预约已取消')
      await loadAppointmentDetail() // 重新加载数据
    } else {
      alert('取消失败：' + response.data.message)
    }
  } catch (error) {
    console.error('取消预约失败:', error)
    alert('取消失败，请重试')
  } finally {
    actionLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadAppointmentDetail()
})
</script>

<style scoped>
.appointment-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.appointment-detail-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 12px 12px 0 0;
}

.detail-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.detail-content {
  padding: 24px;
}

.loading-state,
.error-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loading-spinner,
.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-state h4 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 18px;
}

.error-state p {
  margin: 0 0 24px 0;
  font-size: 14px;
}

.retry-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.retry-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.appointment-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.card-header h4 {
  margin: 0;
  color: #1e40af;
  font-size: 16px;
  font-weight: 600;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-booked {
  background: #dbeafe;
  color: #1d4ed8;
}

.status-completed {
  background: #dcfce7;
  color: #166534;
}

.status-cancelled {
  background: #fee2e2;
  color: #991b1b;
}

.priority-indicator {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
}

.priority-urgent {
  background: #fee2e2;
  color: #991b1b;
}

.priority-high {
  background: #fef3c7;
  color: #92400e;
}

.priority-normal {
  background: #f3f4f6;
  color: #6b7280;
}

.priority-low {
  background: #f0fdf4;
  color: #166534;
}

.patient-info,
.doctor-info {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
}

.patient-avatar,
.doctor-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: 600;
  flex-shrink: 0;
}

.patient-name,
.doctor-name {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.patient-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #6b7280;
}

.patient-contact,
.patient-id {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.doctor-title,
.doctor-department {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.doctor-specialty {
  font-size: 13px;
  color: #3b82f6;
  margin-top: 8px;
}

.appointment-details {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item .label {
  font-weight: 500;
  color: #6b7280;
  font-size: 14px;
  min-width: 80px;
}

.detail-item .value {
  color: #1f2937;
  font-size: 14px;
  text-align: right;
  flex: 1;
}

.record-count {
  background: #f0f9ff;
  color: #0369a1;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
}

.medical-records {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.record-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.record-date {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.record-doctor {
  color: #3b82f6;
  font-size: 13px;
}

.record-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.record-field {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
}

.record-field strong {
  color: #1f2937;
}

.action-section {
  margin-top: 8px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
  min-width: 120px;
}

.confirm-btn {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #16a34a, #15803d);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.complete-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.complete-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.record-btn {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.record-btn:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.history-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.history-btn:hover {
  background: #e5e7eb;
}

.cancel-btn {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.cancel-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.action-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .appointment-detail-container {
    width: 95%;
    margin: 20px;
  }
  
  .detail-content {
    padding: 16px;
  }
  
  .patient-info,
  .doctor-info {
    flex-direction: column;
    text-align: center;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-item .value {
    text-align: left;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-btn {
    min-width: auto;
  }
}
</style>
