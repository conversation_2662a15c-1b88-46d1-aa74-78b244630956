<template>
  <div class="stats-card" :class="cardClass">
    <div class="card-icon">
      <span class="icon">{{ icon }}</span>
    </div>
    <div class="card-content">
      <div class="card-title">{{ title }}</div>
      <div class="card-value">{{ formattedValue }}</div>
      <div class="card-change" :class="changeClass">
        <span class="change-icon">{{ changeIcon }}</span>
        <span class="change-text">{{ changeText }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'DoctorStatsCard',
  props: {
    title: {
      type: String,
      required: true
    },
    value: {
      type: [Number, String],
      required: true
    },
    change: {
      type: Number,
      default: 0
    },
    icon: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
    },
    format: {
      type: String,
      default: 'number',
      validator: (value) => ['number', 'currency', 'percentage'].includes(value)
    }
  },
  setup(props) {
    const cardClass = computed(() => {
      return `stats-card--${props.type}`
    })

    const formattedValue = computed(() => {
      if (typeof props.value === 'string') {
        return props.value
      }

      switch (props.format) {
        case 'currency':
          return `¥${new Intl.NumberFormat('zh-CN').format(props.value)}`
        case 'percentage':
          return `${props.value.toFixed(1)}%`
        default:
          return new Intl.NumberFormat('zh-CN').format(props.value)
      }
    })

    const changeClass = computed(() => {
      if (props.change > 0) return 'change--positive'
      if (props.change < 0) return 'change--negative'
      return 'change--neutral'
    })

    const changeIcon = computed(() => {
      if (props.change > 0) return '↗'
      if (props.change < 0) return '↘'
      return '→'
    })

    const changeText = computed(() => {
      if (props.change === 0) return '无变化'
      const sign = props.change > 0 ? '+' : ''
      return `${sign}${props.change.toFixed(1)}%`
    })

    return {
      cardClass,
      formattedValue,
      changeClass,
      changeIcon,
      changeText
    }
  }
}
</script>

<style scoped>
.stats-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  border-left: 4px solid #e5e7eb;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stats-card--primary {
  border-left-color: #3b82f6;
}

.stats-card--success {
  border-left-color: #10b981;
}

.stats-card--warning {
  border-left-color: #f59e0b;
}

.stats-card--danger {
  border-left-color: #ef4444;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  flex-shrink: 0;
}

.stats-card--primary .card-icon {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
}

.stats-card--success .card-icon {
  background: linear-gradient(135deg, #d1fae5, #a7f3d0);
}

.stats-card--warning .card-icon {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
}

.stats-card--danger .card-icon {
  background: linear-gradient(135deg, #fee2e2, #fecaca);
}

.icon {
  font-size: 28px;
}

.card-content {
  flex: 1;
  min-width: 0;
}

.card-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 8px;
  line-height: 1;
}

.card-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.change--positive {
  color: #10b981;
}

.change--negative {
  color: #ef4444;
}

.change--neutral {
  color: #6b7280;
}

.change-icon {
  font-size: 14px;
}

.change-text {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-card {
    padding: 20px;
    gap: 16px;
  }

  .card-icon {
    width: 50px;
    height: 50px;
  }

  .icon {
    font-size: 24px;
  }

  .card-value {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .stats-card {
    padding: 16px;
    gap: 12px;
  }

  .card-icon {
    width: 40px;
    height: 40px;
  }

  .icon {
    font-size: 20px;
  }

  .card-value {
    font-size: 24px;
  }

  .card-title {
    font-size: 12px;
  }
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-card {
  animation: slideInUp 0.5s ease-out;
}

/* 加载状态 */
.stats-card--loading {
  opacity: 0.6;
  pointer-events: none;
}

.stats-card--loading .card-value {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  color: transparent;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 主题变体 */
.stats-card--compact {
  padding: 16px;
  gap: 12px;
}

.stats-card--compact .card-icon {
  width: 40px;
  height: 40px;
}

.stats-card--compact .icon {
  font-size: 20px;
}

.stats-card--compact .card-value {
  font-size: 24px;
}

.stats-card--large {
  padding: 32px;
  gap: 24px;
}

.stats-card--large .card-icon {
  width: 80px;
  height: 80px;
}

.stats-card--large .icon {
  font-size: 36px;
}

.stats-card--large .card-value {
  font-size: 40px;
}

/* 特殊效果 */
.stats-card--glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.stats-card--gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stats-card--gradient .card-title,
.stats-card--gradient .card-value {
  color: white;
}

.stats-card--gradient .card-icon {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}
</style>
