<template>
  <div class="patient-health-chart">
    <!-- 患者信息 -->
    <div class="patient-info">
      <div class="patient-avatar">
        <img v-if="patientInfo.avatarUrl" :src="patientInfo.avatarUrl" :alt="patientInfo.name">
        <div v-else class="avatar-placeholder">{{ getInitials(patientInfo.name) }}</div>
      </div>
      <div class="patient-details">
        <h3>{{ patientInfo.name }}</h3>
        <p>性别: {{ getGenderText(patientInfo.gender) }} | 年龄: {{ patientInfo.age }}岁</p>
        <p>联系电话: {{ patientInfo.phone }}</p>
      </div>
    </div>

    <!-- 健康指标选择 -->
    <div class="metric-selector">
      <div class="metric-tabs">
        <button 
          v-for="metric in availableMetrics" 
          :key="metric.type"
          :class="['metric-tab', { active: selectedMetric === metric.type }]"
          @click="selectMetric(metric.type)"
        >
          <span class="metric-icon">{{ metric.icon }}</span>
          <span class="metric-name">{{ metric.name }}</span>
        </button>
      </div>
    </div>

    <!-- 时间范围选择 -->
    <div class="time-range-selector">
      <div class="range-buttons">
        <button 
          v-for="range in timeRanges" 
          :key="range.value"
          :class="['range-btn', { active: selectedRange === range.value }]"
          @click="selectTimeRange(range.value)"
        >
          {{ range.label }}
        </button>
      </div>
      
      <div v-if="selectedRange === 'custom'" class="custom-range">
        <input 
          type="date" 
          v-model="customRange.start" 
          @change="loadHealthData"
          class="date-input"
        >
        <span>至</span>
        <input 
          type="date" 
          v-model="customRange.end" 
          @change="loadHealthData"
          class="date-input"
        >
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <div class="loading-spinner"></div>
      <p>正在加载健康数据...</p>
    </div>

    <!-- 健康数据图表 -->
    <div v-else class="chart-section">
      <!-- 主图表 -->
      <div class="main-chart">
        <div class="chart-header">
          <h4>{{ getCurrentMetricName() }}趋势</h4>
          <div class="chart-actions">
            <button @click="exportChart" class="export-btn">导出图表</button>
          </div>
        </div>
        <div ref="healthChart" class="chart"></div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-summary">
        <div class="stat-item">
          <span class="stat-label">最新值</span>
          <span class="stat-value">{{ formatMetricValue(latestValue) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">平均值</span>
          <span class="stat-value">{{ formatMetricValue(averageValue) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最高值</span>
          <span class="stat-value">{{ formatMetricValue(maxValue) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最低值</span>
          <span class="stat-value">{{ formatMetricValue(minValue) }}</span>
        </div>
      </div>

      <!-- 健康建议 -->
      <div class="health-advice" v-if="healthAdvice">
        <h4>健康建议</h4>
        <div class="advice-content">
          <div class="advice-level" :class="healthAdvice.level">
            {{ getLevelText(healthAdvice.level) }}
          </div>
          <p>{{ healthAdvice.message }}</p>
          <ul v-if="healthAdvice.suggestions && healthAdvice.suggestions.length">
            <li v-for="suggestion in healthAdvice.suggestions" :key="suggestion">
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 数据详情表格 -->
    <div class="data-table-section">
      <div class="table-header">
        <h4>详细记录</h4>
        <div class="table-actions">
          <button @click="exportData" class="export-btn">导出数据</button>
        </div>
      </div>
      
      <table class="health-data-table">
        <thead>
          <tr>
            <th>记录时间</th>
            <th>{{ getCurrentMetricName() }}</th>
            <th>状态</th>
            <th>备注</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="record in healthData" :key="record.id">
            <td>{{ formatDateTime(record.recordedAt) }}</td>
            <td>{{ formatMetricValue(record.metricValue) }}</td>
            <td>
              <span class="status-badge" :class="getHealthStatus(record.metricValue)">
                {{ getHealthStatusText(record.metricValue) }}
              </span>
            </td>
            <td>{{ record.notes || '-' }}</td>
          </tr>
        </tbody>
      </table>
      
      <!-- 分页 -->
      <div class="pagination" v-if="totalPages > 1">
        <button 
          @click="changePage(currentPage - 1)" 
          :disabled="currentPage <= 1"
          class="page-btn"
        >
          上一页
        </button>
        
        <span class="page-info">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页
        </span>
        
        <button 
          @click="changePage(currentPage + 1)" 
          :disabled="currentPage >= totalPages"
          class="page-btn"
        >
          下一页
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import * as doctorStatsApi from '@/api/doctorStats'

export default {
  name: 'PatientHealthChart',
  props: {
    patientId: {
      type: Number,
      required: true
    },
    dateRange: {
      type: Object,
      default: () => ({
        start: '',
        end: ''
      })
    }
  },
  setup(props) {
    // 响应式数据
    const loading = ref(false)
    const selectedMetric = ref('blood_pressure')
    const selectedRange = ref('month')
    const customRange = reactive({
      start: '',
      end: ''
    })
    
    const patientInfo = reactive({
      name: '',
      gender: '',
      age: 0,
      phone: '',
      avatarUrl: ''
    })
    
    const healthData = ref([])
    const currentPage = ref(1)
    const totalPages = ref(1)
    const pageSize = 10
    
    const healthChart = ref(null)
    let chartInstance = null
    
    const healthAdvice = ref(null)

    // 可用的健康指标
    const availableMetrics = [
      { type: 'blood_pressure', name: '血压', icon: '🩸', unit: 'mmHg' },
      { type: 'blood_sugar', name: '血糖', icon: '🍯', unit: 'mmol/L' },
      { type: 'weight', name: '体重', icon: '⚖️', unit: 'kg' },
      { type: 'heart_rate', name: '心率', icon: '💓', unit: 'bpm' },
      { type: 'temperature', name: '体温', icon: '🌡️', unit: '°C' }
    ]

    // 时间范围选项
    const timeRanges = [
      { value: 'week', label: '最近一周' },
      { value: 'month', label: '最近一月' },
      { value: '3months', label: '最近三月' },
      { value: '6months', label: '最近半年' },
      { value: 'year', label: '最近一年' },
      { value: 'custom', label: '自定义' }
    ]

    // 计算属性
    const latestValue = computed(() => {
      if (healthData.value.length === 0) return 0
      return healthData.value[0].metricValue
    })

    const averageValue = computed(() => {
      if (healthData.value.length === 0) return 0
      const sum = healthData.value.reduce((acc, item) => acc + item.metricValue, 0)
      return sum / healthData.value.length
    })

    const maxValue = computed(() => {
      if (healthData.value.length === 0) return 0
      return Math.max(...healthData.value.map(item => item.metricValue))
    })

    const minValue = computed(() => {
      if (healthData.value.length === 0) return 0
      return Math.min(...healthData.value.map(item => item.metricValue))
    })

    // 方法
    const selectMetric = (metricType) => {
      selectedMetric.value = metricType
      loadHealthData()
    }

    const selectTimeRange = (range) => {
      selectedRange.value = range
      if (range !== 'custom') {
        loadHealthData()
      }
    }

    const loadHealthData = async () => {
      try {
        loading.value = true
        
        const params = {
          metricType: selectedMetric.value,
          ...getDateRangeParams(),
          page: currentPage.value - 1,
          size: pageSize
        }
        
        const response = await doctorStatsApi.getPatientHealthData(props.patientId, params)
        
        if (response.data.code === 200) {
          const data = response.data.data
          healthData.value = data.content || []
          totalPages.value = data.totalPages || 1

          // 加载患者信息
          if (data.patientInfo) {
            Object.assign(patientInfo, data.patientInfo)
          }

          // 加载健康建议
          healthAdvice.value = data.healthAdvice || null

          // 更新图表
          await nextTick()
          updateChart()
        } else {
          console.error('获取患者健康数据失败:', response.data.message)
          alert('获取患者健康数据失败，请稍后重试')
        }
      } catch (error) {
        console.error('加载健康数据失败:', error)
      } finally {
        loading.value = false
      }
    }

    const getDateRangeParams = () => {
      if (selectedRange.value === 'custom') {
        return {
          startDate: customRange.start,
          endDate: customRange.end
        }
      }
      
      const now = new Date()
      const start = new Date()
      
      switch (selectedRange.value) {
        case 'week':
          start.setDate(now.getDate() - 7)
          break
        case 'month':
          start.setMonth(now.getMonth() - 1)
          break
        case '3months':
          start.setMonth(now.getMonth() - 3)
          break
        case '6months':
          start.setMonth(now.getMonth() - 6)
          break
        case 'year':
          start.setFullYear(now.getFullYear() - 1)
          break
      }
      
      return {
        startDate: start.toISOString().split('T')[0],
        endDate: now.toISOString().split('T')[0]
      }
    }

    const updateChart = () => {
      if (!healthChart.value || healthData.value.length === 0) return
      
      if (chartInstance) {
        chartInstance.dispose()
      }
      
      chartInstance = echarts.init(healthChart.value)
      
      const dates = healthData.value.map(item => 
        new Date(item.recordedAt).toLocaleDateString('zh-CN')
      ).reverse()
      
      const values = healthData.value.map(item => item.metricValue).reverse()
      
      const option = {
        title: {
          text: `${getCurrentMetricName()}趋势图`,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            const point = params[0]
            return `${point.name}<br/>${getCurrentMetricName()}: ${formatMetricValue(point.value)}`
          }
        },
        xAxis: {
          type: 'category',
          data: dates,
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          name: getCurrentMetricUnit()
        },
        series: [{
          name: getCurrentMetricName(),
          type: 'line',
          data: values,
          smooth: true,
          itemStyle: {
            color: getMetricColor()
          },
          areaStyle: {
            opacity: 0.3
          }
        }]
      }
      
      chartInstance.setOption(option)
    }

    const changePage = (page) => {
      currentPage.value = page
      loadHealthData()
    }

    // 工具函数
    const getCurrentMetricName = () => {
      const metric = availableMetrics.find(m => m.type === selectedMetric.value)
      return metric ? metric.name : ''
    }

    const getCurrentMetricUnit = () => {
      const metric = availableMetrics.find(m => m.type === selectedMetric.value)
      return metric ? metric.unit : ''
    }

    const getMetricColor = () => {
      const colors = {
        blood_pressure: '#e74c3c',
        blood_sugar: '#f39c12',
        weight: '#3498db',
        heart_rate: '#e91e63',
        temperature: '#9c27b0'
      }
      return colors[selectedMetric.value] || '#3498db'
    }

    const formatMetricValue = (value) => {
      if (!value) return '-'
      return `${value.toFixed(1)} ${getCurrentMetricUnit()}`
    }

    const formatDateTime = (dateTime) => {
      return new Date(dateTime).toLocaleString('zh-CN')
    }

    const getInitials = (name) => {
      if (!name) return '?'
      return name.charAt(0).toUpperCase()
    }

    const getGenderText = (gender) => {
      const genderMap = {
        'MALE': '男',
        'FEMALE': '女',
        'OTHER': '其他'
      }
      return genderMap[gender] || '未知'
    }

    const getHealthStatus = (value) => {
      // 根据不同指标判断健康状态
      // 这里简化处理，实际应该根据医学标准
      if (selectedMetric.value === 'blood_pressure') {
        if (value < 90 || value > 140) return 'abnormal'
        if (value < 100 || value > 130) return 'warning'
        return 'normal'
      }
      // 其他指标的判断逻辑...
      return 'normal'
    }

    const getHealthStatusText = (value) => {
      const status = getHealthStatus(value)
      const statusMap = {
        'normal': '正常',
        'warning': '注意',
        'abnormal': '异常'
      }
      return statusMap[status] || '正常'
    }

    const getLevelText = (level) => {
      const levelMap = {
        'normal': '正常',
        'warning': '注意',
        'danger': '危险'
      }
      return levelMap[level] || '正常'
    }

    const exportChart = () => {
      if (chartInstance) {
        const url = chartInstance.getDataURL({
          type: 'png',
          backgroundColor: '#fff'
        })
        
        const link = document.createElement('a')
        link.href = url
        link.download = `${patientInfo.name}_${getCurrentMetricName()}_趋势图.png`
        link.click()
      }
    }

    const exportData = () => {
      // 导出数据为CSV格式
      const headers = ['记录时间', getCurrentMetricName(), '状态', '备注']
      const csvContent = [
        headers.join(','),
        ...healthData.value.map(record => [
          formatDateTime(record.recordedAt),
          record.metricValue,
          getHealthStatusText(record.metricValue),
          record.notes || ''
        ].join(','))
      ].join('\n')
      
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${patientInfo.name}_${getCurrentMetricName()}_数据.csv`
      link.click()
      URL.revokeObjectURL(url)
    }

    // 监听props变化
    watch(() => props.patientId, () => {
      if (props.patientId) {
        currentPage.value = 1
        loadHealthData()
      }
    })

    watch(() => props.dateRange, (newRange) => {
      if (newRange.start && newRange.end) {
        selectedRange.value = 'custom'
        customRange.start = newRange.start
        customRange.end = newRange.end
        loadHealthData()
      }
    }, { deep: true })

    // 组件挂载
    onMounted(() => {
      if (props.patientId) {
        loadHealthData()
      }
    })

    return {
      loading,
      selectedMetric,
      selectedRange,
      customRange,
      patientInfo,
      healthData,
      currentPage,
      totalPages,
      healthChart,
      healthAdvice,
      availableMetrics,
      timeRanges,
      latestValue,
      averageValue,
      maxValue,
      minValue,
      selectMetric,
      selectTimeRange,
      loadHealthData,
      changePage,
      getCurrentMetricName,
      getCurrentMetricUnit,
      formatMetricValue,
      formatDateTime,
      getInitials,
      getGenderText,
      getHealthStatus,
      getHealthStatusText,
      getLevelText,
      exportChart,
      exportData
    }
  }
}
</script>

<style scoped>
.patient-health-chart {
  padding: 20px;
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.patient-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
}

.patient-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  font-size: 32px;
  font-weight: bold;
  color: #6c757d;
}

.patient-details h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 24px;
}

.patient-details p {
  margin: 4px 0;
  color: #6c757d;
}

.metric-selector {
  margin-bottom: 20px;
}

.metric-tabs {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.metric-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.metric-tab:hover {
  border-color: #4A90E2;
}

.metric-tab.active {
  border-color: #4A90E2;
  background: #4A90E2;
  color: white;
}

.metric-icon {
  font-size: 20px;
}

.time-range-selector {
  margin-bottom: 20px;
}

.range-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.range-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.range-btn:hover {
  border-color: #4A90E2;
}

.range-btn.active {
  background: #4A90E2;
  color: white;
  border-color: #4A90E2;
}

.custom-range {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.loading {
  text-align: center;
  padding: 50px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4A90E2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-section {
  margin-bottom: 30px;
}

.main-chart {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h4 {
  margin: 0;
  color: #2c3e50;
}

.export-btn {
  padding: 6px 12px;
  background: #4A90E2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.export-btn:hover {
  background: #357abd;
}

.chart {
  width: 100%;
  height: 400px;
}

.stats-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-label {
  display: block;
  color: #6c757d;
  font-size: 12px;
  margin-bottom: 5px;
}

.stat-value {
  display: block;
  color: #2c3e50;
  font-size: 18px;
  font-weight: bold;
}

.health-advice {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.health-advice h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.advice-level {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 10px;
}

.advice-level.normal {
  background: #d4edda;
  color: #155724;
}

.advice-level.warning {
  background: #fff3cd;
  color: #856404;
}

.advice-level.danger {
  background: #f8d7da;
  color: #721c24;
}

.data-table-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-header h4 {
  margin: 0;
  color: #2c3e50;
}

.health-data-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.health-data-table th,
.health-data-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.health-data-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.normal {
  background: #d4edda;
  color: #155724;
}

.status-badge.warning {
  background: #fff3cd;
  color: #856404;
}

.status-badge.abnormal {
  background: #f8d7da;
  color: #721c24;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-btn:not(:disabled):hover {
  border-color: #4A90E2;
}

.page-info {
  color: #6c757d;
  font-size: 14px;
}

@media (max-width: 768px) {
  .patient-info {
    flex-direction: column;
    text-align: center;
  }
  
  .metric-tabs {
    justify-content: center;
  }
  
  .range-buttons {
    justify-content: center;
  }
  
  .stats-summary {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .chart-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .table-header {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
