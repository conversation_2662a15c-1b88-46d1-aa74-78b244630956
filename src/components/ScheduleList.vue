<template>
  <div class="schedule-list">
    <!-- 列表头部 -->
    <div class="list-header">
      <div class="header-info">
        <h3>📋 排班列表</h3>
        <span class="schedule-count">共 {{ schedules.length }} 个排班</span>
      </div>
      <div class="header-actions">
        <div class="view-toggle">
          <button
            @click="viewMode = 'list'"
            :class="{ active: viewMode === 'list' }"
            class="toggle-btn"
          >
            📋 列表
          </button>
          <button
            @click="viewMode = 'calendar'"
            :class="{ active: viewMode === 'calendar' }"
            class="toggle-btn"
          >
            📅 日历
          </button>
        </div>
        <button @click="$emit('create')" class="create-btn">
          ➕ 新增排班
        </button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <div class="filter-group">
        <label>时间范围:</label>
        <select v-model="dateFilter" @change="applyFilters" class="filter-select">
          <option value="all">全部</option>
          <option value="today">今天</option>
          <option value="tomorrow">明天</option>
          <option value="week">本周</option>
          <option value="month">本月</option>
          <option value="future">未来</option>
        </select>
      </div>
      <div class="filter-group">
        <label>状态:</label>
        <select v-model="statusFilter" @change="applyFilters" class="filter-select">
          <option value="all">全部</option>
          <option value="available">可预约</option>
          <option value="full">已满</option>
          <option value="past">已过期</option>
        </select>
      </div>
      <div class="filter-group">
        <input
          v-model="searchKeyword"
          @input="applyFilters"
          placeholder="搜索日期或时间..."
          class="search-input"
        />
      </div>
    </div>

    <!-- 列表视图 -->
    <div v-if="viewMode === 'list'" class="list-view">
      <div v-if="filteredSchedules.length === 0" class="empty-state">
        <div class="empty-icon">📅</div>
        <h4>暂无排班数据</h4>
        <p>{{ getEmptyMessage() }}</p>
        <button @click="$emit('create')" class="empty-action-btn">
          立即创建排班
        </button>
      </div>

      <div v-else class="schedule-table">
        <div class="table-header">
          <div class="col-date">日期</div>
          <div class="col-time">时间</div>
          <div class="col-slots">号源</div>
          <div class="col-status">状态</div>
          <div class="col-actions">操作</div>
        </div>

        <div
          v-for="schedule in filteredSchedules"
          :key="schedule.id"
          class="table-row"
          :class="getRowClass(schedule)"
        >
          <div class="col-date">
            <div class="date-main">{{ formatDateDisplay(schedule.scheduleDate) }}</div>
            <div class="date-relative">{{ getRelativeDate(schedule.scheduleDate) }}</div>
          </div>
          
          <div class="col-time">
            <div class="time-range">
              {{ formatTimeDisplay(schedule.startTime) }} - {{ formatTimeDisplay(schedule.endTime) }}
            </div>
            <div class="duration">{{ calculateDuration(schedule.startTime, schedule.endTime) }}</div>
          </div>
          
          <div class="col-slots">
            <div class="slots-info">
              <span class="available">{{ schedule.availableSlots }}</span>
              <span class="separator">/</span>
              <span class="total">{{ schedule.totalSlots }}</span>
            </div>
            <div class="slots-label">可用/总数</div>
          </div>
          
          <div class="col-status">
            <span class="status-badge" :class="getStatusClass(schedule)">
              {{ getStatusText(schedule) }}
            </span>
          </div>
          
          <div class="col-actions">
            <div class="action-buttons">
              <button
                @click="$emit('edit', schedule)"
                class="action-btn edit-btn"
                :disabled="isPastSchedule(schedule)"
                title="编辑排班"
              >
                ✏️
              </button>
              <button
                @click="$emit('copy', schedule)"
                class="action-btn copy-btn"
                title="复制排班"
              >
                📋
              </button>
              <button
                @click="$emit('delete', schedule)"
                class="action-btn delete-btn"
                :disabled="schedule.bookedSlots > 0"
                title="删除排班"
              >
                🗑️
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 日历视图 -->
    <div v-else class="calendar-view">
      <ScheduleCalendar
        :schedules="filteredSchedules"
        @create="$emit('create', $event)"
        @edit="$emit('edit', $event)"
        @delete="$emit('delete', $event)"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import ScheduleCalendar from './ScheduleCalendar.vue'
import {
  formatDate,
  formatTimeDisplay,
  getDateDisplayName,
  getRelativeDateDescription,
  isToday,
  isTomorrow,
  isPastDate,
  isFutureDate,
  getWeekRange,
  getMonthRange,
  compareTime
} from '@/utils/dateUtils'

// Props & Emits
const props = defineProps({
  schedules: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['create', 'edit', 'copy', 'delete', 'refresh'])

// 响应式数据
const viewMode = ref('list')
const dateFilter = ref('all')
const statusFilter = ref('all')
const searchKeyword = ref('')

// 计算属性
const filteredSchedules = computed(() => {
  let filtered = [...props.schedules]

  // 日期筛选
  if (dateFilter.value !== 'all') {
    const today = formatDate(new Date())
    const tomorrow = formatDate(new Date(Date.now() + 24 * 60 * 60 * 1000))
    const weekRange = getWeekRange()
    const monthRange = getMonthRange()

    filtered = filtered.filter(schedule => {
      const scheduleDate = schedule.scheduleDate
      
      switch (dateFilter.value) {
        case 'today':
          return scheduleDate === today
        case 'tomorrow':
          return scheduleDate === tomorrow
        case 'week':
          return scheduleDate >= weekRange.startDate && scheduleDate <= weekRange.endDate
        case 'month':
          return scheduleDate >= monthRange.startDate && scheduleDate <= monthRange.endDate
        case 'future':
          return scheduleDate > today
        default:
          return true
      }
    })
  }

  // 状态筛选
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(schedule => {
      switch (statusFilter.value) {
        case 'available':
          return schedule.availableSlots > 0 && !isPastSchedule(schedule)
        case 'full':
          return schedule.availableSlots === 0 && !isPastSchedule(schedule)
        case 'past':
          return isPastSchedule(schedule)
        default:
          return true
      }
    })
  }

  // 关键词搜索
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.trim().toLowerCase()
    filtered = filtered.filter(schedule => {
      return schedule.scheduleDate.includes(keyword) ||
             schedule.startTime.includes(keyword) ||
             schedule.endTime.includes(keyword) ||
             getDateDisplayName(schedule.scheduleDate).toLowerCase().includes(keyword)
    })
  }

  // 按日期和时间排序
  return filtered.sort((a, b) => {
    const dateCompare = a.scheduleDate.localeCompare(b.scheduleDate)
    if (dateCompare !== 0) return dateCompare
    return compareTime(a.startTime, b.startTime)
  })
})

// 方法
const formatDateDisplay = (date) => {
  const d = new Date(date)
  const month = d.getMonth() + 1
  const day = d.getDate()
  return `${month}月${day}日`
}

const getRelativeDate = (date) => {
  return getRelativeDateDescription(date)
}

const calculateDuration = (startTime, endTime) => {
  const start = new Date(`2000-01-01 ${startTime}`)
  const end = new Date(`2000-01-01 ${endTime}`)
  
  if (end <= start) return ''
  
  const diffMs = end.getTime() - start.getTime()
  const hours = Math.floor(diffMs / (1000 * 60 * 60))
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours === 0) {
    return `${minutes}分钟`
  } else if (minutes === 0) {
    return `${hours}小时`
  } else {
    return `${hours}h${minutes}m`
  }
}

const isPastSchedule = (schedule) => {
  return isPastDate(schedule.scheduleDate)
}

const getRowClass = (schedule) => {
  const classes = []
  
  if (isPastSchedule(schedule)) {
    classes.push('past')
  } else if (isToday(schedule.scheduleDate)) {
    classes.push('today')
  } else if (isTomorrow(schedule.scheduleDate)) {
    classes.push('tomorrow')
  }
  
  if (schedule.availableSlots === 0) {
    classes.push('full')
  }
  
  return classes
}

const getStatusClass = (schedule) => {
  if (isPastSchedule(schedule)) {
    return 'status-past'
  } else if (schedule.availableSlots === 0) {
    return 'status-full'
  } else if (schedule.availableSlots <= schedule.totalSlots * 0.2) {
    return 'status-low'
  } else {
    return 'status-available'
  }
}

const getStatusText = (schedule) => {
  if (isPastSchedule(schedule)) {
    return '已过期'
  } else if (schedule.availableSlots === 0) {
    return '已满'
  } else if (schedule.availableSlots <= schedule.totalSlots * 0.2) {
    return '紧张'
  } else {
    return '可预约'
  }
}

const getEmptyMessage = () => {
  if (dateFilter.value !== 'all' || statusFilter.value !== 'all' || searchKeyword.value.trim()) {
    return '没有找到符合条件的排班，请调整筛选条件'
  }
  return '您还没有创建任何排班，点击下方按钮开始创建'
}

const applyFilters = () => {
  // 筛选逻辑在计算属性中处理
}
</script>

<style scoped>
.schedule-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.header-info h3 {
  margin: 0 0 4px 0;
  color: #1e40af;
  font-size: 18px;
  font-weight: 600;
}

.schedule-count {
  color: #6b7280;
  font-size: 14px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.view-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 2px;
}

.toggle-btn {
  padding: 6px 12px;
  border: none;
  background: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.toggle-btn.active {
  background: white;
  color: #3b82f6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.create-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.create-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.filters {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 24px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  white-space: nowrap;
}

.filter-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.search-input {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  width: 200px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h4 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 18px;
}

.empty-state p {
  margin: 0 0 24px 0;
  font-size: 14px;
}

.empty-action-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.empty-action-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.schedule-table {
  display: flex;
  flex-direction: column;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 100px 100px 120px;
  gap: 16px;
  padding: 16px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 100px 100px 120px;
  gap: 16px;
  padding: 16px 24px;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
}

.table-row:hover {
  background: #f8fafc;
}

.table-row.today {
  background: #fef3c7;
  border-left: 4px solid #f59e0b;
}

.table-row.tomorrow {
  background: #dbeafe;
  border-left: 4px solid #3b82f6;
}

.table-row.past {
  opacity: 0.6;
  background: #f9fafb;
}

.table-row.full {
  background: #fee2e2;
}

.date-main {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.date-relative {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.time-range {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.duration {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.slots-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
}

.slots-info .available {
  color: #059669;
  font-size: 16px;
}

.slots-info .separator {
  color: #6b7280;
}

.slots-info .total {
  color: #374151;
  font-size: 14px;
}

.slots-label {
  font-size: 11px;
  color: #6b7280;
  margin-top: 2px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.status-available {
  background: #dcfce7;
  color: #166534;
}

.status-low {
  background: #fef3c7;
  color: #92400e;
}

.status-full {
  background: #fee2e2;
  color: #991b1b;
}

.status-past {
  background: #f3f4f6;
  color: #6b7280;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.edit-btn {
  background: #dbeafe;
  color: #1d4ed8;
}

.edit-btn:hover:not(:disabled) {
  background: #3b82f6;
  color: white;
}

.copy-btn {
  background: #f0fdf4;
  color: #166534;
}

.copy-btn:hover {
  background: #22c55e;
  color: white;
}

.delete-btn {
  background: #fee2e2;
  color: #dc2626;
}

.delete-btn:hover:not(:disabled) {
  background: #ef4444;
  color: white;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: space-between;
  }
  
  .filters {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .filter-group {
    justify-content: space-between;
  }
  
  .search-input {
    width: 100%;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .table-header {
    display: none;
  }
  
  .table-row {
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 8px;
  }
}
</style>
