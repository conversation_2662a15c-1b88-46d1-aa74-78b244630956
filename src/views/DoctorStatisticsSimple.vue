<template>
  <div class="statistics-simple">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>统计分析（简化版）</h1>
        <p class="page-description">查看您的工作数据统计和分析报告</p>
      </div>
      <div class="header-controls">
        <!-- 时间范围选择器 -->
        <select v-model="timeRange" @change="handleTimeRangeChange" class="time-range-selector">
          <option value="week">本周</option>
          <option value="month">本月</option>
          <option value="quarter">近三个月</option>
        </select>
        
        <!-- 刷新按钮 -->
        <button @click="loadAllData" class="refresh-btn" :disabled="loading">
          <span v-if="loading">加载中...</span>
          <span v-else>🔄 刷新数据</span>
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载统计数据...</p>
    </div>

    <!-- 主要内容 -->
    <div v-else class="statistics-content">
      <!-- 第一部分：核心指标概览 -->
      <section class="kpi-section">
        <h2 class="section-title">核心指标概览</h2>
        <div class="kpi-grid">
          <!-- 总服务人次 -->
          <div class="kpi-card">
            <div class="kpi-header">
              <h3>总服务人次</h3>
              <div class="kpi-icon">👥</div>
            </div>
            <div class="kpi-content">
              <div class="kpi-value">{{ kpiData.totalServices || 0 }}</div>
              <div class="kpi-change" :class="getChangeClass(kpiData.totalServicesChange)">
                vs 上一个周期{{ formatChange(kpiData.totalServicesChange) }}
              </div>
            </div>
          </div>

          <!-- 预约完成率 -->
          <div class="kpi-card">
            <div class="kpi-header">
              <h3>预约完成率</h3>
              <div class="kpi-icon">✅</div>
            </div>
            <div class="kpi-content">
              <div class="kpi-value">{{ formatPercentage(kpiData.completionRate) }}</div>
              <div class="kpi-subtitle">
                {{ kpiData.completedAppointments || 0 }} / {{ kpiData.totalAppointments || 0 }} (完成/总数)
              </div>
            </div>
          </div>

          <!-- 新增患者数量 -->
          <div class="kpi-card">
            <div class="kpi-header">
              <h3>新增患者数量</h3>
              <div class="kpi-icon">🆕</div>
            </div>
            <div class="kpi-content">
              <div class="kpi-value">{{ kpiData.newPatients || 0 }}</div>
              <div class="kpi-change" :class="getChangeClass(kpiData.newPatientsChange)">
                vs 上一个周期{{ formatChange(kpiData.newPatientsChange) }}
              </div>
            </div>
          </div>

          <!-- 发布健康指南 -->
          <div class="kpi-card">
            <div class="kpi-header">
              <h3>发布健康指南</h3>
              <div class="kpi-icon">📝</div>
            </div>
            <div class="kpi-content">
              <div class="kpi-value">{{ kpiData.publishedGuides || 0 }}</div>
              <div class="kpi-subtitle">
                {{ getTimeRangeText() }}共发布{{ kpiData.publishedGuides || 0 }}篇
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 第二部分：数据表格展示 -->
      <section class="data-section">
        <h2 class="section-title">详细数据分析</h2>
        
        <!-- 服务趋势数据 -->
        <div class="data-card">
          <h3>服务量趋势数据</h3>
          <div class="data-table">
            <table>
              <thead>
                <tr>
                  <th>日期</th>
                  <th>预约服务</th>
                  <th>在线问诊</th>
                  <th>总计</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(date, index) in serviceTrendData.dates" :key="date">
                  <td>{{ date }}</td>
                  <td>{{ serviceTrendData.appointmentCounts?.[index] || 0 }}</td>
                  <td>{{ serviceTrendData.consultationCounts?.[index] || 0 }}</td>
                  <td>{{ (serviceTrendData.appointmentCounts?.[index] || 0) + (serviceTrendData.consultationCounts?.[index] || 0) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 预约状态分配 -->
        <div class="data-card">
          <h3>预约状态分配</h3>
          <div class="status-stats">
            <div class="status-item completed">
              <div class="status-label">已完成</div>
              <div class="status-value">{{ appointmentStatusData.completed || 0 }}</div>
              <div class="status-percent">{{ getStatusPercent('completed') }}</div>
            </div>
            <div class="status-item booked">
              <div class="status-label">已预约</div>
              <div class="status-value">{{ appointmentStatusData.booked || 0 }}</div>
              <div class="status-percent">{{ getStatusPercent('booked') }}</div>
            </div>
            <div class="status-item cancelled">
              <div class="status-label">已取消</div>
              <div class="status-value">{{ appointmentStatusData.cancelled || 0 }}</div>
              <div class="status-percent">{{ getStatusPercent('cancelled') }}</div>
            </div>
          </div>
        </div>

        <!-- 高频患者排行 -->
        <div class="data-card">
          <h3>高频服务患者 TOP 5</h3>
          <div class="patients-list">
            <div v-for="(patient, index) in topPatientsData.patients" :key="index" class="patient-item">
              <div class="patient-rank">{{ index + 1 }}</div>
              <div class="patient-name">{{ patient.name }}</div>
              <div class="patient-count">{{ patient.serviceCount }}次</div>
            </div>
          </div>
        </div>

        <!-- 预约时间段分析 -->
        <div class="data-card">
          <h3>预约时间段分析</h3>
          <div class="time-slots">
            <div v-for="(slot, index) in scheduleHotnessData.timeSlots" :key="slot" class="time-slot">
              <div class="slot-time">{{ slot }}</div>
              <div class="slot-count">{{ scheduleHotnessData.counts?.[index] || 0 }}人</div>
              <div class="slot-bar">
                <div class="slot-fill" :style="{ width: getSlotPercent(index) }"></div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-message">
      <p>{{ error }}</p>
      <button @click="loadAllData" class="retry-btn">重试</button>
    </div>

    <!-- 导航按钮 -->
    <div class="navigation">
      <button @click="goToFullVersion" class="nav-btn">
        查看完整版（图表）
      </button>
      <button @click="goToTest" class="nav-btn secondary">
        API接口测试
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import {
  getDoctorKpi,
  getServiceTrend,
  getAppointmentStatus,
  getTopPatients,
  getScheduleHotness
} from '@/api/doctorStatistics'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const error = ref('')
const timeRange = ref('month') // 默认本月

// 数据
const kpiData = ref({})
const serviceTrendData = ref({})
const appointmentStatusData = ref({})
const topPatientsData = ref({})
const scheduleHotnessData = ref({})

// 计算属性
const totalAppointments = computed(() => {
  const data = appointmentStatusData.value
  return (data.completed || 0) + (data.booked || 0) + (data.cancelled || 0)
})

const maxSlotCount = computed(() => {
  const counts = scheduleHotnessData.value.counts || []
  return Math.max(...counts, 1)
})

// 方法
const handleTimeRangeChange = () => {
  loadAllData()
}

const getTimeRangeText = () => {
  const textMap = {
    week: '本周',
    month: '本月',
    quarter: '近三个月'
  }
  return textMap[timeRange.value] || '本月'
}

const formatChange = (change) => {
  if (!change) return ''
  return change > 0 ? `+${change}` : `${change}`
}

const getChangeClass = (change) => {
  if (!change) return ''
  return change > 0 ? 'positive' : 'negative'
}

const formatPercentage = (value) => {
  if (value === null || value === undefined) return '0%'
  return `${(value * 100).toFixed(1)}%`
}

const getStatusPercent = (status) => {
  const total = totalAppointments.value
  if (total === 0) return '0%'
  const value = appointmentStatusData.value[status] || 0
  return `${((value / total) * 100).toFixed(1)}%`
}

const getSlotPercent = (index) => {
  const counts = scheduleHotnessData.value.counts || []
  const count = counts[index] || 0
  const max = maxSlotCount.value
  return `${(count / max) * 100}%`
}

// 加载所有数据
const loadAllData = async () => {
  loading.value = true
  error.value = ''

  try {
    const params = { range: timeRange.value }

    // 并行加载所有数据
    const [kpiResponse, trendResponse, statusResponse, patientsResponse, hotnessResponse] = await Promise.all([
      getDoctorKpi(params),
      getServiceTrend(params),
      getAppointmentStatus(params),
      getTopPatients(params),
      getScheduleHotness(params)
    ])

    // 处理所有数据
    if (kpiResponse.data.code === 200) {
      kpiData.value = kpiResponse.data.data
    }

    if (trendResponse.data.code === 200) {
      serviceTrendData.value = trendResponse.data.data
    }

    if (statusResponse.data.code === 200) {
      appointmentStatusData.value = statusResponse.data.data
    }

    if (patientsResponse.data.code === 200) {
      topPatientsData.value = patientsResponse.data.data
    }

    if (hotnessResponse.data.code === 200) {
      scheduleHotnessData.value = hotnessResponse.data.data
    }

  } catch (err) {
    console.error('加载统计数据失败:', err)
    error.value = '加载数据失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

const goToFullVersion = () => {
  router.push('/doctor/statistics')
}

const goToTest = () => {
  router.push('/doctor/statistics/test')
}

// 初始化
loadAllData()
</script>

<style scoped>
.statistics-simple {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.page-description {
  color: #64748b;
  margin: 0;
  font-size: 16px;
}

.header-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.time-range-selector {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  min-width: 120px;
}

.refresh-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.refresh-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 内容区域 */
.statistics-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 20px 0;
}

/* KPI指标区域 */
.kpi-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.kpi-card {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.kpi-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.kpi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.kpi-header h3 {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  margin: 0;
}

.kpi-icon {
  font-size: 24px;
}

.kpi-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.kpi-value {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
}

.kpi-change {
  font-size: 12px;
  font-weight: 500;
}

.kpi-change.positive {
  color: #10b981;
}

.kpi-change.negative {
  color: #ef4444;
}

.kpi-subtitle {
  font-size: 12px;
  color: #64748b;
}

/* 数据展示区域 */
.data-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-card {
  margin-bottom: 32px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.data-card h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
  font-size: 16px;
  font-weight: 600;
}

/* 数据表格 */
.data-table {
  overflow-x: auto;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 6px;
  overflow: hidden;
}

.data-table th,
.data-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.data-table th {
  background: #f1f5f9;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.data-table td {
  color: #6b7280;
  font-size: 14px;
}

.data-table tr:hover {
  background: #f9fafb;
}

/* 状态统计 */
.status-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item.completed {
  border-left-color: #10b981;
}

.status-item.booked {
  border-left-color: #3b82f6;
}

.status-item.cancelled {
  border-left-color: #ef4444;
}

.status-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.status-value {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
}

.status-percent {
  font-size: 12px;
  color: #9ca3af;
}

/* 患者列表 */
.patients-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.patient-item {
  display: flex;
  align-items: center;
  gap: 16px;
  background: white;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.patient-rank {
  width: 32px;
  height: 32px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 14px;
}

.patient-name {
  flex: 1;
  font-weight: 500;
  color: #1e293b;
}

.patient-count {
  font-weight: 600;
  color: #3b82f6;
}

/* 时间段分析 */
.time-slots {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.time-slot {
  display: flex;
  align-items: center;
  gap: 16px;
  background: white;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.slot-time {
  min-width: 80px;
  font-weight: 500;
  color: #1e293b;
}

.slot-count {
  min-width: 60px;
  font-weight: 600;
  color: #10b981;
}

.slot-bar {
  flex: 1;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.slot-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  transition: width 0.3s ease;
}

/* 错误提示 */
.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

.retry-btn {
  margin-top: 12px;
  padding: 8px 16px;
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

/* 导航按钮 */
.navigation {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 32px;
}

.nav-btn {
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-btn.secondary {
  background: #6b7280;
}

.nav-btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-simple {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-controls {
    justify-content: space-between;
  }

  .kpi-grid {
    grid-template-columns: 1fr;
  }

  .status-stats {
    grid-template-columns: 1fr;
  }

  .navigation {
    flex-direction: column;
  }
}
</style>
