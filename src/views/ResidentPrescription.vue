<template>
  <div class="resident-prescription">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>我的处方</h1>
      <p class="page-description">查看医生为您开具的电子处方</p>
    </div>

    <!-- 健康档案选择 -->
    <div class="profile-selector">
      <div class="selector-header">
        <h2>选择健康档案</h2>
        <p class="selector-desc">选择要查看处方的健康档案</p>
      </div>
      
      <div class="profile-cards">
        <div 
          v-for="profile in healthProfiles" 
          :key="profile.id"
          class="profile-card"
          :class="{ active: selectedProfileId === profile.id }"
          @click="selectProfile(profile.id)"
        >
          <div class="profile-avatar">{{ profile.profile_owner_name?.charAt(0) || 'P' }}</div>
          <div class="profile-info">
            <div class="profile-name">{{ profile.profile_owner_name }}</div>
            <div class="profile-details">
              <span v-if="profile.gender">{{ profile.gender === 'MALE' ? '男' : profile.gender === 'FEMALE' ? '女' : '其他' }}</span>
              <span v-if="profile.birth_date">{{ calculateAge(profile.birth_date) }}</span>
            </div>
          </div>
          <div class="prescription-count" v-if="profilePrescriptionCounts[profile.id]">
            {{ profilePrescriptionCounts[profile.id] }} 张处方
          </div>
        </div>
      </div>
    </div>

    <!-- 处方列表 -->
    <div v-if="selectedProfileId" class="prescription-list">
      <div class="list-header">
        <h2>处方记录</h2>
        <div class="selected-profile">
          <span class="profile-label">当前档案：</span>
          <span class="profile-name">{{ selectedProfile?.profile_owner_name }}</span>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在加载处方记录...</p>
      </div>

      <!-- 处方列表内容 -->
      <div v-else-if="prescriptions.length > 0" class="prescription-cards">
        <div 
          v-for="prescription in prescriptions" 
          :key="prescription.id" 
          class="prescription-card"
          @click="viewPrescription(prescription)"
        >
          <div class="card-header">
            <div class="doctor-info">
              <div class="doctor-avatar">{{ prescription.doctorName?.charAt(0) || 'D' }}</div>
              <div class="doctor-details">
                <div class="doctor-name">{{ prescription.doctorName }}</div>
                <div class="doctor-dept">{{ prescription.departmentName }}</div>
              </div>
            </div>
            <div class="prescription-date">{{ formatDate(prescription.createdAt) }}</div>
          </div>
          
          <div class="card-content">
            <div class="diagnosis">
              <strong>诊断：</strong>{{ prescription.diagnosis }}
            </div>
            <div class="medication-summary">
              <span class="medication-count">{{ prescription.medications?.length || 0 }} 种药品</span>
              <span class="view-detail">点击查看详情</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">📋</div>
        <h3>暂无处方记录</h3>
        <p>该健康档案还没有医生开具的处方</p>
      </div>
    </div>

    <!-- 未选择档案提示 -->
    <div v-else class="no-selection">
      <div class="no-selection-icon">👆</div>
      <h3>请选择健康档案</h3>
      <p>选择一个健康档案来查看对应的处方记录</p>
    </div>

    <!-- 处方详情对话框 -->
    <div v-if="showDetailDialog" class="dialog-overlay" @click="closeDetailDialog">
      <div class="detail-dialog" @click.stop>
        <div class="dialog-header">
          <h3>处方详情</h3>
          <button @click="closeDetailDialog" class="close-btn">×</button>
        </div>
        
        <div class="dialog-body" v-if="currentPrescription">
          <!-- 医生信息 -->
          <div class="detail-section">
            <h4>开具医生</h4>
            <div class="doctor-info-detail">
              <div class="doctor-avatar-large">{{ currentPrescription.doctorName?.charAt(0) || 'D' }}</div>
              <div class="doctor-details">
                <div class="doctor-name">{{ currentPrescription.doctorName }}</div>
                <div class="doctor-dept">{{ currentPrescription.departmentName }}</div>
                <div class="prescription-time">开具时间：{{ formatDateTime(currentPrescription.createdAt) }}</div>
              </div>
            </div>
          </div>

          <!-- 患者信息 -->
          <div class="detail-section">
            <h4>患者信息</h4>
            <div class="patient-info-detail">
              <div class="info-item">
                <span class="info-label">姓名：</span>
                <span class="info-value">{{ currentPrescription.patientName }}</span>
              </div>
            </div>
          </div>

          <!-- 诊断信息 -->
          <div class="detail-section">
            <h4>临床诊断</h4>
            <div class="diagnosis-text">{{ currentPrescription.diagnosis }}</div>
          </div>

          <!-- 药品信息 -->
          <div class="detail-section">
            <h4>处方药品</h4>
            <div class="medications-list">
              <div 
                v-for="(medication, index) in currentPrescription.medications" 
                :key="index"
                class="medication-detail"
              >
                <div class="medication-header">
                  <div class="medication-name">{{ index + 1 }}. {{ medication.name }}</div>
                  <div class="medication-spec">{{ medication.specification }}</div>
                </div>
                <div class="medication-info">
                  <div class="info-grid">
                    <div class="info-item">
                      <span class="info-label">数量：</span>
                      <span class="info-value">{{ medication.quantity }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">频次：</span>
                      <span class="info-value">{{ medication.frequency }}</span>
                    </div>
                  </div>
                  <div class="info-item full-width">
                    <span class="info-label">用法：</span>
                    <span class="info-value">{{ medication.dosage }}</span>
                  </div>
                  <div v-if="medication.notes" class="info-item full-width">
                    <span class="info-label">备注：</span>
                    <span class="info-value notes">{{ medication.notes }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 用药提醒 -->
          <div class="detail-section">
            <div class="medication-reminder">
              <div class="reminder-icon">⚠️</div>
              <div class="reminder-text">
                <strong>用药提醒：</strong>
                请严格按照医生开具的处方用药，如有疑问请及时咨询医生。服药期间如出现不适，请立即停药并就医。
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import { getPatientPrescriptions } from '@/api/prescription'
import { getHealthProfiles } from '@/api/health'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const healthProfiles = ref([])
const prescriptions = ref([])
const selectedProfileId = ref(null)
const profilePrescriptionCounts = ref({})

// 处方详情对话框
const showDetailDialog = ref(false)
const currentPrescription = ref(null)

// 计算属性
const selectedProfile = computed(() => {
  return healthProfiles.value.find(p => p.id === selectedProfileId.value)
})

// 方法
const loadHealthProfiles = async () => {
  try {
    const response = await getHealthProfiles({ page: 1, size: 100 })
    if (response.data.code === 200) {
      healthProfiles.value = response.data.data.content || []
      // 为每个档案加载处方数量
      for (const profile of healthProfiles.value) {
        await loadPrescriptionCount(profile.id)
      }
    }
  } catch (error) {
    console.error('加载健康档案失败:', error)
  }
}

const loadPrescriptionCount = async (profileId) => {
  try {
    const response = await getPatientPrescriptions(profileId, { page: 1, size: 1 })
    if (response.data.code === 200) {
      profilePrescriptionCounts.value[profileId] = response.data.data.totalElements || 0
    }
  } catch (error) {
    console.error('加载处方数量失败:', error)
    profilePrescriptionCounts.value[profileId] = 0
  }
}

const selectProfile = (profileId) => {
  selectedProfileId.value = profileId
  loadPrescriptions()
}

const loadPrescriptions = async () => {
  if (!selectedProfileId.value) return
  
  loading.value = true
  try {
    const response = await getPatientPrescriptions(selectedProfileId.value, { page: 1, size: 50 })
    if (response.data.code === 200) {
      prescriptions.value = response.data.data.content || []
    }
  } catch (error) {
    console.error('加载处方列表失败:', error)
  } finally {
    loading.value = false
  }
}

const viewPrescription = (prescription) => {
  currentPrescription.value = prescription
  showDetailDialog.value = true
}

const closeDetailDialog = () => {
  showDetailDialog.value = false
  currentPrescription.value = null
}

const calculateAge = (birthDate) => {
  if (!birthDate) return ''
  const today = new Date()
  const birth = new Date(birthDate)
  const age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    return (age - 1) + '岁'
  }
  return age + '岁'
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  // 检查用户权限
  if (!userStore.isResident) {
    router.push('/login')
    return
  }
  
  loadHealthProfiles()
})
</script>

<style scoped>
.resident-prescription {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 32px;
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.page-description {
  color: #64748b;
  font-size: 16px;
  margin: 0;
}

/* 健康档案选择器 */
.profile-selector {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selector-header {
  text-align: center;
  margin-bottom: 24px;
}

.selector-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.selector-desc {
  color: #64748b;
  font-size: 16px;
  margin: 0;
}

.profile-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.profile-card {
  display: flex;
  align-items: center;
  gap: 16px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.profile-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.profile-card.active {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.profile-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.profile-details {
  font-size: 14px;
  color: #64748b;
}

.profile-details span {
  margin-right: 12px;
}

.prescription-count {
  font-size: 12px;
  color: #10b981;
  font-weight: 600;
  background: #dcfce7;
  padding: 4px 8px;
  border-radius: 12px;
}

/* 列表头部 */
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.list-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.selected-profile {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f0f9ff;
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid #bae6fd;
}

.profile-label {
  font-size: 14px;
  color: #0369a1;
  font-weight: 500;
}

.selected-profile .profile-name {
  font-size: 14px;
  color: #0c4a6e;
  font-weight: 600;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 处方卡片 */
.prescription-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.prescription-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.prescription-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #10b981;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.doctor-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.doctor-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
}

.doctor-details {
  flex: 1;
}

.doctor-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.doctor-dept {
  font-size: 14px;
  color: #64748b;
}

.prescription-date {
  font-size: 12px;
  color: #10b981;
  font-weight: 600;
  background: #dcfce7;
  padding: 4px 8px;
  border-radius: 12px;
}

.card-content {
  border-top: 1px solid #f1f5f9;
  padding-top: 16px;
}

.diagnosis {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.medication-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.medication-count {
  font-size: 12px;
  color: #10b981;
  font-weight: 600;
}

.view-detail {
  font-size: 12px;
  color: #64748b;
  font-style: italic;
}

/* 空状态和未选择状态 */
.empty-state,
.no-selection {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon,
.no-selection-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3,
.no-selection h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.empty-state p,
.no-selection p {
  color: #64748b;
  margin: 0;
}

/* 对话框 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.detail-dialog {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 700px;
  max-height: 85vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #f1f5f9;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
}

.dialog-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #0c4a6e;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 50%;
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #374151;
}

.dialog-body {
  padding: 32px;
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 32px;
}

.detail-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #f1f5f9;
}

/* 医生信息详情 */
.doctor-info-detail {
  display: flex;
  align-items: center;
  gap: 16px;
  background: #f0f9ff;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #bae6fd;
}

.doctor-avatar-large {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 24px;
}

.doctor-info-detail .doctor-details {
  flex: 1;
}

.doctor-info-detail .doctor-name {
  font-size: 18px;
  font-weight: 600;
  color: #0c4a6e;
  margin-bottom: 4px;
}

.doctor-info-detail .doctor-dept {
  font-size: 14px;
  color: #0369a1;
  margin-bottom: 4px;
}

.prescription-time {
  font-size: 12px;
  color: #64748b;
}

/* 患者信息详情 */
.patient-info-detail {
  background: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item.full-width {
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 12px;
}

.info-label {
  font-weight: 600;
  color: #64748b;
  margin-right: 8px;
  min-width: 60px;
}

.info-value {
  color: #1e293b;
  flex: 1;
}

.info-value.notes {
  background: #fef3c7;
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid #f59e0b;
  margin-top: 4px;
  font-style: italic;
}

/* 诊断文本 */
.diagnosis-text {
  background: #f0fdf4;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #10b981;
  font-size: 15px;
  line-height: 1.6;
  color: #374151;
}

/* 药品列表 */
.medications-list {
  space-y: 16px;
}

.medication-detail {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
}

.medication-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.medication-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.medication-spec {
  font-size: 14px;
  color: #10b981;
  font-weight: 600;
  background: #dcfce7;
  padding: 4px 8px;
  border-radius: 12px;
}

.medication-info {
  font-size: 14px;
  color: #64748b;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 8px;
}

/* 用药提醒 */
.medication-reminder {
  display: flex;
  gap: 12px;
  background: #fef3c7;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid #fbbf24;
}

.reminder-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.reminder-text {
  font-size: 14px;
  line-height: 1.5;
  color: #92400e;
}

.reminder-text strong {
  color: #78350f;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resident-prescription {
    padding: 16px;
  }

  .profile-cards {
    grid-template-columns: 1fr;
  }

  .prescription-cards {
    grid-template-columns: 1fr;
  }

  .list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .doctor-info-detail {
    flex-direction: column;
    text-align: center;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .medication-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
