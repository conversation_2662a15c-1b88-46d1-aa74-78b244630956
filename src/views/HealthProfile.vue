<template>
  <div class="health-profile-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>健康档案</h1>
        <p class="page-description">管理您和家庭成员的健康档案信息</p>
      </div>
      <div class="header-actions">
        <button @click="showCreateForm" class="btn-primary">
          <span class="btn-icon">➕</span>
          创建档案
        </button>
      </div>
    </div>

    <!-- 档案列表 -->
    <div class="profiles-section" v-if="!showForm">
      <div class="profiles-grid" v-if="profiles.length > 0">
        <div
          v-for="profile in profiles"
          :key="profile.id"
          class="profile-card"
          @click="viewProfile(profile)"
        >
          <div class="profile-avatar">
            <img v-if="profile.avatarUrl" :src="profile.avatarUrl" :alt="profile.profileOwnerName" />
            <div v-else class="avatar-placeholder">
              {{ profile.profileOwnerName?.charAt(0) || '?' }}
            </div>
          </div>
          <div class="profile-info">
            <h3 class="profile-name">{{ profile.profileOwnerName }}</h3>
            <div class="profile-details">
              <span class="profile-gender">{{ getGenderText(profile.gender) }}</span>
              <span class="profile-age">{{ profile.age }}岁</span>
            </div>
            <div class="profile-id">身份证：{{ maskIdCard(profile.idCard) }}</div>
          </div>
          <div class="profile-actions" @click.stop>
            <button @click="editProfile(profile)" class="action-btn edit">
              ✏️
            </button>
            <button @click="deleteProfile(profile)" class="action-btn delete">
              🗑️
            </button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!loading && !loadError" class="empty-state">
        <div class="empty-icon">📋</div>
        <h3>还没有健康档案</h3>
        <p>创建您的第一个健康档案，开始管理健康信息</p>
        <button @click="showCreateForm" class="btn-primary">
          创建档案
        </button>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="loadError" class="error-state">
        <div class="error-icon">⚠️</div>
        <h3>加载失败</h3>
        <p>{{ loadError }}</p>
        <div class="error-actions">
          <button @click="retryLoad" class="btn-primary">
            重试
          </button>
          <button @click="showCreateForm" class="btn-secondary">
            创建新档案
          </button>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > pagination.size" class="pagination">
        <button
          @click="changePage(pagination.current - 1)"
          :disabled="pagination.current <= 1"
          class="page-btn"
        >
          上一页
        </button>
        <span class="page-info">
          第 {{ pagination.current }} 页，共 {{ Math.ceil(pagination.total / pagination.size) }} 页
        </span>
        <button
          @click="changePage(pagination.current + 1)"
          :disabled="pagination.current >= Math.ceil(pagination.total / pagination.size)"
          class="page-btn"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 表单弹窗 -->
    <div v-if="showForm" class="form-modal">
      <div class="modal-overlay" @click="hideForm"></div>
      <div class="modal-content">
        <ProfileForm
          :profile="selectedProfile"
          :is-edit="isEditMode"
          @success="handleFormSuccess"
          @cancel="hideForm"
        />
      </div>
    </div>

    <!-- 详情弹窗 -->
    <div v-if="showDetail" class="detail-modal">
      <div class="modal-overlay" @click="hideDetail"></div>
      <div class="modal-content">
        <div class="profile-detail">
          <div class="detail-header">
            <h3>档案详情</h3>
            <button @click="hideDetail" class="close-btn">✕</button>
          </div>
          <div class="detail-content" v-if="selectedProfile">
            <div class="detail-avatar">
              <img v-if="selectedProfile.avatarUrl" :src="selectedProfile.avatarUrl" :alt="selectedProfile.profileOwnerName" />
              <div v-else class="avatar-placeholder large">
                {{ selectedProfile.profileOwnerName?.charAt(0) || '?' }}
              </div>
            </div>
            <div class="detail-info">
              <div class="info-item">
                <label>姓名：</label>
                <span>{{ selectedProfile.profileOwnerName }}</span>
              </div>
              <div class="info-item">
                <label>性别：</label>
                <span>{{ getGenderText(selectedProfile.gender) }}</span>
              </div>
              <div class="info-item">
                <label>年龄：</label>
                <span>{{ selectedProfile.age }}岁</span>
              </div>
              <div class="info-item">
                <label>出生日期：</label>
                <span>{{ formatDate(selectedProfile.birthDate) || '未填写' }}</span>
              </div>
              <div class="info-item">
                <label>身份证号：</label>
                <span>{{ selectedProfile.idCard || '未填写' }}</span>
              </div>
              <div class="info-item">
                <label>病史信息：</label>
                <span class="medical-history">{{ selectedProfile.medicalHistory || '无' }}</span>
              </div>
            </div>
            <div class="detail-actions">
              <button @click="editProfile(selectedProfile)" class="btn-primary">
                编辑档案
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">⏳ 加载中...</div>
    </div>

    <!-- 成功通知 -->
    <div v-if="successMessage" class="success-notification">
      <div class="notification-content">
        <span class="success-icon">✅</span>
        {{ successMessage }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { getHealthProfiles, getHealthProfile, deleteHealthProfile } from '@/api/health'
import ProfileForm from '@/components/ProfileForm.vue'

// 响应式数据
const loading = ref(false)
const loadError = ref('')
const successMessage = ref('')
const profiles = ref([])
const showForm = ref(false)
const showDetail = ref(false)
const isEditMode = ref(false)
const selectedProfile = ref(null)

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 方法
const loadProfiles = async (page = 1) => {
  loading.value = true
  loadError.value = ''

  try {
    const response = await getHealthProfiles({
      page: page,
      size: pagination.size
    })

    if (response.data.code === 200) {
      const data = response.data.data
      profiles.value = data.profiles || []
      pagination.current = data.page || page
      pagination.total = data.total || 0
      loadError.value = ''
    } else {
      loadError.value = response.data.message || '获取档案列表失败'
      console.error('获取档案列表失败:', response.data.message)
    }
  } catch (error) {
    loadError.value = '网络错误，请检查网络连接后重试'
    console.error('获取档案列表失败:', error)
  } finally {
    loading.value = false
  }
}

const retryLoad = () => {
  loadProfiles(pagination.current)
}

const showCreateForm = () => {
  selectedProfile.value = null
  isEditMode.value = false
  showForm.value = true
}

const editProfile = (profile) => {
  selectedProfile.value = profile
  isEditMode.value = true
  showForm.value = true
  showDetail.value = false
}

const viewProfile = async (profile) => {
  // 如果档案有ID，尝试获取完整详情
  if (profile.id) {
    loading.value = true
    try {
      const detailResponse = await getHealthProfile(profile.id)
      if (detailResponse.data.code === 200) {
        selectedProfile.value = detailResponse.data.data
        console.log('获取档案详情:', detailResponse.data.data)
      } else {
        selectedProfile.value = profile
        console.warn('获取档案详情失败，使用列表数据')
      }
    } catch (error) {
      console.error('获取档案详情失败:', error)
      selectedProfile.value = profile
    } finally {
      loading.value = false
    }
  } else {
    selectedProfile.value = profile
  }

  showDetail.value = true
}

const deleteProfile = async (profile) => {
  if (!confirm(`确定要删除 ${profile.profileOwnerName} 的健康档案吗？`)) {
    return
  }

  try {
    const response = await deleteHealthProfile(profile.id)
    if (response.data.code === 200) {
      await loadProfiles(pagination.current)
    } else {
      alert('删除失败：' + response.data.message)
    }
  } catch (error) {
    console.error('删除档案失败:', error)
    alert('删除失败，请重试')
  }
}

const hideForm = () => {
  showForm.value = false
  selectedProfile.value = null
}

const hideDetail = () => {
  showDetail.value = false
  selectedProfile.value = null
}

const handleFormSuccess = async (newProfile) => {
  hideForm()

  // 如果是创建新档案
  if (!isEditMode.value && newProfile) {
    console.log('创建档案返回的数据:', newProfile)

    // 如果返回的数据有ID，尝试获取完整详情
    if (newProfile.id) {
      try {
        const detailResponse = await getHealthProfile(newProfile.id)
        if (detailResponse.data.code === 200) {
          const completeProfile = detailResponse.data.data
          profiles.value.unshift(completeProfile)
          console.log('获取到完整档案数据:', completeProfile)
        } else {
          // 如果获取详情失败，使用返回的数据
          profiles.value.unshift(newProfile)
        }
      } catch (error) {
        console.error('获取档案详情失败:', error)
        // 如果获取详情失败，使用返回的数据
        profiles.value.unshift(newProfile)
      }
    } else {
      // 如果没有ID，直接使用返回的数据
      profiles.value.unshift(newProfile)
    }

    pagination.total += 1
    loadError.value = '' // 清除错误状态
    showSuccessMessage('健康档案创建成功！')
  } else {
    // 如果是编辑，尝试重新加载列表
    await loadProfiles(pagination.current)
    showSuccessMessage('健康档案更新成功！')
  }
}

const showSuccessMessage = (message) => {
  successMessage.value = message
  setTimeout(() => {
    successMessage.value = ''
  }, 3000)
}

const changePage = (page) => {
  if (page >= 1 && page <= Math.ceil(pagination.total / pagination.size)) {
    loadProfiles(page)
  }
}

const getGenderText = (gender) => {
  const genderMap = {
    MALE: '男',
    FEMALE: '女',
    UNKNOWN: '其他'
  }
  return genderMap[gender] || '未知'
}

const maskIdCard = (idCard) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 生命周期
onMounted(async () => {
  // 尝试加载档案列表
  await loadProfiles()

  // 如果加载失败，给用户一个提示
  if (loadError.value) {
    console.warn('健康档案列表加载失败，但用户仍可以创建新档案')
  }
})
</script>

<style scoped>
.health-profile-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f0f0;
}

.header-content h1 {
  color: #262626;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.page-description {
  color: #8c8c8c;
  font-size: 14px;
  margin: 0;
}

.header-actions .btn-primary {
  background: #1890ff;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions .btn-primary:hover {
  background: #096dd9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.btn-icon {
  font-size: 16px;
}

/* 档案网格 */
.profiles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.profile-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.profile-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-color: #1890ff;
}

.profile-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin: 0 auto 16px;
  overflow: hidden;
  border: 2px solid #f0f0f0;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
}

.avatar-placeholder.large {
  font-size: 48px;
  width: 120px;
  height: 120px;
  border-radius: 50%;
}

.profile-info {
  text-align: center;
  margin-bottom: 16px;
}

.profile-name {
  color: #262626;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.profile-details {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 8px;
}

.profile-gender,
.profile-age {
  color: #595959;
  font-size: 14px;
  background: #f5f5f5;
  padding: 2px 8px;
  border-radius: 12px;
}

.profile-id {
  color: #8c8c8c;
  font-size: 12px;
}

.profile-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.action-btn.edit {
  background: #e6f7ff;
  color: #1890ff;
}

.action-btn.edit:hover {
  background: #1890ff;
  color: white;
}

.action-btn.delete {
  background: #fff2f0;
  color: #ff4d4f;
}

.action-btn.delete:hover {
  background: #ff4d4f;
  color: white;
}

/* 空状态 */
.empty-state,
.error-state {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon,
.error-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.error-icon {
  color: #ff4d4f;
}

.empty-state h3,
.error-state h3 {
  color: #262626;
  font-size: 20px;
  margin-bottom: 12px;
}

.empty-state p,
.error-state p {
  color: #8c8c8c;
  margin-bottom: 24px;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-secondary {
  background: #f5f5f5;
  color: #595959;
  border: 1px solid #d9d9d9;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 32px;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  border-color: #1890ff;
  color: #1890ff;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #595959;
  font-size: 14px;
}

/* 弹窗 */
.form-modal,
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  z-index: 1001;
}

/* 详情弹窗 */
.profile-detail {
  background: white;
  border-radius: 16px;
  overflow: hidden;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.detail-header h3 {
  color: #262626;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #ff4d4f;
  color: white;
}

.detail-content {
  padding: 28px;
}

.detail-avatar {
  text-align: center;
  margin-bottom: 24px;
}

.detail-avatar img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #f0f0f0;
}

.detail-info {
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
}

.info-item label {
  color: #595959;
  font-weight: 600;
  min-width: 100px;
  font-size: 14px;
}

.info-item span {
  color: #262626;
  font-size: 14px;
  flex: 1;
}

.medical-history {
  line-height: 1.6;
  white-space: pre-wrap;
}

.detail-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-spinner {
  font-size: 18px;
  color: #1890ff;
  font-weight: 600;
}

/* 成功通知 */
.success-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 3000;
  animation: slideInRight 0.3s ease;
}

.notification-content {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.2);
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.success-icon {
  font-size: 16px;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .health-profile-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .profiles-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .modal-content {
    width: 95%;
  }

  .detail-content {
    padding: 20px;
  }

  .pagination {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
