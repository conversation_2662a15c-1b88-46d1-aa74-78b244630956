<template>
  <div class="doctor-schedule-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1>📅 排班管理</h1>
          <p class="header-subtitle">管理您的出诊时间安排</p>
        </div>
        <div class="header-actions">
          <button @click="refreshData" class="refresh-btn" :disabled="loading">
            <span class="refresh-icon" :class="{ 'spinning': loading }">🔄</span>
            刷新
          </button>
          <button @click="showCreateForm" class="create-btn">
            ➕ 新增排班
          </button>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.totalSchedules }}</div>
          <div class="stat-label">总排班数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">📅</div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.thisWeekSchedules }}</div>
          <div class="stat-label">本周排班</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🎯</div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.availableSlots }}</div>
          <div class="stat-label">可用号源</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">👥</div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.bookedSlots }}</div>
          <div class="stat-label">已预约</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <ScheduleList
        :schedules="schedules"
        :loading="loading"
        @create="showCreateForm"
        @edit="showEditForm"
        @copy="showCopyForm"
        @delete="showDeleteConfirm"
        @refresh="refreshData"
      />
    </div>

    <!-- 成功消息提示 -->
    <div v-if="successMessage" class="success-message">
      <div class="message-content">
        <span class="success-icon">✅</span>
        {{ successMessage }}
      </div>
    </div>

    <!-- 排班表单弹窗 -->
    <ScheduleForm
      v-if="showForm"
      :schedule="selectedSchedule"
      :is-edit="isEditMode"
      @close="closeForm"
      @success="handleFormSuccess"
    />

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteDialog" class="delete-overlay">
      <div class="delete-dialog">
        <div class="delete-header">
          <h3>🗑️ 确认删除</h3>
        </div>
        <div class="delete-content">
          <p>您确定要删除以下排班吗？</p>
          <div class="schedule-info">
            <div class="info-item">
              <strong>日期:</strong> {{ getDateDisplayName(scheduleToDelete?.scheduleDate) }}
            </div>
            <div class="info-item">
              <strong>时间:</strong> 
              {{ formatTimeDisplay(scheduleToDelete?.startTime) }} - 
              {{ formatTimeDisplay(scheduleToDelete?.endTime) }}
            </div>
            <div class="info-item">
              <strong>号源:</strong> {{ scheduleToDelete?.totalSlots }}个
            </div>
            <div v-if="scheduleToDelete?.bookedSlots > 0" class="warning-info">
              ⚠️ 注意：该排班已有 {{ scheduleToDelete.bookedSlots }} 个预约，删除后将影响患者就诊
            </div>
          </div>
        </div>
        <div class="delete-actions">
          <button @click="closeDeleteDialog" class="cancel-btn">
            取消
          </button>
          <button 
            @click="confirmDelete" 
            :disabled="deleteLoading"
            class="confirm-btn"
          >
            <span v-if="deleteLoading">删除中...</span>
            <span v-else>确认删除</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 复制排班弹窗 -->
    <div v-if="showCopyDialog" class="copy-overlay">
      <div class="copy-dialog">
        <div class="copy-header">
          <h3>📋 复制排班</h3>
          <button @click="closeCopyDialog" class="close-btn">✕</button>
        </div>
        <div class="copy-content">
          <p>将以下排班复制到其他日期：</p>
          <div class="source-schedule">
            <div class="schedule-preview">
              <div class="preview-date">{{ getDateDisplayName(scheduleToCopy?.scheduleDate) }}</div>
              <div class="preview-time">
                {{ formatTimeDisplay(scheduleToCopy?.startTime) }} - 
                {{ formatTimeDisplay(scheduleToCopy?.endTime) }}
              </div>
              <div class="preview-slots">{{ scheduleToCopy?.totalSlots }}个号源</div>
            </div>
          </div>
          
          <div class="target-dates">
            <label>选择目标日期：</label>
            <div class="date-inputs">
              <input
                v-for="(date, index) in copyTargetDates"
                :key="index"
                v-model="copyTargetDates[index]"
                type="date"
                :min="getToday()"
                class="date-input"
              />
              <button @click="addTargetDate" class="add-date-btn">
                ➕ 添加日期
              </button>
            </div>
          </div>
        </div>
        <div class="copy-actions">
          <button @click="closeCopyDialog" class="cancel-btn">
            取消
          </button>
          <button 
            @click="confirmCopy" 
            :disabled="copyLoading || !hasValidTargetDates"
            class="confirm-btn"
          >
            <span v-if="copyLoading">复制中...</span>
            <span v-else>确认复制</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import ScheduleList from '@/components/ScheduleList.vue'
import ScheduleForm from '@/components/ScheduleForm.vue'
import { 
  getDoctorSchedules, 
  deleteSchedule, 
  copySchedule 
} from '@/api/schedule'
import {
  formatTimeDisplay,
  getDateDisplayName,
  getToday,
  isToday,
  isTomorrow,
  getWeekRange,
  isPastDate
} from '@/utils/dateUtils'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const deleteLoading = ref(false)
const copyLoading = ref(false)
const schedules = ref([])
const successMessage = ref('')

// 表单相关
const showForm = ref(false)
const selectedSchedule = ref(null)
const isEditMode = ref(false)

// 删除相关
const showDeleteDialog = ref(false)
const scheduleToDelete = ref(null)

// 复制相关
const showCopyDialog = ref(false)
const scheduleToCopy = ref(null)
const copyTargetDates = ref([''])

// 计算属性
const stats = computed(() => {
  const total = schedules.value.length
  const weekRange = getWeekRange()
  const thisWeek = schedules.value.filter(s => 
    s.scheduleDate >= weekRange.startDate && s.scheduleDate <= weekRange.endDate
  ).length
  
  const totalAvailable = schedules.value.reduce((sum, s) => sum + s.availableSlots, 0)
  const totalBooked = schedules.value.reduce((sum, s) => sum + s.bookedSlots, 0)
  
  return {
    totalSchedules: total,
    thisWeekSchedules: thisWeek,
    availableSlots: totalAvailable,
    bookedSlots: totalBooked
  }
})

const hasValidTargetDates = computed(() => {
  return copyTargetDates.value.some(date => date && date >= getToday())
})

// 方法
const loadSchedules = async () => {
  loading.value = true
  try {
    const response = await getDoctorSchedules()
    if (response.data.code === 200) {
      schedules.value = response.data.data || []
    } else {
      console.error('获取排班失败:', response.data.message)
      schedules.value = []
    }
  } catch (error) {
    console.error('获取排班失败:', error)
    schedules.value = []
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadSchedules()
}

const showCreateForm = (dateString = null) => {
  selectedSchedule.value = dateString ? { scheduleDate: dateString } : null
  isEditMode.value = false
  showForm.value = true
}

const showEditForm = (schedule) => {
  selectedSchedule.value = schedule
  isEditMode.value = true
  showForm.value = true
}

const showCopyForm = (schedule) => {
  scheduleToCopy.value = schedule
  copyTargetDates.value = ['']
  showCopyDialog.value = true
}

const showDeleteConfirm = (schedule) => {
  scheduleToDelete.value = schedule
  showDeleteDialog.value = true
}

const closeForm = () => {
  showForm.value = false
  selectedSchedule.value = null
  isEditMode.value = false
}

const closeDeleteDialog = () => {
  showDeleteDialog.value = false
  scheduleToDelete.value = null
}

const closeCopyDialog = () => {
  showCopyDialog.value = false
  scheduleToCopy.value = null
  copyTargetDates.value = ['']
}

const handleFormSuccess = (message) => {
  closeForm()
  successMessage.value = message
  setTimeout(() => {
    successMessage.value = ''
  }, 3000)
  loadSchedules() // 重新加载数据
}

const confirmDelete = async () => {
  if (!scheduleToDelete.value) return

  deleteLoading.value = true
  try {
    const response = await deleteSchedule(scheduleToDelete.value.id)
    if (response.data.code === 200) {
      successMessage.value = '排班删除成功！'
      setTimeout(() => {
        successMessage.value = ''
      }, 3000)
      loadSchedules() // 重新加载数据
      closeDeleteDialog()
    } else {
      alert('删除失败：' + response.data.message)
    }
  } catch (error) {
    console.error('删除排班失败:', error)
    if (error.response?.data?.message) {
      alert('删除失败：' + error.response.data.message)
    } else {
      alert('删除失败，请重试')
    }
  } finally {
    deleteLoading.value = false
  }
}

const addTargetDate = () => {
  copyTargetDates.value.push('')
}

const confirmCopy = async () => {
  if (!scheduleToCopy.value || !hasValidTargetDates.value) return

  const validDates = copyTargetDates.value.filter(date => date && date >= getToday())
  
  copyLoading.value = true
  try {
    const response = await copySchedule(scheduleToCopy.value.id, validDates)
    if (response.data.code === 200) {
      successMessage.value = `排班复制成功！已复制到 ${validDates.length} 个日期`
      setTimeout(() => {
        successMessage.value = ''
      }, 3000)
      loadSchedules() // 重新加载数据
      closeCopyDialog()
    } else {
      alert('复制失败：' + response.data.message)
    }
  } catch (error) {
    console.error('复制排班失败:', error)
    if (error.response?.data?.message) {
      alert('复制失败：' + error.response.data.message)
    } else {
      alert('复制失败，请重试')
    }
  } finally {
    copyLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadSchedules()
})
</script>

<style scoped>
.doctor-schedule-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  padding: 20px;
}

.page-header {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  padding: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h1 {
  margin: 0 0 8px 0;
  color: #1e40af;
  font-size: 28px;
  font-weight: 700;
}

.header-subtitle {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.refresh-btn,
.create-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.refresh-btn:hover:not(:disabled) {
  background: #e5e7eb;
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.create-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.create-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.15);
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.main-content {
  margin-bottom: 24px;
}

.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.message-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: #dcfce7;
  color: #166534;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-weight: 500;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 删除确认弹窗样式 */
.delete-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.delete-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
}

.delete-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-radius: 12px 12px 0 0;
}

.delete-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.delete-content {
  padding: 24px;
}

.delete-content p {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 16px;
}

.schedule-info {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.info-item {
  margin-bottom: 8px;
  color: #374151;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.warning-info {
  margin-top: 12px;
  padding: 12px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  color: #92400e;
  font-size: 14px;
}

.delete-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 24px;
  background: #f9fafb;
  border-radius: 0 0 12px 12px;
}

.cancel-btn,
.confirm-btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.confirm-btn {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
}

.confirm-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.confirm-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 复制排班弹窗样式 */
.copy-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.copy-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

.copy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 12px 12px 0 0;
}

.copy-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.copy-content {
  padding: 24px;
}

.copy-content p {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 16px;
}

.source-schedule {
  margin-bottom: 24px;
}

.schedule-preview {
  background: #f0f9ff;
  border: 1px solid #3b82f6;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.preview-date {
  font-weight: 600;
  color: #1e40af;
  font-size: 16px;
  margin-bottom: 4px;
}

.preview-time {
  color: #374151;
  font-size: 14px;
  margin-bottom: 4px;
}

.preview-slots {
  color: #6b7280;
  font-size: 14px;
}

.target-dates label {
  display: block;
  margin-bottom: 12px;
  font-weight: 500;
  color: #374151;
}

.date-inputs {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.date-input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.add-date-btn {
  padding: 8px 12px;
  background: #f0f9ff;
  color: #3b82f6;
  border: 1px solid #3b82f6;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  align-self: flex-start;
}

.add-date-btn:hover {
  background: #3b82f6;
  color: white;
}

.copy-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 24px;
  background: #f9fafb;
  border-radius: 0 0 12px 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .doctor-schedule-page {
    padding: 12px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: space-between;
  }
  
  .stats-overview {
    grid-template-columns: 1fr;
  }
  
  .delete-dialog,
  .copy-dialog {
    width: 95%;
    margin: 20px;
  }
  
  .delete-content,
  .copy-content {
    padding: 16px;
  }
  
  .delete-actions,
  .copy-actions {
    flex-direction: column;
  }
}
</style>
