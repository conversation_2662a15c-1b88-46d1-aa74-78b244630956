<template>
  <div class="patient-appointments">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>我的预约</h1>
      <div class="header-actions">
        <button @click="showBookingModal = true" class="book-btn">
          <span class="btn-icon">📅</span>
          预约挂号
        </button>
      </div>
    </div>

    <!-- 快速统计 -->
    <div class="quick-stats">
      <div class="stat-card">
        <div class="stat-icon pending">⏰</div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.pending }}</div>
          <div class="stat-label">待就诊</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon completed">✅</div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.completed }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon upcoming">📋</div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.upcoming }}</div>
          <div class="stat-label">即将到来</div>
        </div>
      </div>
    </div>

    <!-- 即将到来的预约 -->
    <div class="upcoming-appointments" v-if="upcomingAppointments.length > 0">
      <h3>即将到来的预约</h3>
      <div class="upcoming-list">
        <div 
          v-for="appointment in upcomingAppointments" 
          :key="appointment.id"
          class="upcoming-card"
        >
          <div class="appointment-time">
            <div class="date">{{ formatDate(appointment.appointmentDate) }}</div>
            <div class="time">{{ appointment.appointmentTime }}</div>
          </div>
          <div class="appointment-info">
            <h4>{{ appointment.doctorName }}</h4>
            <p>{{ appointment.departmentName }}</p>
            <p class="patient-name">就诊人: {{ appointment.profileOwnerName }}</p>
          </div>
          <div class="appointment-actions">
            <button 
              @click="viewAppointmentDetail(appointment.id)" 
              class="detail-btn"
            >
              查看详情
            </button>
            <button 
              v-if="appointment.canCancel"
              @click="cancelAppointment(appointment.id)" 
              class="cancel-btn"
            >
              取消预约
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选功能 -->
    <div class="filter-section">
      <div class="filter-controls">
        <select v-model="filterStatus" @change="loadAppointments" class="status-filter">
          <option value="">全部状态</option>
          <option value="booked">已预约</option>
          <option value="completed">已完成</option>
          <option value="cancelled">已取消</option>
        </select>

        <input
          type="date"
          v-model="filterDate"
          @change="loadAppointments"
          class="date-filter"
          placeholder="选择日期"
        >

        <select
          v-model="filterDepartment"
          @change="loadAppointments"
          class="department-filter"
        >
          <option value="">全部科室</option>
          <option value="内科">内科</option>
          <option value="外科">外科</option>
          <option value="儿科">儿科</option>
          <option value="妇产科">妇产科</option>
          <option value="眼科">眼科</option>
          <option value="耳鼻喉科">耳鼻喉科</option>
          <option value="皮肤科">皮肤科</option>
          <option value="骨科">骨科</option>
        </select>

        <select
          v-model="filterTitle"
          @change="loadAppointments"
          class="title-filter"
        >
          <option value="">全部职称</option>
          <option value="主任医师">主任医师</option>
          <option value="副主任医师">副主任医师</option>
          <option value="主治医师">主治医师</option>
          <option value="住院医师">住院医师</option>
        </select>
      </div>
    </div>

    <!-- 预约列表 -->
    <div class="appointments-list">
      <div v-if="loading" class="loading">
        <div class="loading-spinner"></div>
        <p>正在加载预约信息...</p>
      </div>
      
      <div v-else-if="appointments.length === 0" class="empty-state">
        <div class="empty-icon">📋</div>
        <h3>暂无预约记录</h3>
        <p>您还没有任何预约记录，点击上方"预约挂号"开始预约</p>
        <button @click="showBookingModal = true" class="book-btn">
          立即预约
        </button>
      </div>
      
      <div v-else class="appointment-cards">
        <div 
          v-for="appointment in appointments" 
          :key="appointment.id"
          class="appointment-card"
          :class="getAppointmentCardClass(appointment.status)"
        >
          <div class="card-header">
            <div class="appointment-status">
              <span class="status-badge" :class="appointment.status.toLowerCase()">
                {{ getStatusText(appointment.status) }}
              </span>
            </div>
            <div class="appointment-date">
              {{ formatDate(appointment.appointmentDate) }} {{ appointment.appointmentTime }}
            </div>
          </div>
          
          <div class="card-body">
            <div class="doctor-info">
              <h4>{{ appointment.doctorName }}</h4>
              <p>{{ appointment.departmentName }}</p>
            </div>
            
            <div class="patient-info">
              <p><strong>就诊人:</strong> {{ appointment.profileOwnerName }}</p>
              <p v-if="appointment.notes"><strong>备注:</strong> {{ appointment.notes }}</p>
            </div>
          </div>
          
          <div class="card-footer">
            <div class="appointment-actions">
              <button 
                @click="viewAppointmentDetail(appointment.id)" 
                class="action-btn detail"
              >
                查看详情
              </button>
              
              <button 
                v-if="appointment.canCancel && appointment.status === 'BOOKED'"
                @click="cancelAppointment(appointment.id)" 
                class="action-btn cancel"
              >
                取消预约
              </button>
              
              <button 
                v-if="appointment.status === 'COMPLETED'"
                @click="viewMedicalRecord(appointment.id)" 
                class="action-btn record"
              >
                查看病历
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <div class="pagination" v-if="totalPages > 1">
        <button 
          @click="changePage(currentPage - 1)" 
          :disabled="currentPage <= 1"
          class="page-btn"
        >
          上一页
        </button>
        
        <div class="page-numbers">
          <button 
            v-for="page in getPageNumbers()" 
            :key="page"
            @click="changePage(page)"
            :class="['page-number', { active: page === currentPage }]"
          >
            {{ page }}
          </button>
        </div>
        
        <button 
          @click="changePage(currentPage + 1)" 
          :disabled="currentPage >= totalPages"
          class="page-btn"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 预约挂号模态框 -->
    <div v-if="showBookingModal" class="modal-overlay" @click="closeBookingModal">
      <div class="modal-content booking-modal" @click.stop>
        <div class="modal-header">
          <h3>预约挂号</h3>
          <button @click="closeBookingModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <AppointmentBooking @success="handleBookingSuccess" @cancel="closeBookingModal" />
        </div>
      </div>
    </div>

    <!-- 预约详情模态框 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="closeDetailModal">
      <div class="modal-content detail-modal" @click.stop>
        <div class="modal-header">
          <h3>预约详情</h3>
          <button @click="closeDetailModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <AppointmentDetail
            v-if="selectedAppointmentId"
            :appointment-id="selectedAppointmentId"
            @cancel="handleAppointmentCancel"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import * as appointmentsApi from '@/api/appointments'
import AppointmentBooking from '@/components/AppointmentBooking.vue'
import AppointmentDetail from '@/components/AppointmentDetail.vue'

export default {
  name: 'PatientAppointments',
  components: {
    AppointmentBooking,
    AppointmentDetail
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const appointments = ref([])
    const upcomingAppointments = ref([])
    const currentPage = ref(1)
    const totalPages = ref(1)
    const pageSize = 10
    
    const filterStatus = ref('')
    const filterDate = ref('')
    const filterDepartment = ref('')
    const filterTitle = ref('')
    
    const showBookingModal = ref(false)
    const showDetailModal = ref(false)
    const selectedAppointmentId = ref(null)
    
    const stats = reactive({
      pending: 0,
      completed: 0,
      upcoming: 0
    })

    // 计算属性
    const getPageNumbers = () => {
      const pages = []
      const start = Math.max(1, currentPage.value - 2)
      const end = Math.min(totalPages.value, currentPage.value + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    }

    // 方法
    const loadAppointments = async () => {
      try {
        loading.value = true
        
        const params = {
          page: Math.max(0, currentPage.value - 1),
          size: pageSize,
          status: filterStatus.value,
          date: filterDate.value,
          department: filterDepartment.value,
          title: filterTitle.value
        }
        
        const response = await appointmentsApi.getMyAppointments(params)
        
        if (response.data.code === 200) {
          const data = response.data.data
          appointments.value = data.content || []
          totalPages.value = data.totalPages || 1
          
          // 更新统计信息
          updateStats()
        }
      } catch (error) {
        console.error('加载预约列表失败:', error)
      } finally {
        loading.value = false
      }
    }

    const loadUpcomingAppointments = async () => {
      try {
        // 明确传递大写状态值，避免枚举错误
        const response = await appointmentsApi.getUpcomingAppointments({
          days: 7,
          status: 'BOOKED'  // 明确指定大写状态
        })

        if (response.data.code === 200) {
          upcomingAppointments.value = response.data.data || []
          stats.upcoming = upcomingAppointments.value.length
        } else {
          console.error('获取即将到来的预约失败:', response.data.message)
        }
      } catch (error) {
        console.error('加载即将到来的预约失败:', error)
        // 如果还是有枚举错误，暂时跳过这个功能
        if (error.message && error.message.includes('enum constant')) {
          console.warn('预约状态枚举问题，跳过即将到来的预约加载')
          upcomingAppointments.value = []
          stats.upcoming = 0
        }
      }
    }

    const updateStats = () => {
      // 使用大写状态值，与后端Java枚举保持一致
      stats.pending = appointments.value.filter(a => a.status === 'BOOKED').length
      stats.completed = appointments.value.filter(a => a.status === 'COMPLETED').length
    }

    const changePage = (page) => {
      currentPage.value = page
      loadAppointments()
    }



    const viewAppointmentDetail = (appointmentId) => {
      selectedAppointmentId.value = appointmentId
      showDetailModal.value = true
    }

    const cancelAppointment = async (appointmentId) => {
      if (!confirm('确定要取消这个预约吗？')) {
        return
      }
      
      try {
        const response = await appointmentsApi.cancelPatientAppointment(appointmentId)
        
        if (response.data.code === 200) {
          alert('预约已取消')
          loadAppointments()
          loadUpcomingAppointments()
        } else {
          alert(response.data.message || '取消预约失败')
        }
      } catch (error) {
        console.error('取消预约失败:', error)
        alert('取消预约失败，请稍后重试')
      }
    }

    const viewMedicalRecord = (appointmentId) => {
      // 跳转到病历查看页面或打开病历模态框
      console.log('查看病历:', appointmentId)
      // TODO: 实现病历查看功能
    }

    const handleBookingSuccess = () => {
      showBookingModal.value = false
      loadAppointments()
      loadUpcomingAppointments()
    }

    const handleAppointmentCancel = () => {
      showDetailModal.value = false
      loadAppointments()
      loadUpcomingAppointments()
    }

    const closeBookingModal = () => {
      showBookingModal.value = false
    }

    const closeDetailModal = () => {
      showDetailModal.value = false
      selectedAppointmentId.value = null
    }

    // 工具函数
    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    }

    const getStatusText = (status) => {
      const statusMap = {
        'BOOKED': '已预约',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消',
        // 兼容小写格式
        'booked': '已预约',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    }

    const getAppointmentCardClass = (status) => {
      return {
        'booked': status === 'BOOKED' || status === 'booked',
        'completed': status === 'COMPLETED' || status === 'completed',
        'cancelled': status === 'CANCELLED' || status === 'cancelled'
      }
    }

    // 组件挂载
    onMounted(() => {
      loadAppointments()
      loadUpcomingAppointments()
    })

    return {
      loading,
      appointments,
      upcomingAppointments,
      currentPage,
      totalPages,
      filterStatus,
      filterDate,
      filterDepartment,
      filterTitle,
      showBookingModal,
      showDetailModal,
      selectedAppointmentId,
      stats,
      getPageNumbers,
      loadAppointments,
      changePage,
      viewAppointmentDetail,
      cancelAppointment,
      viewMedicalRecord,
      handleBookingSuccess,
      handleAppointmentCancel,
      closeBookingModal,
      closeDetailModal,
      formatDate,
      getStatusText,
      getAppointmentCardClass
    }
  }
}
</script>

<style scoped>
.patient-appointments {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 28px;
}

.book-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #4A90E2;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: background 0.3s;
}

.book-btn:hover {
  background: #357abd;
}

.btn-icon {
  font-size: 18px;
}

.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  font-size: 32px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.stat-icon.pending { background: #fff3cd; }
.stat-icon.completed { background: #d4edda; }
.stat-icon.upcoming { background: #e3f2fd; }

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  color: #6c757d;
  font-size: 14px;
}

.upcoming-appointments {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.upcoming-appointments h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 20px;
}

.upcoming-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.upcoming-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #f8f9fa;
}

.appointment-time {
  text-align: center;
  min-width: 80px;
}

.appointment-time .date {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 4px;
}

.appointment-time .time {
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
}

.appointment-info {
  flex: 1;
}

.appointment-info h4 {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 16px;
}

.appointment-info p {
  margin: 2px 0;
  color: #6c757d;
  font-size: 14px;
}

.patient-name {
  font-weight: 500;
  color: #495057 !important;
}

.appointment-actions {
  display: flex;
  gap: 10px;
}

.detail-btn, .cancel-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
}

.detail-btn {
  background: #4A90E2;
  color: white;
}

.detail-btn:hover {
  background: #357abd;
}

.cancel-btn {
  background: #dc3545;
  color: white;
}

.cancel-btn:hover {
  background: #c82333;
}

.filter-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.filter-controls {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.status-filter, .date-filter, .department-filter, .title-filter {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  min-width: 120px;
}

.department-filter, .title-filter {
  flex: 1;
  min-width: 150px;
}

.appointments-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.loading {
  text-align: center;
  padding: 50px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4A90E2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 24px;
}

.empty-state p {
  color: #6c757d;
  margin-bottom: 30px;
}

.appointment-cards {
  padding: 20px;
}

.appointment-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
  transition: all 0.3s;
}

.appointment-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.appointment-card.booked {
  border-left: 4px solid #4A90E2;
}

.appointment-card.completed {
  border-left: 4px solid #28a745;
}

.appointment-card.cancelled {
  border-left: 4px solid #dc3545;
  opacity: 0.7;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.booked {
  background: #e3f2fd;
  color: #1976d2;
}

.status-badge.completed {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-badge.cancelled {
  background: #ffebee;
  color: #d32f2f;
}

.appointment-date {
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
}

.card-body {
  padding: 20px;
}

.doctor-info h4 {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 18px;
}

.doctor-info p {
  margin: 0 0 15px 0;
  color: #6c757d;
}

.patient-info p {
  margin: 4px 0;
  color: #495057;
  font-size: 14px;
}

.card-footer {
  padding: 15px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 8px;
  transition: all 0.3s;
}

.action-btn.detail {
  background: #4A90E2;
  color: white;
}

.action-btn.detail:hover {
  background: #357abd;
}

.action-btn.cancel {
  background: #dc3545;
  color: white;
}

.action-btn.cancel:hover {
  background: #c82333;
}

.action-btn.record {
  background: #28a745;
  color: white;
}

.action-btn.record:hover {
  background: #218838;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #e9ecef;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-btn:not(:disabled):hover {
  border-color: #4A90E2;
  background: #f8f9fa;
}

.page-numbers {
  display: flex;
  gap: 5px;
}

.page-number {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.page-number:hover {
  border-color: #4A90E2;
  background: #f8f9fa;
}

.page-number.active {
  background: #4A90E2;
  color: white;
  border-color: #4A90E2;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-height: 90vh;
  overflow: auto;
}

.booking-modal {
  width: 90%;
  max-width: 800px;
}

.detail-modal {
  width: 90%;
  max-width: 600px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
  background: #f8f9fa;
  border-radius: 50%;
}

.modal-body {
  padding: 20px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .quick-stats {
    grid-template-columns: 1fr;
  }
  
  .upcoming-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .filter-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .status-filter, .date-filter, .department-filter, .title-filter {
    min-width: auto;
  }
  
  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .appointment-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .action-btn {
    margin-right: 0;
  }
  
  .pagination {
    flex-wrap: wrap;
    gap: 5px;
  }
  
  .page-numbers {
    order: -1;
    width: 100%;
    justify-content: center;
  }
}
</style>
