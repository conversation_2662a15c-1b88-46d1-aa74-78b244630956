<template>
  <div class="statistics-test">
    <div class="test-header">
      <h1>医生统计分析接口测试</h1>
      <p>测试所有统计分析相关的API接口</p>
    </div>

    <div class="test-controls">
      <select v-model="timeRange" class="time-range-select">
        <option value="week">本周</option>
        <option value="month">本月</option>
        <option value="quarter">近三个月</option>
      </select>
      <button @click="testAllApis" class="test-btn" :disabled="testing">
        {{ testing ? '测试中...' : '测试所有接口' }}
      </button>
    </div>

    <div class="test-results">
      <!-- KPI接口测试 -->
      <div class="test-section">
        <h3>1. KPI指标接口测试</h3>
        <button @click="testKpiApi" :disabled="testing">测试 KPI 接口</button>
        <div class="result-box">
          <pre>{{ kpiResult }}</pre>
        </div>
      </div>

      <!-- 服务趋势接口测试 -->
      <div class="test-section">
        <h3>2. 服务趋势接口测试</h3>
        <button @click="testServiceTrendApi" :disabled="testing">测试服务趋势接口</button>
        <div class="result-box">
          <pre>{{ serviceTrendResult }}</pre>
        </div>
      </div>

      <!-- 预约状态接口测试 -->
      <div class="test-section">
        <h3>3. 预约状态分配接口测试</h3>
        <button @click="testAppointmentStatusApi" :disabled="testing">测试预约状态接口</button>
        <div class="result-box">
          <pre>{{ appointmentStatusResult }}</pre>
        </div>
      </div>

      <!-- 患者排行接口测试 -->
      <div class="test-section">
        <h3>4. 高频患者排行接口测试</h3>
        <button @click="testTopPatientsApi" :disabled="testing">测试患者排行接口</button>
        <div class="result-box">
          <pre>{{ topPatientsResult }}</pre>
        </div>
      </div>

      <!-- 时间段分析接口测试 -->
      <div class="test-section">
        <h3>5. 预约时间段分析接口测试</h3>
        <button @click="testScheduleHotnessApi" :disabled="testing">测试时间段分析接口</button>
        <div class="result-box">
          <pre>{{ scheduleHotnessResult }}</pre>
        </div>
      </div>
    </div>

    <div class="navigation">
      <button @click="goToStatistics" class="nav-btn">
        前往统计分析页面（图表版）
      </button>
      <button @click="goToSimple" class="nav-btn">
        前往统计分析页面（简化版）
      </button>
      <button @click="goBack" class="nav-btn secondary">
        返回医生仪表板
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  getDoctorKpi,
  getServiceTrend,
  getAppointmentStatus,
  getTopPatients,
  getScheduleHotness
} from '@/api/doctorStatistics'

const router = useRouter()

// 响应式数据
const testing = ref(false)
const timeRange = ref('month')

// 测试结果
const kpiResult = ref('等待测试...')
const serviceTrendResult = ref('等待测试...')
const appointmentStatusResult = ref('等待测试...')
const topPatientsResult = ref('等待测试...')
const scheduleHotnessResult = ref('等待测试...')

// 测试方法
const testKpiApi = async () => {
  try {
    kpiResult.value = '测试中...'
    const response = await getDoctorKpi({ range: timeRange.value })
    kpiResult.value = JSON.stringify(response.data, null, 2)
  } catch (error) {
    kpiResult.value = `错误: ${error.message}\n${JSON.stringify(error.response?.data || {}, null, 2)}`
  }
}

const testServiceTrendApi = async () => {
  try {
    serviceTrendResult.value = '测试中...'
    const response = await getServiceTrend({ range: timeRange.value })
    serviceTrendResult.value = JSON.stringify(response.data, null, 2)
  } catch (error) {
    serviceTrendResult.value = `错误: ${error.message}\n${JSON.stringify(error.response?.data || {}, null, 2)}`
  }
}

const testAppointmentStatusApi = async () => {
  try {
    appointmentStatusResult.value = '测试中...'
    const response = await getAppointmentStatus({ range: timeRange.value })
    appointmentStatusResult.value = JSON.stringify(response.data, null, 2)
  } catch (error) {
    appointmentStatusResult.value = `错误: ${error.message}\n${JSON.stringify(error.response?.data || {}, null, 2)}`
  }
}

const testTopPatientsApi = async () => {
  try {
    topPatientsResult.value = '测试中...'
    const response = await getTopPatients({ range: timeRange.value })
    topPatientsResult.value = JSON.stringify(response.data, null, 2)
  } catch (error) {
    topPatientsResult.value = `错误: ${error.message}\n${JSON.stringify(error.response?.data || {}, null, 2)}`
  }
}

const testScheduleHotnessApi = async () => {
  try {
    scheduleHotnessResult.value = '测试中...'
    const response = await getScheduleHotness({ range: timeRange.value })
    scheduleHotnessResult.value = JSON.stringify(response.data, null, 2)
  } catch (error) {
    scheduleHotnessResult.value = `错误: ${error.message}\n${JSON.stringify(error.response?.data || {}, null, 2)}`
  }
}

const testAllApis = async () => {
  testing.value = true
  try {
    await Promise.all([
      testKpiApi(),
      testServiceTrendApi(),
      testAppointmentStatusApi(),
      testTopPatientsApi(),
      testScheduleHotnessApi()
    ])
  } finally {
    testing.value = false
  }
}

const goToStatistics = () => {
  router.push('/doctor/statistics')
}

const goToSimple = () => {
  router.push('/doctor/statistics/simple')
}

const goBack = () => {
  router.push('/doctor')
}
</script>

<style scoped>
.statistics-test {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.test-header h1 {
  color: #1e293b;
  margin: 0 0 8px 0;
}

.test-header p {
  color: #64748b;
  margin: 0;
}

.test-controls {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 32px;
}

.time-range-select {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
}

.test-btn {
  padding: 8px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
}

.test-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.test-results {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.test-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.test-section h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
}

.test-section button {
  padding: 6px 16px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 12px;
}

.test-section button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.result-box {
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.result-box pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.navigation {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 32px;
}

.nav-btn {
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
}

.nav-btn.secondary {
  background: #6b7280;
}

.nav-btn:hover {
  opacity: 0.9;
}
</style>
