<template>
  <div class="doctor-prescription">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>电子处方</h1>
      <p class="page-description">管理患者处方，开具电子处方</p>
      <div class="stats-summary">
        <div class="stat-item">
          <div class="stat-number">{{ totalPrescriptions }}</div>
          <div class="stat-label">总处方数</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ todayPrescriptions }}</div>
          <div class="stat-label">今日开具</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ totalPatients }}</div>
          <div class="stat-label">服务患者</div>
        </div>
      </div>
    </div>

    <!-- 功能选择 -->
    <div class="function-tabs">
      <button
        @click="activeFunction = 'prescriptions'"
        class="function-tab"
        :class="{ active: activeFunction === 'prescriptions' }"
      >
        <span class="tab-icon">📋</span>
        我的处方
      </button>
      <button
        @click="activeFunction = 'create'"
        class="function-tab"
        :class="{ active: activeFunction === 'create' }"
      >
        <span class="tab-icon">➕</span>
        开具处方
      </button>
    </div>

    <!-- 处方列表 -->
    <div v-if="activeFunction === 'prescriptions'" class="prescription-list">
      <div class="list-header">
        <h2>我的处方</h2>
        <button @click="activeFunction = 'create'" class="create-btn">
          <span class="btn-icon">➕</span>
          开具处方
        </button>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在加载处方记录...</p>
      </div>

      <!-- 处方列表内容 -->
      <div v-else-if="prescriptions.length > 0" class="prescription-cards">
        <div 
          v-for="prescription in prescriptions" 
          :key="prescription.id" 
          class="prescription-card"
          @click="viewPrescription(prescription)"
        >
          <div class="card-header">
            <div class="patient-info">
              <div class="patient-avatar">{{ prescription.patientName?.charAt(0) || 'P' }}</div>
              <div class="patient-details">
                <div class="patient-name">{{ prescription.patientName }}</div>
                <div class="prescription-date">{{ formatDate(prescription.createdAt) }}</div>
              </div>
            </div>
            <div class="prescription-id">#{{ prescription.id }}</div>
          </div>
          
          <div class="card-content">
            <div class="diagnosis">
              <strong>诊断：</strong>{{ prescription.diagnosis }}
            </div>
            <div class="medication-count">
              {{ prescription.medications?.length || 0 }} 种药品
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">📋</div>
        <h3>还没有开具处方</h3>
        <p>点击"开具处方"为患者开具电子处方</p>
        <button @click="activeFunction = 'create'" class="empty-action-btn">
          开具处方
        </button>
      </div>
    </div>

    <!-- 开具处方页面 -->
    <div v-else-if="activeFunction === 'create'" class="create-prescription">
      <div class="list-header">
        <h2>开具处方</h2>
        <p class="header-desc">为患者开具电子处方</p>
      </div>

      <div class="prescription-form">
        <!-- 患者选择 -->
        <div class="form-section">
          <h3>患者信息</h3>
          <div class="form-group">
            <label>选择患者健康档案</label>
            <select v-model="prescriptionForm.profileId" class="form-select" @change="loadPatientInfo">
              <option value="">请选择患者</option>
              <option
                v-for="profile in healthProfiles"
                :key="profile.id"
                :value="profile.id"
              >
                {{ profile.profile_owner_name }} - {{ profile.id_card_number || '无身份证号' }}
              </option>
            </select>
          </div>
          
          <div v-if="selectedPatient" class="patient-info-card">
            <div class="patient-avatar-large">{{ selectedPatient.profile_owner_name?.charAt(0) || 'P' }}</div>
            <div class="patient-details">
              <div class="patient-name">{{ selectedPatient.profile_owner_name }}</div>
              <div class="patient-info">性别：{{ selectedPatient.gender || '未设置' }}</div>
              <div class="patient-info">年龄：{{ calculateAge(selectedPatient.birth_date) }}</div>
              <div class="patient-info">病史：{{ selectedPatient.medical_history || '无' }}</div>
            </div>
          </div>
        </div>

        <!-- 诊断信息 -->
        <div class="form-section">
          <h3>诊断信息</h3>
          <div class="form-group">
            <label>临床诊断</label>
            <textarea
              v-model="prescriptionForm.diagnosis"
              class="form-textarea"
              placeholder="请输入详细的临床诊断..."
              rows="3"
            ></textarea>
          </div>
        </div>

        <!-- 药品信息 -->
        <div class="form-section">
          <h3>药品信息</h3>
          <div 
            v-for="(medication, index) in prescriptionForm.medications" 
            :key="index"
            class="medication-item"
          >
            <div class="medication-header">
              <h4>药品 {{ index + 1 }}</h4>
              <button 
                v-if="prescriptionForm.medications.length > 1"
                @click="removeMedication(index)" 
                class="remove-btn"
              >
                删除
              </button>
            </div>
            
            <div class="medication-form">
              <div class="form-row">
                <div class="form-group">
                  <label>药品名称</label>
                  <input
                    v-model="medication.name"
                    type="text"
                    class="form-input"
                    placeholder="请输入药品名称"
                  >
                </div>
                <div class="form-group">
                  <label>规格</label>
                  <input
                    v-model="medication.specification"
                    type="text"
                    class="form-input"
                    placeholder="如：500mg"
                  >
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label>数量</label>
                  <input
                    v-model.number="medication.quantity"
                    type="number"
                    class="form-input"
                    placeholder="数量"
                    min="1"
                  >
                </div>
                <div class="form-group">
                  <label>用药频次</label>
                  <input
                    v-model="medication.frequency"
                    type="text"
                    class="form-input"
                    placeholder="如：每日3次"
                  >
                </div>
              </div>
              
              <div class="form-group">
                <label>用法用量</label>
                <input
                  v-model="medication.dosage"
                  type="text"
                  class="form-input"
                  placeholder="如：每次1片，饭后服用"
                >
              </div>
              
              <div class="form-group">
                <label>备注</label>
                <input
                  v-model="medication.notes"
                  type="text"
                  class="form-input"
                  placeholder="特殊说明（可选）"
                >
              </div>
            </div>
          </div>
          
          <button @click="addMedication" class="add-medication-btn">
            <span class="btn-icon">➕</span>
            添加药品
          </button>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <button @click="activeFunction = 'prescriptions'" class="cancel-btn">取消</button>
          <button 
            @click="createPrescription" 
            class="submit-btn"
            :disabled="!canSubmit || creating"
          >
            {{ creating ? '开具中...' : '开具处方' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 处方详情对话框 -->
    <div v-if="showDetailDialog" class="dialog-overlay" @click="closeDetailDialog">
      <div class="detail-dialog" @click.stop>
        <div class="dialog-header">
          <h3>处方详情</h3>
          <button @click="closeDetailDialog" class="close-btn">×</button>
        </div>
        
        <div class="dialog-body" v-if="currentPrescription">
          <!-- 患者信息 -->
          <div class="detail-section">
            <h4>患者信息</h4>
            <div class="detail-info">
              <div class="info-item">
                <span class="info-label">姓名：</span>
                <span class="info-value">{{ currentPrescription.patientName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">开具时间：</span>
                <span class="info-value">{{ formatDateTime(currentPrescription.createdAt) }}</span>
              </div>
            </div>
          </div>

          <!-- 诊断信息 -->
          <div class="detail-section">
            <h4>临床诊断</h4>
            <div class="diagnosis-text">{{ currentPrescription.diagnosis }}</div>
          </div>

          <!-- 药品信息 -->
          <div class="detail-section">
            <h4>药品信息</h4>
            <div class="medications-list">
              <div 
                v-for="(medication, index) in currentPrescription.medications" 
                :key="index"
                class="medication-detail"
              >
                <div class="medication-name">{{ index + 1 }}. {{ medication.name }}</div>
                <div class="medication-info">
                  <div class="info-row">
                    <span>规格：{{ medication.specification }}</span>
                    <span>数量：{{ medication.quantity }}</span>
                  </div>
                  <div class="info-row">
                    <span>频次：{{ medication.frequency }}</span>
                  </div>
                  <div class="info-row">
                    <span>用法：{{ medication.dosage }}</span>
                  </div>
                  <div v-if="medication.notes" class="info-row">
                    <span>备注：{{ medication.notes }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import { 
  createPrescription as createPrescriptionApi,
  getDoctorPrescriptions
} from '@/api/prescription'
import { getHealthProfiles } from '@/api/health'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const creating = ref(false)
const prescriptions = ref([])
const healthProfiles = ref([])
const activeFunction = ref('prescriptions')

// 处方表单
const prescriptionForm = ref({
  profileId: '',
  consultationId: null,
  diagnosis: '',
  medications: [
    {
      name: '',
      specification: '',
      quantity: 1,
      frequency: '',
      dosage: '',
      notes: ''
    }
  ]
})

// 选中的患者信息
const selectedPatient = ref(null)

// 处方详情对话框
const showDetailDialog = ref(false)
const currentPrescription = ref(null)

// 计算属性
const totalPrescriptions = computed(() => prescriptions.value.length)
const todayPrescriptions = computed(() => {
  const today = new Date().toDateString()
  return prescriptions.value.filter(p => 
    new Date(p.createdAt).toDateString() === today
  ).length
})
const totalPatients = computed(() => {
  const patientIds = new Set(prescriptions.value.map(p => p.profileId))
  return patientIds.size
})

const canSubmit = computed(() => {
  return prescriptionForm.value.profileId &&
         prescriptionForm.value.diagnosis.trim() &&
         prescriptionForm.value.medications.every(m =>
           m.name.trim() && m.specification.trim() &&
           m.quantity > 0 && m.frequency.trim() && m.dosage.trim()
         )
})

// 方法
const loadPrescriptions = async () => {
  loading.value = true
  try {
    const response = await getDoctorPrescriptions({ page: 1, size: 50 })
    if (response.data.code === 200) {
      prescriptions.value = response.data.data.content || []
    }
  } catch (error) {
    console.error('加载处方列表失败:', error)
  } finally {
    loading.value = false
  }
}

const loadHealthProfiles = async () => {
  try {
    const response = await getHealthProfiles({ page: 1, size: 100 })
    if (response.data.code === 200) {
      healthProfiles.value = response.data.data.content || []
    }
  } catch (error) {
    console.error('加载健康档案失败:', error)
  }
}

const loadPatientInfo = () => {
  const profileId = prescriptionForm.value.profileId
  if (profileId) {
    selectedPatient.value = healthProfiles.value.find(p => p.id == profileId)
  } else {
    selectedPatient.value = null
  }
}

const addMedication = () => {
  prescriptionForm.value.medications.push({
    name: '',
    specification: '',
    quantity: 1,
    frequency: '',
    dosage: '',
    notes: ''
  })
}

const removeMedication = (index) => {
  prescriptionForm.value.medications.splice(index, 1)
}

const createPrescription = async () => {
  if (!canSubmit.value) return

  creating.value = true
  try {
    const response = await createPrescriptionApi(prescriptionForm.value)
    if (response.data.code === 200) {
      alert('处方开具成功！')
      // 重置表单
      prescriptionForm.value = {
        profileId: '',
        consultationId: null,
        diagnosis: '',
        medications: [
          {
            name: '',
            specification: '',
            quantity: 1,
            frequency: '',
            dosage: '',
            notes: ''
          }
        ]
      }
      selectedPatient.value = null
      activeFunction.value = 'prescriptions'
      loadPrescriptions()
    }
  } catch (error) {
    console.error('开具处方失败:', error)
    alert('开具处方失败，请重试')
  } finally {
    creating.value = false
  }
}

const viewPrescription = (prescription) => {
  currentPrescription.value = prescription
  showDetailDialog.value = true
}

const closeDetailDialog = () => {
  showDetailDialog.value = false
  currentPrescription.value = null
}

const calculateAge = (birthDate) => {
  if (!birthDate) return '未知'
  const today = new Date()
  const birth = new Date(birthDate)
  const age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    return age - 1 + '岁'
  }
  return age + '岁'
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  // 检查用户权限
  if (!userStore.isDoctor) {
    router.push('/login')
    return
  }

  loadPrescriptions()
  loadHealthProfiles()
})
</script>

<style scoped>
.doctor-prescription {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 32px;
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.page-description {
  color: #64748b;
  font-size: 16px;
  margin: 0 0 24px 0;
}

.stats-summary {
  display: flex;
  justify-content: center;
  gap: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #10b981;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 功能选择标签 */
.function-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  background: white;
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.function-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background: transparent;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.function-tab:hover {
  background: #f1f5f9;
  color: #10b981;
}

.function-tab.active {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.tab-icon {
  font-size: 18px;
}

/* 列表头部 */
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.list-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.header-desc {
  color: #64748b;
  font-size: 16px;
  margin: 8px 0 0 0;
}

.create-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.btn-icon {
  font-size: 18px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #10b981;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 处方卡片 */
.prescription-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.prescription-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.prescription-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #10b981;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.patient-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
}

.patient-details {
  flex: 1;
}

.patient-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.prescription-date {
  font-size: 14px;
  color: #64748b;
}

.prescription-id {
  font-size: 12px;
  color: #10b981;
  font-weight: 600;
  background: #dcfce7;
  padding: 4px 8px;
  border-radius: 12px;
}

.card-content {
  border-top: 1px solid #f1f5f9;
  padding-top: 16px;
}

.diagnosis {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.medication-count {
  font-size: 12px;
  color: #10b981;
  font-weight: 600;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.empty-state p {
  color: #64748b;
  margin: 0 0 24px 0;
}

.empty-action-btn {
  padding: 12px 24px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.empty-action-btn:hover {
  background: #059669;
}

/* 处方表单 */
.prescription-form {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 32px;
}

.form-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #f1f5f9;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

/* 患者信息卡片 */
.patient-info-card {
  display: flex;
  align-items: center;
  gap: 16px;
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  margin-top: 16px;
}

.patient-avatar-large {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 24px;
}

.patient-details .patient-name {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.patient-info {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 4px;
}

/* 药品项目 */
.medication-item {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
}

.medication-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.medication-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.remove-btn {
  padding: 6px 12px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background: #dc2626;
}

.add-medication-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #f1f5f9;
  color: #10b981;
  border: 2px dashed #10b981;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  justify-content: center;
}

.add-medication-btn:hover {
  background: #dcfce7;
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #f1f5f9;
}

.cancel-btn {
  padding: 12px 24px;
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background: #f1f5f9;
  border-color: #9ca3af;
}

.submit-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 对话框 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.detail-dialog {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #f1f5f9;
}

.dialog-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  background: #f8fafc;
  border: none;
  border-radius: 50%;
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f1f5f9;
  color: #374151;
}

.dialog-body {
  padding: 32px;
  max-height: 60vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f1f5f9;
}

.detail-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-weight: 600;
  color: #64748b;
  margin-right: 8px;
}

.info-value {
  color: #1e293b;
}

.diagnosis-text {
  background: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #10b981;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
}

.medications-list {
  space-y: 16px;
}

.medication-detail {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.medication-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.medication-info {
  font-size: 14px;
  color: #64748b;
}

.info-row {
  display: flex;
  gap: 24px;
  margin-bottom: 4px;
}

.info-row span {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .doctor-prescription {
    padding: 16px;
  }

  .prescription-cards {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .stats-summary {
    flex-direction: column;
    gap: 20px;
  }

  .detail-info {
    grid-template-columns: 1fr;
  }

  .info-row {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
