<template>
  <div class="consultation-test">
    <div class="test-header">
      <h1>在线问诊API测试</h1>
      <p>测试在线问诊相关的API接口</p>
    </div>

    <div class="test-sections">
      <!-- 创建问诊测试 -->
      <div class="test-section">
        <h2>1. 创建问诊会话 (居民)</h2>
        <div class="test-form">
          <div class="form-group">
            <label>医生ID:</label>
            <input v-model="createForm.doctorId" type="number" placeholder="输入医生ID">
          </div>
          <div class="form-group">
            <label>初始消息:</label>
            <textarea v-model="createForm.initialMessage" placeholder="描述症状"></textarea>
          </div>
          <button @click="testCreateConsultation" :disabled="testing.create">
            {{ testing.create ? '测试中...' : '创建问诊' }}
          </button>
        </div>
        <div class="test-result">
          <h4>响应结果:</h4>
          <pre>{{ results.create }}</pre>
        </div>
      </div>

      <!-- 获取问诊列表测试 -->
      <div class="test-section">
        <h2>2. 获取问诊列表</h2>
        <div class="test-form">
          <button @click="testGetConsultations" :disabled="testing.list">
            {{ testing.list ? '测试中...' : '获取问诊列表' }}
          </button>
        </div>
        <div class="test-result">
          <h4>响应结果:</h4>
          <pre>{{ results.list }}</pre>
        </div>
      </div>

      <!-- 发送消息测试 -->
      <div class="test-section">
        <h2>3. 发送消息</h2>
        <div class="test-form">
          <div class="form-group">
            <label>问诊ID:</label>
            <input v-model="messageForm.consultationId" type="number" placeholder="输入问诊ID">
          </div>
          <div class="form-group">
            <label>消息内容:</label>
            <textarea v-model="messageForm.content" placeholder="输入消息内容"></textarea>
          </div>
          <button @click="testSendMessage" :disabled="testing.message">
            {{ testing.message ? '测试中...' : '发送消息' }}
          </button>
        </div>
        <div class="test-result">
          <h4>响应结果:</h4>
          <pre>{{ results.message }}</pre>
        </div>
      </div>

      <!-- 获取消息历史测试 -->
      <div class="test-section">
        <h2>4. 获取消息历史</h2>
        <div class="test-form">
          <div class="form-group">
            <label>问诊ID:</label>
            <input v-model="historyForm.consultationId" type="number" placeholder="输入问诊ID">
          </div>
          <button @click="testGetMessages" :disabled="testing.history">
            {{ testing.history ? '测试中...' : '获取消息历史' }}
          </button>
        </div>
        <div class="test-result">
          <h4>响应结果:</h4>
          <pre>{{ results.history }}</pre>
        </div>
      </div>

      <!-- 完成问诊测试 -->
      <div class="test-section">
        <h2>5. 完成问诊 (医生)</h2>
        <div class="test-form">
          <div class="form-group">
            <label>问诊ID:</label>
            <input v-model="completeForm.consultationId" type="number" placeholder="输入问诊ID">
          </div>
          <button @click="testCompleteConsultation" :disabled="testing.complete">
            {{ testing.complete ? '测试中...' : '完成问诊' }}
          </button>
        </div>
        <div class="test-result">
          <h4>响应结果:</h4>
          <pre>{{ results.complete }}</pre>
        </div>
      </div>
    </div>

    <!-- 导航按钮 -->
    <div class="navigation">
      <button @click="goToResidentConsultation" class="nav-btn">
        居民问诊页面
      </button>
      <button @click="goToDoctorConsultation" class="nav-btn">
        医生问诊页面
      </button>
      <button @click="goBack" class="nav-btn secondary">
        返回首页
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  createConsultation,
  getConsultations,
  sendMessage,
  getMessages,
  completeConsultation
} from '@/api/consultation'

const router = useRouter()

// 响应式数据
const testing = ref({
  create: false,
  list: false,
  message: false,
  history: false,
  complete: false
})

const results = ref({
  create: '',
  list: '',
  message: '',
  history: '',
  complete: ''
})

const createForm = ref({
  doctorId: 7,
  initialMessage: '医生您好，我最近感觉头痛，想咨询一下可能的原因和治疗建议。'
})

const messageForm = ref({
  consultationId: 1,
  content: '补充一下，头痛主要是在太阳穴附近，持续了大概3天了，有时候还会感到恶心。'
})

const historyForm = ref({
  consultationId: 1
})

const completeForm = ref({
  consultationId: 1
})

// 测试方法
const testCreateConsultation = async () => {
  testing.value.create = true
  try {
    const response = await createConsultation(createForm.value)
    results.value.create = JSON.stringify(response.data, null, 2)
  } catch (error) {
    results.value.create = JSON.stringify({
      error: error.message,
      response: error.response?.data
    }, null, 2)
  } finally {
    testing.value.create = false
  }
}

const testGetConsultations = async () => {
  testing.value.list = true
  try {
    const response = await getConsultations({ page: 1, size: 10 })
    results.value.list = JSON.stringify(response.data, null, 2)
  } catch (error) {
    results.value.list = JSON.stringify({
      error: error.message,
      response: error.response?.data
    }, null, 2)
  } finally {
    testing.value.list = false
  }
}

const testSendMessage = async () => {
  testing.value.message = true
  try {
    const response = await sendMessage(messageForm.value.consultationId, {
      content: messageForm.value.content
    })
    results.value.message = JSON.stringify(response.data, null, 2)
  } catch (error) {
    results.value.message = JSON.stringify({
      error: error.message,
      response: error.response?.data
    }, null, 2)
  } finally {
    testing.value.message = false
  }
}

const testGetMessages = async () => {
  testing.value.history = true
  try {
    const response = await getMessages(historyForm.value.consultationId, { page: 1, size: 20 })
    results.value.history = JSON.stringify(response.data, null, 2)
  } catch (error) {
    results.value.history = JSON.stringify({
      error: error.message,
      response: error.response?.data
    }, null, 2)
  } finally {
    testing.value.history = false
  }
}

const testCompleteConsultation = async () => {
  testing.value.complete = true
  try {
    const response = await completeConsultation(completeForm.value.consultationId)
    results.value.complete = JSON.stringify(response.data, null, 2)
  } catch (error) {
    results.value.complete = JSON.stringify({
      error: error.message,
      response: error.response?.data
    }, null, 2)
  } finally {
    testing.value.complete = false
  }
}

// 导航方法
const goToResidentConsultation = () => {
  router.push('/consultations')
}

const goToDoctorConsultation = () => {
  router.push('/doctor/consultations')
}

const goBack = () => {
  router.push('/')
}
</script>

<style scoped>
.consultation-test {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.test-header p {
  color: #64748b;
  font-size: 16px;
  margin: 0;
}

.test-sections {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.test-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 8px;
}

.test-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
  min-height: 80px;
  resize: vertical;
}

button {
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

button:hover:not(:disabled) {
  background: #2563eb;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.test-result {
  border-top: 1px solid #e2e8f0;
  padding-top: 16px;
}

.test-result h4 {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.test-result pre {
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  font-size: 12px;
  color: #374151;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 300px;
  overflow-y: auto;
}

.navigation {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}

.nav-btn {
  padding: 12px 24px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-btn.secondary {
  background: #6b7280;
}

.nav-btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .consultation-test {
    padding: 16px;
  }
  
  .navigation {
    flex-direction: column;
  }
}
</style>
