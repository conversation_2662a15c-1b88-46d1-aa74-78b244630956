<template>
  <div class="create-health-profile">
    <div class="page-header">
      <h1>创建健康档案</h1>
      <p>请填写健康档案信息，用于预约挂号</p>
    </div>

    <div class="form-container">
      <form @submit.prevent="createProfile" class="profile-form">
        <div class="form-group">
          <label for="profileOwnerName">姓名 *</label>
          <input
            type="text"
            id="profileOwnerName"
            v-model="formData.profileOwnerName"
            required
            placeholder="请输入姓名"
            class="form-input"
          >
        </div>

        <div class="form-group">
          <label for="gender">性别</label>
          <select id="gender" v-model="formData.gender" class="form-select">
            <option value="">请选择性别</option>
            <option value="male">男</option>
            <option value="female">女</option>
            <option value="other">其他</option>
          </select>
        </div>

        <div class="form-group">
          <label for="birthDate">出生日期</label>
          <input
            type="date"
            id="birthDate"
            v-model="formData.birthDate"
            class="form-input"
          >
        </div>

        <div class="form-group">
          <label for="idCard">身份证号</label>
          <input
            type="text"
            id="idCard"
            v-model="formData.idCard"
            placeholder="请输入身份证号"
            class="form-input"
          >
        </div>

        <div class="form-group">
          <label for="medicalHistory">既往病史</label>
          <textarea
            id="medicalHistory"
            v-model="formData.medicalHistory"
            placeholder="请输入既往病史（可选）"
            rows="4"
            class="form-textarea"
          ></textarea>
        </div>

        <div class="form-actions">
          <button type="button" @click="goBack" class="btn-secondary">返回</button>
          <button type="submit" :disabled="submitting" class="btn-primary">
            {{ submitting ? '创建中...' : '创建档案' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import * as healthApi from '@/api/health'

export default {
  name: 'CreateHealthProfile',
  setup() {
    const router = useRouter()
    const submitting = ref(false)

    const formData = reactive({
      profileOwnerName: '',
      gender: '',
      birthDate: '',
      idCard: '',
      medicalHistory: ''
    })

    const createProfile = async () => {
      if (!formData.profileOwnerName.trim()) {
        alert('请输入姓名')
        return
      }

      try {
        submitting.value = true

        const profileData = {
          profileOwnerName: formData.profileOwnerName.trim(),
          gender: formData.gender || null,
          birthDate: formData.birthDate || null,
          idCard: formData.idCard.trim() || null,
          medicalHistory: formData.medicalHistory.trim() || null
        }

        console.log('创建健康档案数据:', profileData)
        const response = await healthApi.createHealthProfile(profileData)
        console.log('创建健康档案响应:', response)

        if (response.data.code === 200) {
          alert('健康档案创建成功！')
          router.push('/profile')
        } else {
          alert('创建失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('创建健康档案失败:', error)
        alert('创建失败: ' + (error.response?.data?.message || error.message))
      } finally {
        submitting.value = false
      }
    }

    const goBack = () => {
      router.go(-1)
    }

    return {
      formData,
      submitting,
      createProfile,
      goBack
    }
  }
}
</script>

<style scoped>
.create-health-profile {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 32px;
  font-weight: 700;
}

.page-header p {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
}

.form-container {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  max-width: 600px;
  margin: 0 auto;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.form-input, .form-select, .form-textarea {
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  outline: none;
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 20px;
}

.btn-primary, .btn-secondary {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-primary {
  background: #4A90E2;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #357abd;
}

.btn-primary:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

@media (max-width: 768px) {
  .form-container {
    padding: 20px;
    margin: 0 10px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn-primary, .btn-secondary {
    width: 100%;
  }
}
</style>
