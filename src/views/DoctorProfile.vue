<template>
  <div class="doctor-profile-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1>👨‍⚕️ 个人信息管理</h1>
          <p class="header-subtitle">管理您的个人资料和账户设置</p>
        </div>
        <div class="header-actions">
          <button @click="refreshData" class="refresh-btn" :disabled="loading">
            <span class="refresh-icon" :class="{ 'spinning': loading }">🔄</span>
            刷新
          </button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧信息卡片 -->
      <div class="profile-sidebar">
        <!-- 医生基本信息卡片 -->
        <div class="info-card doctor-info-card">
          <div class="card-header">
            <h3>👤 基本信息</h3>
            <span class="status-badge" :class="statusClass">
              {{ statusText }}
            </span>
          </div>
          
          <div class="doctor-avatar-section">
            <div class="doctor-avatar" :style="avatarStyle">
              <span v-if="!userInfo?.avatarUrl" class="avatar-placeholder">
                {{ userInfo?.nickname?.charAt(0) || userInfo?.realName?.charAt(0) || 'D' }}
              </span>
            </div>
            <div class="doctor-basic-info">
              <h4>{{ userInfo?.realName || '未设置' }}</h4>
              <p class="nickname">{{ userInfo?.nickname || '未设置' }}</p>
              <p class="department">{{ userInfo?.departmentName || '未分配科室' }}</p>
              <p class="title">{{ userInfo?.title || '未设置职称' }}</p>
            </div>
          </div>

          <div class="info-details">
            <div class="info-item">
              <span class="label">手机号:</span>
              <span class="value">{{ userInfo?.phoneNumber || '未设置' }}</span>
            </div>
            <div class="info-item">
              <span class="label">性别:</span>
              <span class="value">{{ genderText || '未设置' }}</span>
            </div>
            <div class="info-item">
              <span class="label">年龄:</span>
              <span class="value">{{ ageText || '未设置' }}</span>
            </div>
            <div class="info-item">
              <span class="label">身份证号:</span>
              <span class="value">{{ maskedIdCard || '未设置' }}</span>
            </div>
          </div>

          <div class="card-actions">
            <button @click="showProfileForm = true" class="edit-btn">
              ✏️ 编辑资料
            </button>
            <button @click="showPasswordForm = true" class="password-btn">
              🔒 修改密码
            </button>
          </div>
        </div>

        <!-- 专业信息卡片 -->
        <div class="info-card professional-info-card">
          <div class="card-header">
            <h3>🏥 专业信息</h3>
          </div>
          
          <div class="professional-details">
            <div class="info-item">
              <span class="label">科室:</span>
              <span class="value">{{ userInfo?.departmentName || '未分配' }}</span>
            </div>
            <div class="info-item">
              <span class="label">职称:</span>
              <span class="value">{{ userInfo?.title || '未设置' }}</span>
            </div>
            <div class="info-item">
              <span class="label">专长:</span>
              <span class="value">{{ userInfo?.specialty || '未设置' }}</span>
            </div>
            <div class="info-item full-width">
              <span class="label">个人简介:</span>
              <span class="value bio">{{ userInfo?.bio || '未设置个人简介' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧操作区域 -->
      <div class="profile-main">
        <!-- 快捷操作卡片 -->
        <div class="info-card quick-actions-card">
          <div class="card-header">
            <h3>⚡ 快捷操作</h3>
          </div>
          
          <div class="quick-actions-grid">
            <button @click="showProfileForm = true" class="action-card">
              <div class="action-icon">👤</div>
              <div class="action-content">
                <h4>编辑个人资料</h4>
                <p>修改基本信息、联系方式等</p>
              </div>
            </button>
            
            <button @click="showPasswordForm = true" class="action-card">
              <div class="action-icon">🔐</div>
              <div class="action-content">
                <h4>修改登录密码</h4>
                <p>更改账户登录密码</p>
              </div>
            </button>
            
            <button @click="viewSchedule" class="action-card">
              <div class="action-icon">📅</div>
              <div class="action-content">
                <h4>查看排班信息</h4>
                <p>管理出诊时间安排</p>
              </div>
            </button>
            
            <button @click="viewAppointments" class="action-card">
              <div class="action-icon">📋</div>
              <div class="action-content">
                <h4>预约管理</h4>
                <p>查看和管理患者预约</p>
              </div>
            </button>
          </div>
        </div>

        <!-- 最近活动卡片 -->
        <div class="info-card activity-card">
          <div class="card-header">
            <h3>📊 账户统计</h3>
          </div>
          
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-number">{{ stats.totalAppointments }}</div>
              <div class="stat-label">总预约数</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.todayAppointments }}</div>
              <div class="stat-label">今日预约</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.completedAppointments }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.patientCount }}</div>
              <div class="stat-label">服务患者</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 成功消息提示 -->
    <div v-if="successMessage" class="success-message">
      <div class="message-content">
        <span class="success-icon">✅</span>
        {{ successMessage }}
      </div>
    </div>

    <!-- 医生个人信息编辑表单 -->
    <DoctorProfileForm
      v-if="showProfileForm"
      @close="showProfileForm = false"
      @success="handleProfileSuccess"
    />

    <!-- 医生密码修改表单 -->
    <DoctorChangePasswordForm
      v-if="showPasswordForm"
      @close="showPasswordForm = false"
      @success="handlePasswordSuccess"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import { getUserInfo } from '@/api/user'
import DoctorProfileForm from '@/components/DoctorProfileForm.vue'
import DoctorChangePasswordForm from '@/components/DoctorChangePasswordForm.vue'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const userInfo = ref(null)
const showProfileForm = ref(false)
const showPasswordForm = ref(false)
const successMessage = ref('')

// 模拟统计数据
const stats = ref({
  totalAppointments: 156,
  todayAppointments: 8,
  completedAppointments: 142,
  patientCount: 89
})

// 计算属性
const statusClass = computed(() => {
  const status = userInfo.value?.doctorStatus
  switch (status) {
    case 'APPROVED': return 'status-approved'
    case 'PENDING': return 'status-pending'
    case 'REJECTED': return 'status-rejected'
    default: return 'status-unknown'
  }
})

const statusText = computed(() => {
  const status = userInfo.value?.doctorStatus
  switch (status) {
    case 'APPROVED': return '已认证'
    case 'PENDING': return '待审核'
    case 'REJECTED': return '已拒绝'
    default: return '未知状态'
  }
})

const avatarStyle = computed(() => {
  return userInfo.value?.avatarUrl 
    ? { backgroundImage: `url(${userInfo.value.avatarUrl})` }
    : {}
})

const genderText = computed(() => {
  const gender = userInfo.value?.gender
  switch (gender) {
    case 'MALE': return '男'
    case 'FEMALE': return '女'
    case 'OTHER': return '其他'
    default: return null
  }
})

const ageText = computed(() => {
  return userInfo.value?.age ? `${userInfo.value.age}岁` : null
})

const maskedIdCard = computed(() => {
  const idCard = userInfo.value?.idCardNumber
  if (!idCard) return null
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
})

// 方法
const loadUserInfo = async () => {
  loading.value = true
  try {
    const response = await getUserInfo()
    if (response.data.code === 200) {
      userInfo.value = response.data.data
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadUserInfo()
}

const handleProfileSuccess = (message) => {
  showProfileForm.value = false
  successMessage.value = message
  setTimeout(() => {
    successMessage.value = ''
  }, 3000)
  loadUserInfo() // 重新加载用户信息
}

const handlePasswordSuccess = (message) => {
  showPasswordForm.value = false
  successMessage.value = message
  setTimeout(() => {
    successMessage.value = ''
    // 密码修改成功后，可能需要重新登录
    if (message.includes('重新登录')) {
      setTimeout(() => {
        userStore.logout()
        router.push('/login')
      }, 2000)
    }
  }, 3000)
}

const viewSchedule = () => {
  // 跳转到排班管理页面
  router.push('/doctor/schedule')
}

const viewAppointments = () => {
  // 跳转到预约管理页面
  router.push('/doctor/appointments')
}

// 生命周期
onMounted(() => {
  loadUserInfo()
})
</script>

<style scoped>
.doctor-profile-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  padding: 20px;
}

.page-header {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  padding: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h1 {
  margin: 0 0 8px 0;
  color: #1e40af;
  font-size: 28px;
  font-weight: 700;
}

.header-subtitle {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.refresh-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.refresh-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.main-content {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 24px;
  align-items: start;
}

.info-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-bottom: 1px solid #e5e7eb;
}

.card-header h3 {
  margin: 0;
  color: #1e40af;
  font-size: 16px;
  font-weight: 600;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-approved {
  background: #dcfce7;
  color: #166534;
}

.status-pending {
  background: #fef3c7;
  color: #92400e;
}

.status-rejected {
  background: #fee2e2;
  color: #991b1b;
}

.doctor-avatar-section {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px;
  border-bottom: 1px solid #f3f4f6;
}

.doctor-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  font-weight: 600;
  flex-shrink: 0;
}

.doctor-basic-info h4 {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.doctor-basic-info p {
  margin: 2px 0;
  color: #6b7280;
  font-size: 14px;
}

.nickname {
  color: #3b82f6 !important;
  font-weight: 500;
}

.info-details {
  padding: 20px 24px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f9fafb;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item.full-width {
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.info-item .label {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.info-item .value {
  color: #1f2937;
  font-size: 14px;
}

.info-item .value.bio {
  line-height: 1.5;
  text-align: left;
}

.card-actions {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  background: #f9fafb;
}

.edit-btn,
.password-btn {
  flex: 1;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.edit-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
}

.edit-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.password-btn {
  background: white;
  color: #374151;
  border: 2px solid #e5e7eb;
}

.password-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 24px;
}

.action-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.action-card:hover {
  border-color: #3b82f6;
  background: #f8fafc;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.action-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.action-content h4 {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.action-content p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 24px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 12px;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.message-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: #dcfce7;
  color: #166534;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-weight: 500;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .doctor-profile-page {
    padding: 12px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .doctor-avatar-section {
    flex-direction: column;
    text-align: center;
  }
  
  .card-actions {
    flex-direction: column;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
