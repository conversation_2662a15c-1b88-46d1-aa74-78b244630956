<template>
  <div class="debug-api-page">
    <div class="page-header">
      <h1>API调试页面</h1>
      <p>用于调试健康档案和排班API的返回格式</p>
    </div>

    <div class="debug-sections">
      <!-- 健康档案API调试 -->
      <div class="debug-section">
        <h3>健康档案API调试</h3>
        <div class="debug-controls">
          <button @click="testHealthProfilesApi" :disabled="loading.healthProfiles" class="debug-btn">
            {{ loading.healthProfiles ? '测试中...' : '测试健康档案API' }}
          </button>
        </div>
        <div v-if="results.healthProfiles" class="debug-result">
          <h4>API响应结果:</h4>
          <pre>{{ JSON.stringify(results.healthProfiles, null, 2) }}</pre>
        </div>
      </div>

      <!-- 排班API调试 -->
      <div class="debug-section">
        <h3>排班API调试</h3>
        <div class="debug-controls">
          <input
            v-model="debugParams.doctorId"
            placeholder="医生ID (例如: 7)"
            class="debug-input"
          >
          <input
            v-model="debugParams.date"
            type="date"
            class="debug-input"
          >
          <button @click="testSchedulesApi" :disabled="loading.schedules" class="debug-btn">
            {{ loading.schedules ? '测试中...' : '测试排班API' }}
          </button>
        </div>
        <div v-if="results.schedules" class="debug-result">
          <h4>API响应结果:</h4>
          <pre>{{ JSON.stringify(results.schedules, null, 2) }}</pre>
        </div>
      </div>

      <!-- 可预约时段API调试 -->
      <div class="debug-section">
        <h3>可预约时段API调试</h3>
        <div class="debug-controls">
          <input
            v-model="debugParams.date"
            type="date"
            class="debug-input"
          >
          <input
            v-model="debugParams.departmentId"
            placeholder="科室ID (可选)"
            class="debug-input"
          >
          <button @click="testAvailableSchedulesApi" :disabled="loading.availableSchedules" class="debug-btn">
            {{ loading.availableSchedules ? '测试中...' : '测试可预约时段API' }}
          </button>
        </div>
        <div v-if="results.availableSchedules" class="debug-result">
          <h4>可预约时段API响应结果:</h4>
          <pre>{{ JSON.stringify(results.availableSchedules, null, 2) }}</pre>
        </div>
      </div>

      <!-- 医生列表API调试 -->
      <div class="debug-section">
        <h3>医生列表API调试</h3>
        <div class="debug-controls">
          <button @click="testDoctorsApi" :disabled="loading.doctors" class="debug-btn">
            {{ loading.doctors ? '测试中...' : '测试医生列表API' }}
          </button>
        </div>
        <div v-if="results.doctors" class="debug-result">
          <h4>API响应结果:</h4>
          <pre>{{ JSON.stringify(results.doctors, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import * as healthApi from '@/api/health'
import * as appointmentsApi from '@/api/appointments'

export default {
  name: 'DebugApiPage',
  setup() {
    const loading = reactive({
      healthProfiles: false,
      schedules: false,
      doctors: false,
      availableSchedules: false
    })

    const results = reactive({
      healthProfiles: null,
      schedules: null,
      doctors: null,
      availableSchedules: null
    })

    const debugParams = reactive({
      doctorId: '7',
      date: new Date().toISOString().split('T')[0],
      departmentId: ''
    })

    const testHealthProfilesApi = async () => {
      loading.healthProfiles = true
      try {
        console.log('开始测试健康档案API...')
        const response = await healthApi.getHealthProfiles()
        console.log('健康档案API原始响应:', response)
        results.healthProfiles = response.data
      } catch (error) {
        console.error('健康档案API测试失败:', error)
        results.healthProfiles = { 
          error: error.message,
          details: error.response?.data 
        }
      } finally {
        loading.healthProfiles = false
      }
    }

    const testSchedulesApi = async () => {
      if (!debugParams.doctorId || !debugParams.date) {
        alert('请填写医生ID和日期')
        return
      }

      loading.schedules = true
      try {
        console.log('开始测试排班API...', {
          doctorId: debugParams.doctorId,
          date: debugParams.date
        })

        // 测试多种参数格式
        const testCases = [
          {
            name: '格式1: startDate + endDate',
            params: {
              startDate: debugParams.date,
              endDate: debugParams.date
            }
          },
          {
            name: '格式2: date',
            params: {
              date: debugParams.date
            }
          },
          {
            name: '格式3: scheduleDate',
            params: {
              scheduleDate: debugParams.date
            }
          }
        ]

        const testResults = []

        for (const testCase of testCases) {
          try {
            console.log(`测试${testCase.name}:`, testCase.params)
            const response = await appointmentsApi.getDoctorSchedules(debugParams.doctorId, testCase.params)
            testResults.push({
              ...testCase,
              success: true,
              response: response.data
            })
            console.log(`${testCase.name} 成功:`, response.data)
          } catch (error) {
            testResults.push({
              ...testCase,
              success: false,
              error: error.message,
              details: error.response?.data
            })
            console.error(`${testCase.name} 失败:`, error)
          }
        }

        results.schedules = {
          testResults: testResults,
          summary: `测试了${testCases.length}种参数格式`
        }

      } catch (error) {
        console.error('排班API测试失败:', error)
        results.schedules = {
          error: error.message,
          details: error.response?.data
        }
      } finally {
        loading.schedules = false
      }
    }

    const testDoctorsApi = async () => {
      loading.doctors = true
      try {
        console.log('开始测试医生列表API...')
        const response = await appointmentsApi.searchDoctors({ page: 1, size: 10 })
        console.log('医生列表API原始响应:', response)
        results.doctors = response.data
      } catch (error) {
        console.error('医生列表API测试失败:', error)
        results.doctors = {
          error: error.message,
          details: error.response?.data
        }
      } finally {
        loading.doctors = false
      }
    }

    const testAvailableSchedulesApi = async () => {
      loading.availableSchedules = true
      try {
        console.log('开始测试可预约时段API...', {
          date: debugParams.date,
          departmentId: debugParams.departmentId
        })

        const params = {
          date: debugParams.date
        }

        if (debugParams.departmentId) {
          params.departmentId = debugParams.departmentId
        }

        const response = await appointmentsApi.getAvailableSchedules(params)
        console.log('可预约时段API原始响应:', response)
        results.availableSchedules = response.data
      } catch (error) {
        console.error('可预约时段API测试失败:', error)
        results.availableSchedules = {
          error: error.message,
          details: error.response?.data
        }
      } finally {
        loading.availableSchedules = false
      }
    }

    // 页面加载时自动测试健康档案API
    onMounted(() => {
      testHealthProfilesApi()
    })

    return {
      loading,
      results,
      debugParams,
      testHealthProfilesApi,
      testSchedulesApi,
      testDoctorsApi,
      testAvailableSchedulesApi
    }
  }
}
</script>

<style scoped>
.debug-api-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 32px;
  font-weight: 700;
}

.page-header p {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
}

.debug-sections {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.debug-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.debug-section h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.debug-controls {
  display: flex;
  gap: 15px;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.debug-btn {
  padding: 10px 20px;
  background: #4A90E2;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.3s;
}

.debug-btn:hover:not(:disabled) {
  background: #357abd;
}

.debug-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.debug-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 150px;
}

.debug-result {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
}

.debug-result h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.debug-result pre {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .debug-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .debug-input {
    min-width: auto;
  }
}
</style>
