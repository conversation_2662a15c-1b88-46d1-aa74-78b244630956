<template>
  <div class="consultation-nav">
    <div class="nav-header">
      <h1>在线问诊功能导航</h1>
      <p>选择您要访问的功能页面</p>
    </div>

    <div class="user-info" v-if="userStore.isLoggedIn">
      <div class="info-card">
        <h3>当前用户信息</h3>
        <p><strong>用户名:</strong> {{ userStore.userInfo?.nickname || '未设置' }}</p>
        <p><strong>角色:</strong> {{ getRoleText() }}</p>
        <p><strong>权限:</strong> 
          <span v-if="userStore.isResident" class="role-badge resident">居民</span>
          <span v-if="userStore.isDoctor" class="role-badge doctor">医生</span>
          <span v-if="userStore.isAdmin" class="role-badge admin">管理员</span>
        </p>
      </div>
    </div>

    <div class="nav-sections">
      <!-- 居民功能 -->
      <div class="nav-section" v-if="userStore.isResident">
        <h2>🏠 居民功能</h2>
        <div class="nav-buttons">
          <button @click="goToResidentConsultation" class="nav-btn primary">
            <div class="btn-icon">💬</div>
            <div class="btn-content">
              <div class="btn-title">在线问诊</div>
              <div class="btn-desc">发起问诊，与医生交流</div>
            </div>
          </button>
          <button @click="goToResidentDashboard" class="nav-btn secondary">
            <div class="btn-icon">🏠</div>
            <div class="btn-content">
              <div class="btn-title">居民仪表板</div>
              <div class="btn-desc">返回主页面</div>
            </div>
          </button>
        </div>
      </div>

      <!-- 医生功能 -->
      <div class="nav-section" v-if="userStore.isDoctor">
        <h2>👨‍⚕️ 医生功能</h2>
        <div class="nav-buttons">
          <button @click="goToDoctorConsultation" class="nav-btn primary">
            <div class="btn-icon">💬</div>
            <div class="btn-content">
              <div class="btn-title">在线问诊管理</div>
              <div class="btn-desc">管理患者问诊，提供医疗建议</div>
            </div>
          </button>
          <button @click="goToDoctorDashboard" class="nav-btn secondary">
            <div class="btn-icon">🏥</div>
            <div class="btn-content">
              <div class="btn-title">医生工作台</div>
              <div class="btn-desc">返回工作台</div>
            </div>
          </button>
        </div>
      </div>

      <!-- 测试功能 -->
      <div class="nav-section">
        <h2>🧪 测试功能</h2>
        <div class="nav-buttons">
          <button @click="goToConsultationTest" class="nav-btn test">
            <div class="btn-icon">🔧</div>
            <div class="btn-content">
              <div class="btn-title">API接口测试</div>
              <div class="btn-desc">测试在线问诊相关API</div>
            </div>
          </button>
        </div>
      </div>

      <!-- 未登录提示 -->
      <div class="nav-section" v-if="!userStore.isLoggedIn">
        <h2>⚠️ 请先登录</h2>
        <div class="nav-buttons">
          <button @click="goToLogin" class="nav-btn primary">
            <div class="btn-icon">🔑</div>
            <div class="btn-content">
              <div class="btn-title">登录系统</div>
              <div class="btn-desc">登录后访问在线问诊功能</div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- 功能说明 -->
    <div class="feature-info">
      <h3>功能说明</h3>
      <div class="info-grid">
        <div class="info-item">
          <h4>👥 居民端</h4>
          <ul>
            <li>查看我的问诊记录</li>
            <li>发起新的问诊会话</li>
            <li>与医生实时聊天</li>
            <li>查看问诊状态</li>
          </ul>
        </div>
        <div class="info-item">
          <h4>👨‍⚕️ 医生端</h4>
          <ul>
            <li>查看所有患者问诊</li>
            <li>按状态筛选问诊</li>
            <li>回复患者消息</li>
            <li>完成问诊操作</li>
          </ul>
        </div>
        <div class="info-item">
          <h4>🔧 API测试</h4>
          <ul>
            <li>创建问诊会话</li>
            <li>发送和接收消息</li>
            <li>获取问诊列表</li>
            <li>完成问诊测试</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'

const userStore = useUserStore()
const router = useRouter()

const getRoleText = () => {
  const role = userStore.userRole
  switch (role) {
    case 'RESIDENT': return '居民'
    case 'DOCTOR': return '医生'
    case 'ADMIN': return '管理员'
    default: return '未知'
  }
}

// 导航方法
const goToResidentConsultation = () => {
  router.push('/consultations')
}

const goToDoctorConsultation = () => {
  router.push('/doctor/consultations')
}

const goToConsultationTest = () => {
  router.push('/consultation-test')
}

const goToResidentDashboard = () => {
  router.push('/')
}

const goToDoctorDashboard = () => {
  router.push('/doctor')
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.consultation-nav {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
}

.nav-header {
  text-align: center;
  margin-bottom: 32px;
  color: white;
}

.nav-header h1 {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.nav-header p {
  font-size: 18px;
  opacity: 0.9;
  margin: 0;
}

.user-info {
  max-width: 600px;
  margin: 0 auto 32px auto;
}

.info-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-card h3 {
  margin: 0 0 16px 0;
  font-size: 20px;
}

.info-card p {
  margin: 8px 0;
  font-size: 16px;
}

.role-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-right: 8px;
}

.role-badge.resident {
  background: #3b82f6;
  color: white;
}

.role-badge.doctor {
  background: #10b981;
  color: white;
}

.role-badge.admin {
  background: #f59e0b;
  color: white;
}

.nav-sections {
  max-width: 1000px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.nav-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-section h2 {
  color: white;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.nav-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.nav-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.nav-btn.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.nav-btn.secondary {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.nav-btn.test {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.btn-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.btn-content {
  flex: 1;
}

.btn-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.btn-desc {
  font-size: 14px;
  opacity: 0.8;
}

.feature-info {
  max-width: 1000px;
  margin: 32px auto 0 auto;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.feature-info h3 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 20px 0;
  text-align: center;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.info-item h4 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.info-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-item li {
  padding: 4px 0;
  font-size: 14px;
  opacity: 0.9;
}

.info-item li:before {
  content: "✓ ";
  color: #10b981;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .consultation-nav {
    padding: 16px;
  }
  
  .nav-buttons {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
