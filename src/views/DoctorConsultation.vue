<template>
  <div class="doctor-consultation">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>在线问诊</h1>
      <p class="page-description">管理患者问诊，提供专业医疗建议</p>
      <div class="stats-summary">
        <div class="stat-item">
          <div class="stat-number">{{ totalConsultations }}</div>
          <div class="stat-label">总问诊数</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ inProgressCount }}</div>
          <div class="stat-label">进行中</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ completedCount }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
    </div>

    <!-- 问诊列表 -->
    <div class="consultation-list">
      <div class="list-header">
        <h2>患者问诊</h2>
        <div class="filter-tabs">
          <button 
            v-for="tab in filterTabs" 
            :key="tab.value"
            @click="activeFilter = tab.value"
            class="filter-tab"
            :class="{ active: activeFilter === tab.value }"
          >
            {{ tab.label }}
            <span v-if="tab.count > 0" class="tab-count">{{ tab.count }}</span>
          </button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在加载问诊记录...</p>
      </div>

      <!-- 问诊列表内容 -->
      <div v-else-if="filteredConsultations.length > 0" class="consultation-cards">
        <div 
          v-for="consultation in filteredConsultations" 
          :key="consultation.id" 
          class="consultation-card"
          @click="openChat(consultation)"
        >
          <div class="card-header">
            <div class="patient-info">
              <div class="patient-avatar">{{ consultation.userNickname?.charAt(0) || 'P' }}</div>
              <div class="patient-details">
                <div class="patient-name">{{ consultation.userNickname }}</div>
                <div class="patient-phone">{{ consultation.userPhoneNumber }}</div>
              </div>
            </div>
            <div class="consultation-status" :class="consultation.status.toLowerCase()">
              {{ consultation.statusDescription }}
            </div>
          </div>
          
          <div class="card-content">
            <div class="last-message">{{ consultation.lastMessage }}</div>
            <div class="message-info">
              <span class="message-count">{{ consultation.messageCount }} 条消息</span>
              <span class="last-time">{{ formatTime(consultation.lastMessageTime) }}</span>
            </div>
          </div>

          <div class="card-actions" v-if="consultation.status === 'IN_PROGRESS'">
            <button @click.stop="openChat(consultation)" class="reply-btn">
              立即回复
            </button>
            <button @click.stop="completeConsultation(consultation)" class="complete-btn">
              完成问诊
            </button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">💬</div>
        <h3>{{ getEmptyStateTitle() }}</h3>
        <p>{{ getEmptyStateDescription() }}</p>
      </div>
    </div>

    <!-- 聊天对话框 -->
    <div v-if="showChatDialog" class="dialog-overlay" @click="closeChatDialog">
      <div class="chat-dialog" @click.stop>
        <div class="chat-header">
          <div class="chat-patient-info">
            <div class="patient-avatar">{{ currentConsultation?.userNickname?.charAt(0) || 'P' }}</div>
            <div class="patient-details">
              <div class="patient-name">{{ currentConsultation?.userNickname }}</div>
              <div class="patient-phone">{{ currentConsultation?.userPhoneNumber }}</div>
            </div>
          </div>
          <div class="chat-actions">
            <div class="chat-status" :class="currentConsultation?.status?.toLowerCase()">
              {{ currentConsultation?.statusDescription }}
            </div>
            <button 
              v-if="currentConsultation?.status === 'IN_PROGRESS'"
              @click="completeCurrentConsultation" 
              class="complete-chat-btn"
              :disabled="completing"
            >
              {{ completing ? '完成中...' : '完成问诊' }}
            </button>
          </div>
          <button @click="closeChatDialog" class="close-btn">×</button>
        </div>

        <div class="chat-messages" ref="messagesContainer">
          <div v-if="loadingMessages" class="loading-messages">
            <div class="loading-spinner small"></div>
            <span>加载消息中...</span>
          </div>
          
          <div 
            v-for="message in messages" 
            :key="message.id" 
            class="message-item"
            :class="{ 'own-message': message.senderRole === 'DOCTOR' }"
          >
            <div class="message-avatar">
              {{ message.senderNickname?.charAt(0) || 'U' }}
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="sender-name">{{ message.senderNickname }}</span>
                <span class="message-time">{{ formatTime(message.sentAt) }}</span>
              </div>
              <div class="message-text">{{ message.content }}</div>
            </div>
          </div>
        </div>

        <div class="chat-input" v-if="currentConsultation?.status === 'IN_PROGRESS'">
          <div class="input-container">
            <textarea 
              v-model="newMessage" 
              class="message-input"
              placeholder="输入您的专业建议..."
              rows="3"
              @keydown.enter.prevent="sendMessage"
            ></textarea>
            <div class="input-actions">
              <button 
                @click="sendMessage" 
                class="send-btn"
                :disabled="!newMessage.trim() || sending"
              >
                {{ sending ? '发送中...' : '发送' }}
              </button>
            </div>
          </div>
          
          <!-- 快捷回复 -->
          <div class="quick-replies">
            <span class="quick-label">快捷回复：</span>
            <button 
              v-for="reply in quickReplies" 
              :key="reply"
              @click="addQuickReply(reply)"
              class="quick-reply-btn"
            >
              {{ reply }}
            </button>
          </div>
        </div>

        <div v-else class="chat-completed">
          <div class="completed-notice">
            <span class="completed-icon">✅</span>
            <span>问诊已完成</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import { 
  getConsultations, 
  getMessages,
  sendMessage as sendMessageApi,
  completeConsultation as completeConsultationApi
} from '@/api/consultation'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const consultations = ref([])
const activeFilter = ref('all')

// 聊天相关
const showChatDialog = ref(false)
const currentConsultation = ref(null)
const messages = ref([])
const loadingMessages = ref(false)
const newMessage = ref('')
const sending = ref(false)
const completing = ref(false)
const messagesContainer = ref(null)

// 快捷回复
const quickReplies = ref([
  '请详细描述您的症状',
  '建议您到医院进一步检查',
  '注意休息，多喝水',
  '按时服药，定期复查',
  '如有不适请及时就医'
])

// 过滤标签
const filterTabs = computed(() => [
  { label: '全部', value: 'all', count: consultations.value.length },
  { label: '进行中', value: 'in_progress', count: inProgressCount.value },
  { label: '已完成', value: 'completed', count: completedCount.value }
])

// 计算属性
const totalConsultations = computed(() => consultations.value.length)
const inProgressCount = computed(() => 
  consultations.value.filter(c => c.status === 'IN_PROGRESS').length
)
const completedCount = computed(() => 
  consultations.value.filter(c => c.status === 'COMPLETED').length
)

const filteredConsultations = computed(() => {
  if (activeFilter.value === 'all') return consultations.value
  return consultations.value.filter(c => 
    c.status === activeFilter.value.toUpperCase()
  )
})

// 方法
const loadConsultations = async () => {
  loading.value = true
  try {
    const response = await getConsultations({ page: 1, size: 50 })
    if (response.data.code === 200) {
      consultations.value = response.data.data.content
    }
  } catch (error) {
    console.error('加载问诊列表失败:', error)
  } finally {
    loading.value = false
  }
}

const openChat = async (consultation) => {
  currentConsultation.value = consultation
  showChatDialog.value = true
  await loadMessages()
}

const loadMessages = async () => {
  if (!currentConsultation.value) return
  
  loadingMessages.value = true
  try {
    const response = await getMessages(currentConsultation.value.id, { page: 1, size: 50 })
    if (response.data.code === 200) {
      messages.value = response.data.data.content
      await nextTick()
      scrollToBottom()
    }
  } catch (error) {
    console.error('加载消息失败:', error)
  } finally {
    loadingMessages.value = false
  }
}

const sendMessage = async () => {
  if (!newMessage.value.trim() || !currentConsultation.value) return
  
  sending.value = true
  try {
    const response = await sendMessageApi(currentConsultation.value.id, {
      content: newMessage.value.trim()
    })
    if (response.data.code === 200) {
      messages.value.push(response.data.data)
      newMessage.value = ''
      await nextTick()
      scrollToBottom()
      // 更新问诊列表中的最后消息
      loadConsultations()
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    alert('发送消息失败，请重试')
  } finally {
    sending.value = false
  }
}

const completeConsultation = async (consultation) => {
  if (!confirm('确定要完成这个问诊吗？完成后将无法继续对话。')) return
  
  try {
    const response = await completeConsultationApi(consultation.id)
    if (response.data.code === 200) {
      // 更新本地数据
      const index = consultations.value.findIndex(c => c.id === consultation.id)
      if (index !== -1) {
        consultations.value[index].status = 'COMPLETED'
        consultations.value[index].statusDescription = '已完成'
      }
      alert('问诊已完成')
    }
  } catch (error) {
    console.error('完成问诊失败:', error)
    alert('完成问诊失败，请重试')
  }
}

const completeCurrentConsultation = async () => {
  if (!currentConsultation.value) return
  
  completing.value = true
  try {
    await completeConsultation(currentConsultation.value)
    currentConsultation.value.status = 'COMPLETED'
    currentConsultation.value.statusDescription = '已完成'
  } finally {
    completing.value = false
  }
}

const addQuickReply = (reply) => {
  if (newMessage.value) {
    newMessage.value += '\n' + reply
  } else {
    newMessage.value = reply
  }
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const closeChatDialog = () => {
  showChatDialog.value = false
  currentConsultation.value = null
  messages.value = []
  newMessage.value = ''
}

const getEmptyStateTitle = () => {
  switch (activeFilter.value) {
    case 'in_progress': return '暂无进行中的问诊'
    case 'completed': return '暂无已完成的问诊'
    default: return '暂无问诊记录'
  }
}

const getEmptyStateDescription = () => {
  switch (activeFilter.value) {
    case 'in_progress': return '当前没有需要处理的问诊'
    case 'completed': return '还没有完成的问诊记录'
    default: return '还没有患者向您发起问诊'
  }
}

const formatTime = (timeString) => {
  if (!timeString) return ''
  const date = new Date(timeString)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  // 检查用户权限
  if (!userStore.userInfo?.isDoctor) {
    router.push('/login')
    return
  }
  
  loadConsultations()
})
</script>

<style scoped>
.doctor-consultation {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 32px;
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.page-description {
  color: #64748b;
  font-size: 16px;
  margin: 0 0 24px 0;
}

.stats-summary {
  display: flex;
  justify-content: center;
  gap: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 问诊列表 */
.consultation-list {
  max-width: 1200px;
  margin: 0 auto;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.list-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.filter-tabs {
  display: flex;
  gap: 8px;
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-tab:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.filter-tab.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.tab-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
}

.filter-tab.active .tab-count {
  background: rgba(255, 255, 255, 0.3);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-spinner.small {
  width: 20px;
  height: 20px;
  border-width: 2px;
  margin: 0 8px 0 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 问诊卡片 */
.consultation-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.consultation-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.consultation-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #10b981;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.patient-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
}

.patient-details {
  flex: 1;
}

.patient-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.patient-phone {
  font-size: 14px;
  color: #64748b;
}

.consultation-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.consultation-status.in_progress {
  background: #fef3c7;
  color: #d97706;
}

.consultation-status.completed {
  background: #dcfce7;
  color: #16a34a;
}

.card-content {
  border-top: 1px solid #f1f5f9;
  padding-top: 16px;
  margin-bottom: 16px;
}

.last-message {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.message-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #9ca3af;
}

.card-actions {
  display: flex;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid #f1f5f9;
}

.reply-btn {
  flex: 1;
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reply-btn:hover {
  background: #2563eb;
}

.complete-btn {
  padding: 8px 16px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.complete-btn:hover {
  background: #059669;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.empty-state p {
  color: #64748b;
  margin: 0;
}

/* 对话框 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.chat-dialog {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 900px;
  height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #f1f5f9;
  background: #f8fafc;
}

.chat-patient-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.chat-status.in_progress {
  background: #fef3c7;
  color: #d97706;
}

.chat-status.completed {
  background: #dcfce7;
  color: #16a34a;
}

.complete-chat-btn {
  padding: 8px 16px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.complete-chat-btn:hover:not(:disabled) {
  background: #059669;
}

.complete-chat-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f1f5f9;
  border-radius: 50%;
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e2e8f0;
  color: #374151;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #f8fafc;
}

.loading-messages {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #64748b;
}

.message-item {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.message-item.own-message {
  flex-direction: row-reverse;
}

.message-item.own-message .message-content {
  background: #10b981;
  color: white;
}

.message-item.own-message .message-header .sender-name {
  color: rgba(255, 255, 255, 0.9);
}

.message-item.own-message .message-header .message-time {
  color: rgba(255, 255, 255, 0.7);
}

.message-avatar {
  width: 40px;
  height: 40px;
  background: #e5e7eb;
  color: #6b7280;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.own-message .message-avatar {
  background: #059669;
  color: white;
}

.message-content {
  background: white;
  border-radius: 16px;
  padding: 12px 16px;
  max-width: 70%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.sender-name {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.message-time {
  font-size: 11px;
  color: #9ca3af;
}

.message-text {
  font-size: 14px;
  line-height: 1.5;
  color: #1f2937;
  word-wrap: break-word;
}

.own-message .message-text {
  color: white;
}

.chat-input {
  padding: 20px;
  border-top: 1px solid #f1f5f9;
  background: white;
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
  margin-bottom: 16px;
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  font-size: 14px;
  font-family: inherit;
  resize: none;
  max-height: 120px;
  transition: border-color 0.2s ease;
}

.message-input:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.input-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.send-btn {
  padding: 12px 20px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.send-btn:hover:not(:disabled) {
  background: #059669;
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quick-replies {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 600;
  white-space: nowrap;
}

.quick-reply-btn {
  padding: 6px 12px;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  font-size: 12px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.quick-reply-btn:hover {
  background: #e2e8f0;
  border-color: #10b981;
  color: #10b981;
}

.chat-completed {
  padding: 20px;
  border-top: 1px solid #f1f5f9;
  background: #f8fafc;
}

.completed-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #16a34a;
  font-weight: 600;
}

.completed-icon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .doctor-consultation {
    padding: 16px;
  }

  .stats-summary {
    flex-direction: column;
    gap: 16px;
  }

  .consultation-cards {
    grid-template-columns: 1fr;
  }

  .list-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filter-tabs {
    justify-content: center;
  }

  .dialog-overlay {
    padding: 10px;
  }

  .chat-dialog {
    height: 90vh;
  }

  .message-content {
    max-width: 85%;
  }

  .quick-replies {
    flex-direction: column;
    align-items: flex-start;
  }

  .card-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .chat-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .chat-actions {
    justify-content: space-between;
  }

  .input-container {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
