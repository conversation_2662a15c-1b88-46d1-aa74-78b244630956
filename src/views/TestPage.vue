<template>
  <div class="test-page">
    <div class="page-header">
      <h1>功能测试页面</h1>
      <p>测试新开发的医生统计分析和患者预约功能</p>
    </div>

    <div class="test-sections">
      <!-- 医生功能测试 -->
      <div class="test-section">
        <h2>医生功能</h2>
        <div class="test-buttons">
          <router-link to="/doctor/statistics" class="test-btn doctor-btn">
            📊 医生统计分析
          </router-link>
          <router-link to="/doctor/appointments" class="test-btn doctor-btn">
            👥 医生预约管理
          </router-link>
          <router-link to="/doctor" class="test-btn doctor-btn">
            🏥 医生仪表板
          </router-link>
        </div>
      </div>

      <!-- 患者功能测试 -->
      <div class="test-section">
        <h2>患者功能</h2>
        <div class="test-buttons">
          <router-link to="/booking" class="test-btn patient-btn">
            📅 预约挂号
          </router-link>
          <router-link to="/appointments" class="test-btn patient-btn">
            📋 我的预约
          </router-link>
          <router-link to="/" class="test-btn patient-btn">
            🏠 患者仪表板
          </router-link>
        </div>
      </div>

      <!-- 组件测试 -->
      <div class="test-section">
        <h2>组件测试</h2>
        <div class="component-tests">
          <!-- 统计卡片测试 -->
          <div class="component-demo">
            <h3>统计卡片组件</h3>
            <div class="stats-demo">
              <DoctorStatsCard
                title="测试预约数"
                :value="156"
                :change="12.5"
                icon="📅"
                type="primary"
                format="number"
              />
              <DoctorStatsCard
                title="测试收入"
                :value="45600"
                :change="-5.2"
                icon="💰"
                type="success"
                format="currency"
              />
              <DoctorStatsCard
                title="测试满意度"
                :value="94.5"
                :change="2.1"
                icon="⭐"
                type="warning"
                format="percentage"
              />
            </div>
          </div>

          <!-- 患者健康图表测试 -->
          <div class="component-demo">
            <h3>患者健康图表组件</h3>
            <div class="chart-demo">
              <button @click="showHealthChart = !showHealthChart" class="demo-btn">
                {{ showHealthChart ? '隐藏' : '显示' }}健康图表
              </button>
              <div v-if="showHealthChart" class="chart-container">
                <PatientHealthChart 
                  :patient-id="1"
                  :date-range="{ start: '2024-01-01', end: '2024-12-31' }"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- API测试 -->
      <div class="test-section">
        <h2>API测试</h2>
        <div class="api-tests">
          <div class="api-group">
            <h3>医生统计API</h3>
            <div class="api-buttons">
              <button @click="testDoctorStats" class="api-btn" :disabled="loading">
                测试医生统计
              </button>
              <button @click="testPatientHealthData" class="api-btn" :disabled="loading">
                测试患者健康数据
              </button>
              <button @click="testTreatmentReport" class="api-btn" :disabled="loading">
                测试诊疗报告
              </button>
            </div>
          </div>

          <div class="api-group">
            <h3>患者预约API</h3>
            <div class="api-buttons">
              <button @click="testDepartments" class="api-btn" :disabled="loading">
                测试科室列表
              </button>
              <button @click="testDoctors" class="api-btn" :disabled="loading">
                测试医生搜索
              </button>
              <button @click="testAppointments" class="api-btn" :disabled="loading">
                测试我的预约
              </button>
            </div>
          </div>
        </div>

        <!-- API测试结果 -->
        <div v-if="apiResult" class="api-result">
          <h3>测试结果</h3>
          <div class="result-content">
            <div class="result-status" :class="apiResult.success ? 'success' : 'error'">
              {{ apiResult.success ? '✅ 成功' : '❌ 失败' }}
            </div>
            <div class="result-message">{{ apiResult.message }}</div>
            <div v-if="apiResult.data" class="result-data">
              <pre>{{ JSON.stringify(apiResult.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户信息 -->
      <div class="test-section">
        <h2>当前用户信息</h2>
        <div class="user-info">
          <div class="info-item">
            <label>用户ID:</label>
            <span>{{ userStore.userInfo?.id || '未登录' }}</span>
          </div>
          <div class="info-item">
            <label>用户角色:</label>
            <span>{{ userStore.userInfo?.role || '未知' }}</span>
          </div>
          <div class="info-item">
            <label>昵称:</label>
            <span>{{ userStore.userInfo?.nickname || '未设置' }}</span>
          </div>
          <div class="info-item">
            <label>手机号:</label>
            <span>{{ userStore.userInfo?.phoneNumber || '未设置' }}</span>
          </div>
          <div class="info-item">
            <label>登录状态:</label>
            <span :class="userStore.isLoggedIn ? 'status-online' : 'status-offline'">
              {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useUserStore } from '@/stores/user'
import DoctorStatsCard from '@/components/DoctorStatsCard.vue'
import PatientHealthChart from '@/components/PatientHealthChart.vue'
import * as doctorStatsApi from '@/api/doctorStats'
import * as appointmentsApi from '@/api/appointments'

export default {
  name: 'TestPage',
  components: {
    DoctorStatsCard,
    PatientHealthChart
  },
  setup() {
    const userStore = useUserStore()
    
    // 响应式数据
    const loading = ref(false)
    const showHealthChart = ref(false)
    const apiResult = ref(null)

    // API测试方法
    const testDoctorStats = async () => {
      loading.value = true
      apiResult.value = null
      
      try {
        const response = await doctorStatsApi.getDoctorStatistics()
        apiResult.value = {
          success: true,
          message: '医生统计API调用成功',
          data: response.data
        }
      } catch (error) {
        apiResult.value = {
          success: false,
          message: `医生统计API调用失败: ${error.message}`,
          data: error.response?.data
        }
      } finally {
        loading.value = false
      }
    }

    const testPatientHealthData = async () => {
      loading.value = true
      apiResult.value = null
      
      try {
        const response = await doctorStatsApi.getPatientHealthData(1)
        apiResult.value = {
          success: true,
          message: '患者健康数据API调用成功',
          data: response.data
        }
      } catch (error) {
        apiResult.value = {
          success: false,
          message: `患者健康数据API调用失败: ${error.message}`,
          data: error.response?.data
        }
      } finally {
        loading.value = false
      }
    }

    const testTreatmentReport = async () => {
      loading.value = true
      apiResult.value = null
      
      try {
        const response = await doctorStatsApi.generateTreatmentReport()
        apiResult.value = {
          success: true,
          message: '诊疗报告API调用成功',
          data: response.data
        }
      } catch (error) {
        apiResult.value = {
          success: false,
          message: `诊疗报告API调用失败: ${error.message}`,
          data: error.response?.data
        }
      } finally {
        loading.value = false
      }
    }

    const testDepartments = async () => {
      loading.value = true
      apiResult.value = null
      
      try {
        const response = await appointmentsApi.getDepartments()
        apiResult.value = {
          success: true,
          message: '科室列表API调用成功',
          data: response.data
        }
      } catch (error) {
        apiResult.value = {
          success: false,
          message: `科室列表API调用失败: ${error.message}`,
          data: error.response?.data
        }
      } finally {
        loading.value = false
      }
    }

    const testDoctors = async () => {
      loading.value = true
      apiResult.value = null
      
      try {
        const response = await appointmentsApi.searchDoctors({ name: '王' })
        apiResult.value = {
          success: true,
          message: '医生搜索API调用成功',
          data: response.data
        }
      } catch (error) {
        apiResult.value = {
          success: false,
          message: `医生搜索API调用失败: ${error.message}`,
          data: error.response?.data
        }
      } finally {
        loading.value = false
      }
    }

    const testAppointments = async () => {
      loading.value = true
      apiResult.value = null
      
      try {
        const response = await appointmentsApi.getMyAppointments()
        apiResult.value = {
          success: true,
          message: '我的预约API调用成功',
          data: response.data
        }
      } catch (error) {
        apiResult.value = {
          success: false,
          message: `我的预约API调用失败: ${error.message}`,
          data: error.response?.data
        }
      } finally {
        loading.value = false
      }
    }

    return {
      userStore,
      loading,
      showHealthChart,
      apiResult,
      testDoctorStats,
      testPatientHealthData,
      testTreatmentReport,
      testDepartments,
      testDoctors,
      testAppointments
    }
  }
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 32px;
}

.page-header p {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.test-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.test-section h2 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 24px;
  border-bottom: 2px solid #4A90E2;
  padding-bottom: 10px;
}

.test-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.doctor-btn {
  background: linear-gradient(135deg, #4A90E2, #357abd);
  color: white;
}

.doctor-btn:hover {
  background: linear-gradient(135deg, #357abd, #2c5aa0);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.patient-btn {
  background: linear-gradient(135deg, #28a745, #218838);
  color: white;
}

.patient-btn:hover {
  background: linear-gradient(135deg, #218838, #1e7e34);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.component-tests {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.component-demo h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
}

.stats-demo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.chart-demo {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.demo-btn {
  padding: 10px 20px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  align-self: flex-start;
}

.demo-btn:hover {
  background: #5a6268;
}

.chart-container {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  background: #f8f9fa;
}

.api-tests {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.api-group h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
}

.api-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.api-btn {
  padding: 8px 16px;
  background: #17a2b8;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.api-btn:hover:not(:disabled) {
  background: #138496;
}

.api-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.api-result {
  margin-top: 25px;
  padding: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: #f8f9fa;
}

.api-result h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
}

.result-status {
  font-weight: bold;
  margin-bottom: 10px;
}

.result-status.success {
  color: #28a745;
}

.result-status.error {
  color: #dc3545;
}

.result-message {
  margin-bottom: 15px;
  color: #495057;
}

.result-data {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 15px;
  overflow-x: auto;
}

.result-data pre {
  margin: 0;
  font-size: 12px;
  color: #495057;
}

.user-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.info-item label {
  font-weight: 500;
  color: #495057;
}

.info-item span {
  color: #2c3e50;
}

.status-online {
  color: #28a745 !important;
  font-weight: 500;
}

.status-offline {
  color: #dc3545 !important;
  font-weight: 500;
}

@media (max-width: 768px) {
  .test-buttons {
    flex-direction: column;
  }
  
  .stats-demo {
    grid-template-columns: 1fr;
  }
  
  .api-buttons {
    flex-direction: column;
  }
  
  .user-info {
    grid-template-columns: 1fr;
  }
}
</style>
