<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1>社区健康管理系统</h1>
        <p>用户登录</p>
      </div>

      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label for="phone">手机号</label>
          <input
            id="phone"
            v-model="loginForm.phone_number"
            type="tel"
            placeholder="请输入手机号"
            :class="{ 'error': errors.phone_number }"
            @blur="validatePhone"
          />
          <span v-if="errors.phone_number" class="error-message">
            {{ errors.phone_number }}
          </span>
        </div>

        <div class="form-group">
          <label for="password">密码</label>
          <input
            id="password"
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            :class="{ 'error': errors.password }"
            @blur="validatePassword"
          />
          <span v-if="errors.password" class="error-message">
            {{ errors.password }}
          </span>
        </div>

        <div class="button-group">
          <button
            type="submit"
            class="login-btn"
            :disabled="isLoading || !isFormValid"
          >
            {{ isLoading ? '登录中...' : '登录' }}
          </button>

          <router-link to="/register" class="register-btn">
            注册
          </router-link>
        </div>
      </form>

      <!-- 错误提示 -->
      <div v-if="loginError" class="error-alert">
        {{ loginError }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 表单数据
const loginForm = ref({
  phone_number: '',
  password: ''
})

// 表单验证错误
const errors = ref({
  phone_number: '',
  password: ''
})

// 登录错误信息
const loginError = ref('')

// 加载状态
const isLoading = computed(() => userStore.isLoading)

// 表单验证
const validatePhone = () => {
  const phone = loginForm.value.phone_number.trim()
  if (!phone) {
    errors.value.phone_number = '请输入手机号'
    return false
  }
  if (!/^1[3-9]\d{9}$/.test(phone)) {
    errors.value.phone_number = '请输入正确的手机号格式'
    return false
  }
  errors.value.phone_number = ''
  return true
}

const validatePassword = () => {
  const password = loginForm.value.password
  if (!password) {
    errors.value.password = '请输入密码'
    return false
  }
  if (password.length < 6) {
    errors.value.password = '密码长度不能少于6位'
    return false
  }
  errors.value.password = ''
  return true
}

// 表单是否有效
const isFormValid = computed(() => {
  return loginForm.value.phone_number &&
         loginForm.value.password &&
         !errors.value.phone_number &&
         !errors.value.password
})

// 处理登录
const handleLogin = async () => {
  // 清除之前的错误信息
  loginError.value = ''

  // 验证表单
  const isPhoneValid = validatePhone()
  const isPasswordValid = validatePassword()

  if (!isPhoneValid || !isPasswordValid) {
    return
  }

  try {
    const result = await userStore.login(loginForm.value)

    if (result.success) {
      let userInfo = result.data.userInfo;
      console.log(userInfo)
      let redirectPath = '/'

      // 使用正确的计算属性进行角色判断
      if (userInfo.isAdmin) {
        redirectPath = '/admin' // 管理员跳转到管理端
        console.log('跳转到管理员页面')
      } else if (userInfo.isDoctor) {
        redirectPath = '/doctor' // 医生跳转到医生端
        console.log('跳转到医生页面')
      } else {
        redirectPath = '/' // 居民用户跳转到居民端
        console.log('跳转到居民页面')
      }

      // 如果有指定的重定向路径，优先使用
      const queryRedirect = router.currentRoute.value.query.redirect
      if (queryRedirect) {
        redirectPath = queryRedirect
        console.log('使用重定向路径:', redirectPath)
      }

      console.log('最终跳转路径:', redirectPath)
      router.push(redirectPath)
    } else {
      loginError.value = result.message
    }
  } catch (error) {
    loginError.value = '登录失败，请稍后重试'
    console.error('登录错误:', error)
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  margin: 0;
  position: fixed;
  top: 0;
  left: 0;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  padding: 50px 60px;
  width: 90%;
  max-width: 450px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin: 20px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-header h1 {
  color: #1565c0;
  font-size: 32px;
  margin-bottom: 12px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.login-header p {
  color: #546e7a;
  font-size: 18px;
  margin: 0;
  font-weight: 400;
}

.login-form {
  width: 100%;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 10px;
  color: #1565c0;
  font-weight: 600;
  font-size: 15px;
}

.form-group input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e3f2fd;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.8);
}

.form-group input:focus {
  outline: none;
  border-color: #42a5f5;
  background: white;
  box-shadow: 0 0 0 3px rgba(66, 165, 245, 0.1);
}

.form-group input.error {
  border-color: #f44336;
  background: rgba(244, 67, 54, 0.05);
}

.error-message {
  color: #f44336;
  font-size: 14px;
  margin-top: 8px;
  display: block;
  font-weight: 500;
}

.button-group {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.login-btn {
  flex: 1;
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.register-btn {
  flex: 1;
  padding: 16px 24px;
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}

.register-btn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.error-alert {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  color: #c62828;
  padding: 16px;
  border-radius: 12px;
  margin-top: 25px;
  text-align: center;
  border: 1px solid #ffcdd2;
  font-weight: 500;
}


</style>
