/* 医生个人信息管理页面专用样式 */

/* 全局变量 */
:root {
  --doctor-primary: #3b82f6;
  --doctor-primary-dark: #1d4ed8;
  --doctor-secondary: #1e40af;
  --doctor-success: #10b981;
  --doctor-warning: #f59e0b;
  --doctor-danger: #ef4444;
  --doctor-light-blue: #f0f9ff;
  --doctor-light-gray: #f8fafc;
  --doctor-border: #e5e7eb;
  --doctor-text-primary: #1f2937;
  --doctor-text-secondary: #6b7280;
  --doctor-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --doctor-shadow-hover: 0 4px 20px rgba(59, 130, 246, 0.15);
  --doctor-radius: 12px;
  --doctor-radius-small: 8px;
  --doctor-transition: all 0.2s ease;
}

/* 页面布局 */
.doctor-profile-page {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--doctor-light-blue) 0%, #e0f2fe 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> U<PERSON>', <PERSON><PERSON>, sans-serif;
}

/* 页面头部 */
.page-header {
  background: white;
  border-radius: var(--doctor-radius);
  box-shadow: var(--doctor-shadow);
  margin-bottom: 24px;
  padding: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h1 {
  margin: 0 0 8px 0;
  color: var(--doctor-secondary);
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--doctor-primary), var(--doctor-primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-subtitle {
  margin: 0;
  color: var(--doctor-text-secondary);
  font-size: 16px;
}

/* 按钮样式 */
.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(135deg, var(--doctor-primary), var(--doctor-primary-dark));
  color: white;
  border: none;
  border-radius: var(--doctor-radius-small);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: var(--doctor-transition);
}

.refresh-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: var(--doctor-shadow-hover);
}

.refresh-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

/* 主要内容区域 */
.main-content {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 24px;
  align-items: start;
}

/* 信息卡片 */
.info-card {
  background: white;
  border-radius: var(--doctor-radius);
  box-shadow: var(--doctor-shadow);
  overflow: hidden;
  margin-bottom: 20px;
  transition: var(--doctor-transition);
}

.info-card:hover {
  box-shadow: var(--doctor-shadow-hover);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, var(--doctor-light-gray), #e2e8f0);
  border-bottom: 1px solid var(--doctor-border);
}

.card-header h3 {
  margin: 0;
  color: var(--doctor-secondary);
  font-size: 16px;
  font-weight: 600;
}

/* 状态徽章 */
.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-approved {
  background: #dcfce7;
  color: #166534;
}

.status-pending {
  background: #fef3c7;
  color: #92400e;
}

.status-rejected {
  background: #fee2e2;
  color: #991b1b;
}

/* 医生头像区域 */
.doctor-avatar-section {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px;
  border-bottom: 1px solid #f3f4f6;
}

.doctor-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--doctor-primary), var(--doctor-primary-dark));
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  font-weight: 600;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.doctor-basic-info h4 {
  margin: 0 0 4px 0;
  color: var(--doctor-text-primary);
  font-size: 20px;
  font-weight: 600;
}

.doctor-basic-info p {
  margin: 2px 0;
  color: var(--doctor-text-secondary);
  font-size: 14px;
}

.nickname {
  color: var(--doctor-primary) !important;
  font-weight: 500;
}

/* 信息详情 */
.info-details {
  padding: 20px 24px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f9fafb;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item.full-width {
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.info-item .label {
  color: var(--doctor-text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.info-item .value {
  color: var(--doctor-text-primary);
  font-size: 14px;
}

.info-item .value.bio {
  line-height: 1.5;
  text-align: left;
}

/* 卡片操作按钮 */
.card-actions {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  background: var(--doctor-light-gray);
}

.edit-btn,
.password-btn {
  flex: 1;
  padding: 10px 16px;
  border-radius: var(--doctor-radius-small);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--doctor-transition);
}

.edit-btn {
  background: linear-gradient(135deg, var(--doctor-primary), var(--doctor-primary-dark));
  color: white;
  border: none;
}

.edit-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: var(--doctor-shadow-hover);
}

.password-btn {
  background: white;
  color: #374151;
  border: 2px solid var(--doctor-border);
}

.password-btn:hover {
  background: var(--doctor-light-gray);
  border-color: #d1d5db;
}

/* 快捷操作网格 */
.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 24px;
}

.action-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border: 2px solid var(--doctor-border);
  border-radius: var(--doctor-radius);
  cursor: pointer;
  transition: var(--doctor-transition);
  text-align: left;
}

.action-card:hover {
  border-color: var(--doctor-primary);
  background: var(--doctor-light-blue);
  transform: translateY(-2px);
  box-shadow: var(--doctor-shadow-hover);
}

.action-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.action-content h4 {
  margin: 0 0 4px 0;
  color: var(--doctor-text-primary);
  font-size: 16px;
  font-weight: 600;
}

.action-content p {
  margin: 0;
  color: var(--doctor-text-secondary);
  font-size: 14px;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 24px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: linear-gradient(135deg, var(--doctor-light-blue), #e0f2fe);
  border-radius: var(--doctor-radius);
  transition: var(--doctor-transition);
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--doctor-shadow-hover);
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: var(--doctor-secondary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--doctor-text-secondary);
  font-weight: 500;
}

/* 成功消息 */
.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.message-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: #dcfce7;
  color: #166534;
  border-radius: var(--doctor-radius-small);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-weight: 500;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 旋转动画 */
.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .doctor-profile-page {
    padding: 12px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .doctor-avatar-section {
    flex-direction: column;
    text-align: center;
  }
  
  .card-actions {
    flex-direction: column;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
}

/* 打印样式 */
@media print {
  .doctor-profile-page {
    background: white;
    padding: 0;
  }
  
  .page-header,
  .info-card {
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
  
  .card-actions,
  .quick-actions-grid {
    display: none;
  }
}
