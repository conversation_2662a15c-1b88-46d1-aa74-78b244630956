import api from './index'

/**
 * 医生统计分析API接口
 */

/**
 * 获取医生KPI指标数据
 * @param {Object} params - 查询参数
 * @param {string} params.range - 时间范围 (week/month/quarter)
 * @returns {Promise} KPI数据响应
 */
export const getDoctorKpi = (params = {}) => {
  return api.get('/doctor/statistics/kpi', { params })
}

/**
 * 获取服务量趋势数据
 * @param {Object} params - 查询参数
 * @param {string} params.range - 时间范围 (week/month/quarter)
 * @returns {Promise} 服务量趋势数据响应
 */
export const getServiceTrend = (params = {}) => {
  return api.get('/doctor/statistics/service-trend', { params })
}

/**
 * 获取预约状态分配数据
 * @param {Object} params - 查询参数
 * @param {string} params.range - 时间范围 (week/month/quarter)
 * @returns {Promise} 预约状态分配数据响应
 */
export const getAppointmentStatus = (params = {}) => {
  return api.get('/doctor/statistics/appointment-status', { params })
}

/**
 * 获取高频服务患者排行
 * @param {Object} params - 查询参数
 * @param {string} params.range - 时间范围 (week/month/quarter)
 * @returns {Promise} 患者排行数据响应
 */
export const getTopPatients = (params = {}) => {
  return api.get('/doctor/statistics/top-patients', { params })
}

/**
 * 获取预约时间段分析数据
 * @param {Object} params - 查询参数
 * @param {string} params.range - 时间范围 (week/month/quarter)
 * @returns {Promise} 时间段分析数据响应
 */
export const getScheduleHotness = (params = {}) => {
  return api.get('/doctor/statistics/schedule-hotness', { params })
}

/**
 * 导出统计报告
 * @param {Object} params - 查询参数
 * @param {string} params.range - 时间范围 (week/month/quarter)
 * @returns {Promise} 导出响应
 */
export const exportStatisticsReport = (params = {}) => {
  return api.get('/doctor/statistics/export', { 
    params,
    responseType: 'blob' // 用于文件下载
  })
}
