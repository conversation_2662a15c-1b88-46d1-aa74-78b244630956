import api from './index'

/**
 * 健康档案管理API
 */

/**
 * 创建健康档案
 * @param {Object} profileData - 档案数据
 * @param {string} profileData.profileOwnerName - 档案所有者姓名
 * @param {string} profileData.gender - 性别 (MALE/FEMALE/OTHER)
 * @param {string} profileData.birthDate - 出生日期 (YYYY-MM-DD)
 * @param {string} profileData.idCard - 身份证号
 * @param {string} profileData.avatarUrl - 头像链接 (可选)
 * @param {string} profileData.medicalHistory - 病史信息 (可选)
 * @returns {Promise} 创建响应
 */
export const createHealthProfile = (profileData) => {
  return api.post('/health/profiles', profileData)
}

/**
 * 获取健康档案列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码 (默认1)
 * @param {number} params.size - 每页数量 (默认10)
 * @returns {Promise} 档案列表响应
 */
export const getHealthProfiles = (params = {}) => {
  return api.get('/health/profiles', { params })
}

/**
 * 获取健康档案详情
 * @param {number} profileId - 档案ID
 * @returns {Promise} 档案详情响应
 */
export const getHealthProfile = (profileId) => {
  return api.get(`/health/profiles/${profileId}`)
}

/**
 * 更新健康档案
 * @param {number} profileId - 档案ID
 * @param {Object} profileData - 更新数据
 * @returns {Promise} 更新响应
 */
export const updateHealthProfile = (profileId, profileData) => {
  return api.put(`/health/profiles/${profileId}`, profileData)
}

/**
 * 删除健康档案
 * @param {number} profileId - 档案ID
 * @returns {Promise} 删除响应
 */
export const deleteHealthProfile = (profileId) => {
  return api.delete(`/health/profiles/${profileId}`)
}

/**
 * 上传头像
 * @param {File} file - 头像文件
 * @returns {Promise} 上传响应
 */
export const uploadAvatar = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return api.post('/health/profiles/upload-avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 验证身份证号是否可用
 * @param {string} idCard - 身份证号
 * @returns {Promise} 验证响应
 */
export const validateIdCard = (idCard) => {
  return api.post('/health/profiles/validate-id-card', { idCard })
}
