import api from './index'

// 开具电子处方（医生）
export const createPrescription = (data) => {
  return api.post('/prescriptions', data)
}

// 获取患者的处方列表
export const getPatientPrescriptions = (profileId, params) => {
  return api.get(`/profiles/${profileId}/prescriptions`, { params })
}

// 获取医生开具的处方列表
export const getDoctorPrescriptions = (params) => {
  return api.get('/prescriptions/doctor', { params })
}

// 获取单个处方详情
export const getPrescriptionDetail = (prescriptionId) => {
  return api.get(`/prescriptions/${prescriptionId}`)
}
