import api from './index'

// 创建问诊会话（居民）
export const createConsultation = (data) => {
  return api.post('/consultations', data)
}

// 获取问诊列表
export const getConsultations = (params) => {
  return api.get('/consultations', { params })
}

// 发送消息
export const sendMessage = (consultationId, data) => {
  return api.post(`/consultations/${consultationId}/messages`, data)
}

// 获取消息历史记录
export const getMessages = (consultationId, params) => {
  return api.get(`/consultations/${consultationId}/messages`, { params })
}

// 完成问诊（医生）
export const completeConsultation = (consultationId) => {
  return api.put(`/consultations/${consultationId}/complete`)
}

// 获取医生列表（用于发起问诊）
export const getDoctorList = (params) => {
  return api.get('/doctors', { params })
}
