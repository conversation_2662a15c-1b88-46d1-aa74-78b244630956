import api from './index'

/**
 * 获取医生的排班列表
 * @returns {Promise} 排班列表响应
 */
export const getDoctorSchedules = () => {
  return api.get('/doctor/schedules/my')
}

/**
 * 创建新的排班
 * @param {Object} scheduleData - 排班数据
 * @param {string} scheduleData.scheduleDate - 排班日期 (YYYY-MM-DD)
 * @param {string} scheduleData.startTime - 开始时间 (HH:mm:ss)
 * @param {string} scheduleData.endTime - 结束时间 (HH:mm:ss)
 * @param {number} scheduleData.totalSlots - 总号源数量
 * @returns {Promise} 创建排班响应
 */
export const createSchedule = (scheduleData) => {
  return api.post('/doctor/schedules', scheduleData)
}

/**
 * 更新排班信息
 * @param {number} scheduleId - 排班ID
 * @param {Object} scheduleData - 排班数据
 * @param {string} scheduleData.scheduleDate - 排班日期 (YYYY-MM-DD)
 * @param {string} scheduleData.startTime - 开始时间 (HH:mm:ss)
 * @param {string} scheduleData.endTime - 结束时间 (HH:mm:ss)
 * @param {number} scheduleData.totalSlots - 总号源数量
 * @returns {Promise} 更新排班响应
 */
export const updateSchedule = (scheduleId, scheduleData) => {
  return api.put(`/doctor/schedules/${scheduleId}`, scheduleData)
}

/**
 * 删除排班
 * @param {number} scheduleId - 排班ID
 * @returns {Promise} 删除排班响应
 */
export const deleteSchedule = (scheduleId) => {
  return api.delete(`/doctor/schedules/${scheduleId}`)
}

/**
 * 获取排班统计信息
 * @param {string} startDate - 开始日期 (可选)
 * @param {string} endDate - 结束日期 (可选)
 * @returns {Promise} 统计信息响应
 */
export const getScheduleStats = (startDate, endDate) => {
  const params = {}
  if (startDate) params.startDate = startDate
  if (endDate) params.endDate = endDate
  
  return api.get('/doctor/schedules/stats', { params })
}

/**
 * 批量创建排班（周期性排班）
 * @param {Object} batchData - 批量排班数据
 * @param {string} batchData.startDate - 开始日期
 * @param {string} batchData.endDate - 结束日期
 * @param {Array} batchData.weekdays - 星期几 [1,2,3,4,5] (1=周一)
 * @param {string} batchData.startTime - 开始时间
 * @param {string} batchData.endTime - 结束时间
 * @param {number} batchData.totalSlots - 总号源数量
 * @returns {Promise} 批量创建响应
 */
export const createBatchSchedules = (batchData) => {
  return api.post('/doctor/schedules/batch', batchData)
}

/**
 * 复制排班到其他日期
 * @param {number} sourceScheduleId - 源排班ID
 * @param {Array} targetDates - 目标日期数组
 * @returns {Promise} 复制排班响应
 */
export const copySchedule = (sourceScheduleId, targetDates) => {
  return api.post(`/doctor/schedules/${sourceScheduleId}/copy`, {
    targetDates
  })
}

/**
 * 获取指定日期范围的排班
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 * @returns {Promise} 排班列表响应
 */
export const getSchedulesByDateRange = (startDate, endDate) => {
  return api.get('/doctor/schedules/my', {
    params: {
      startDate,
      endDate
    }
  })
}

/**
 * 检查排班时间冲突
 * @param {Object} scheduleData - 排班数据
 * @param {number} excludeId - 排除的排班ID（编辑时使用）
 * @returns {Promise} 冲突检查响应
 */
export const checkScheduleConflict = (scheduleData, excludeId = null) => {
  const params = { ...scheduleData }
  if (excludeId) params.excludeId = excludeId
  
  return api.post('/doctor/schedules/check-conflict', params)
}

/**
 * 获取排班模板
 * @returns {Promise} 排班模板响应
 */
export const getScheduleTemplates = () => {
  return api.get('/doctor/schedules/templates')
}

/**
 * 保存排班模板
 * @param {Object} templateData - 模板数据
 * @param {string} templateData.name - 模板名称
 * @param {string} templateData.startTime - 开始时间
 * @param {string} templateData.endTime - 结束时间
 * @param {number} templateData.totalSlots - 总号源数量
 * @returns {Promise} 保存模板响应
 */
export const saveScheduleTemplate = (templateData) => {
  return api.post('/doctor/schedules/templates', templateData)
}

/**
 * 删除排班模板
 * @param {number} templateId - 模板ID
 * @returns {Promise} 删除模板响应
 */
export const deleteScheduleTemplate = (templateId) => {
  return api.delete(`/doctor/schedules/templates/${templateId}`)
}
