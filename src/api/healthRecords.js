import api from './index'

/**
 * 健康数据记录管理API
 */

/**
 * 添加健康数据记录
 * @param {Object} recordData - 记录数据
 * @param {number} recordData.profileId - 档案ID
 * @param {string} recordData.metricType - 指标类型 (blood_pressure/blood_sugar/weight/heart_rate/temperature)
 * @param {number} recordData.metricValue - 指标数值
 * @param {number} recordData.systolicPressure - 收缩压 (仅血压类型)
 * @param {number} recordData.diastolicPressure - 舒张压 (仅血压类型)
 * @param {string} recordData.unit - 单位
 * @param {string} recordData.notes - 备注 (可选)
 * @returns {Promise} 创建响应
 */
export const addHealthRecord = (recordData) => {
  return api.post('/health/records', recordData)
}

/**
 * 获取健康数据记录列表
 * @param {Object} params - 查询参数
 * @param {number} params.profileId - 档案ID (必填)
 * @param {string} params.metricType - 指标类型 (可选)
 * @param {string} params.startDate - 开始日期 (可选)
 * @param {string} params.endDate - 结束日期 (可选)
 * @param {number} params.page - 页码 (默认1)
 * @param {number} params.size - 每页数量 (默认10)
 * @returns {Promise} 记录列表响应
 */
export const getHealthRecords = (params) => {
  return api.get('/health/records', { params })
}

/**
 * 获取健康数据统计分析
 * @param {Object} params - 查询参数
 * @param {number} params.profileId - 档案ID (必填)
 * @param {string} params.metricType - 指标类型 (必填)
 * @param {string} params.period - 统计周期 (week/month/year)
 * @returns {Promise} 统计数据响应
 */
export const getHealthStatistics = (params) => {
  return api.get('/health/records/statistics', { params })
}

/**
 * 更新健康数据记录
 * @param {number} recordId - 记录ID
 * @param {Object} recordData - 更新数据
 * @returns {Promise} 更新响应
 */
export const updateHealthRecord = (recordId, recordData) => {
  return api.put(`/health/records/${recordId}`, recordData)
}

/**
 * 删除健康数据记录
 * @param {number} recordId - 记录ID
 * @returns {Promise} 删除响应
 */
export const deleteHealthRecord = (recordId) => {
  return api.delete(`/health/records/${recordId}`)
}

/**
 * 健康指标类型配置
 */
export const METRIC_TYPES = {
  blood_pressure: {
    label: '血压',
    icon: '🩸',
    unit: 'mmHg',
    hasExtendedFields: true,
    fields: ['systolicPressure', 'diastolicPressure'],
    format: (record) => `${record.systolicPressure}/${record.diastolicPressure} ${record.unit}`
  },
  blood_sugar: {
    label: '血糖',
    icon: '🍯',
    unit: 'mmol/L',
    hasExtendedFields: false,
    format: (record) => `${record.metricValue} ${record.unit}`
  },
  weight: {
    label: '体重',
    icon: '⚖️',
    unit: 'kg',
    hasExtendedFields: false,
    format: (record) => `${record.metricValue} ${record.unit}`
  },
  heart_rate: {
    label: '心率',
    icon: '💓',
    unit: 'bpm',
    hasExtendedFields: false,
    format: (record) => `${record.metricValue} ${record.unit}`
  },
  temperature: {
    label: '体温',
    icon: '🌡️',
    unit: '°C',
    hasExtendedFields: false,
    format: (record) => `${record.metricValue} ${record.unit}`
  }
}

/**
 * 获取指标类型配置
 * @param {string} metricType - 指标类型
 * @returns {Object} 配置对象
 */
export const getMetricConfig = (metricType) => {
  return METRIC_TYPES[metricType] || {
    label: '未知',
    icon: '❓',
    unit: '',
    hasExtendedFields: false,
    format: (record) => `${record.metricValue}`
  }
}

/**
 * 格式化记录显示值
 * @param {Object} record - 记录对象
 * @returns {string} 格式化后的显示值
 */
export const formatRecordValue = (record) => {
  const config = getMetricConfig(record.metricType)
  return config.format(record)
}
