import api from './index'

/**
 * 健康提醒管理API
 */

// 获取健康提醒列表
export const getHealthReminders = (params = {}) => {
  return api.get('/health/reminders', { params })
}

// 获取今日提醒
export const getTodayReminders = () => {
  return api.get('/health/reminders/today')
}

// 创建健康提醒
export const createHealthReminder = (data) => {
  return api.post('/health/reminders', data)
}

// 更新健康提醒
export const updateHealthReminder = (id, data) => {
  return api.put(`/health/reminders/${id}`, data)
}

// 删除健康提醒
export const deleteHealthReminder = (id) => {
  return api.delete(`/health/reminders/${id}`)
}

// 标记提醒完成
export const completeHealthReminder = (id) => {
  return api.post(`/health/reminders/${id}/complete`)
}
